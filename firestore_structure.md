# Firestore Database Structure for BillMate

## Collections and Documents

### Collection: `bills`
Documents in this collection represent individual bills or obligations.

**Document ID**: Auto-generated

**Fields**:
- `id`: String - Document ID (same as Firestore document ID)
- `title`: String - Title or name of the bill
- `amount`: Double - Amount due
- `description`: String - Additional details about the bill
- `dueDate`: Timestamp - When the bill is due
- `paidDate`: Timestamp (optional) - When the bill was paid
- `isPaid`: Boolean - Whether the bill has been paid
- `category`: String - "PERSONAL" or "BUSINESS"
- `tags`: Array<String> - Custom tags for the bill
- `imageUrl`: String (optional) - URL to the bill image in Firebase Storage
- `userId`: String - ID of the user who owns this bill
- `createdAt`: Timestamp - When the bill was created
- `updatedAt`: Timestamp - When the bill was last updated

### Collection: `users`
Documents in this collection represent user profiles.

**Document ID**: User's UID from Firebase Authentication

**Fields**:
- `displayName`: String - User's display name
- `email`: String - User's email address
- `photoUrl`: String (optional) - URL to the user's profile picture
- `createdAt`: Timestamp - When the user account was created
- `lastLogin`: Timestamp - When the user last logged in
- `preferences`: Map - User preferences
  - `darkMode`: Boolean - Whether dark mode is enabled
  - `notificationsEnabled`: Boolean - Whether notifications are enabled
  - `language`: String - User's preferred language

## Security Rules

The security rules ensure that:
- Users can only create bills when authenticated
- Users can only read, update, or delete bills that they own
- Users can only access their own user profile data

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write only their own data
    match /bills/{billId} {
      allow create: if request.auth != null;
      allow read, update, delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // User profiles
    match /users/{userId} {
      allow create: if request.auth != null;
      allow read, update, delete: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Indexes

The following indexes may be needed for efficient queries:

1. Collection: `bills`
   - Fields: `userId` (Ascending), `dueDate` (Ascending)
   - Purpose: List bills by due date

2. Collection: `bills`
   - Fields: `userId` (Ascending), `category` (Ascending), `dueDate` (Ascending)
   - Purpose: List bills by category and due date

3. Collection: `bills`
   - Fields: `userId` (Ascending), `isPaid` (Ascending), `dueDate` (Ascending)
   - Purpose: List unpaid bills by due date

## Storage Structure

### Path: `/bills/{userId}/{billId}`
- Stores bill images uploaded by users
- Each user has their own directory
- Each bill image is stored with the bill's ID

### Path: `/profiles/{userId}`
- Stores user profile pictures
