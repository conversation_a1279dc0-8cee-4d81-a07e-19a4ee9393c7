# 🚀 Extended Development Plan: AI Integration

This plan focuses on adding artificial intelligence features to enhance automation and user experience, specifically:

1. Automatic extraction of bills from user emails
2. AI-powered chatbot for natural language input

These features will be implemented as an upgrade to the core app.

---

## 🤖 Feature 1: Automatic Bill Extraction from Emails

### ✅ **Objectives:**
- Automatically scan user’s email inbox (e.g., Gmail, Outlook)
- Identify emails containing bills/invoices
- Extract relevant data (issuer, amount, due date, invoice number) using NLP and OCR (if PDF/image attachments)
- Automatically create an entry in the app’s database
- Notify the user for confirmation or edit

### 🏗️ **Technical Approach:**
- Use Gmail API or IMAP protocol for email access (with OAuth 2.0 user authorization)
- Filter emails using NLP-based classifier (e.g., pretrained BERT model fine-tuned for “invoice detection”)
- If attachments are PDFs or images:
    - Extract text via Google ML Kit OCR or Tesseract
    - Parse extracted text for structured data using rule-based + ML hybrid extraction
- Send extracted data to Firestore database
- Push notification to user for review/approval

### 📝 **Dependencies:**
- User must authorize email account access
- Email access limited to major providers initially (Gmail, Outlook)

### 💡 **User Benefits:**
- No manual photo upload required for email invoices
- Faster, automated input of digital bills
- Less risk of missing emailed invoices

---

## 💬 Feature 2: AI Chatbot for Bill Entry

### ✅ **Objectives:**
- Allow users to input new bills via a natural language chatbot interface
- Parse user sentences like:
  - "Add a bill for Vodafone, 80 euros, due May 15th"
  - "I need to pay 45 for electricity on June 1"
- Automatically extract:
  - Issuer
  - Amount
  - Due date
- Create structured entry in Firestore

### 🏗️ **Technical Approach:**
- Use Gemini 2.0 flash API for natural language understanding (NLU)
- Define intents and entities:
    - Intent: AddBill
    - Entities: {issuer}, {amount}, {due_date}
- Connect chatbot interface to Android app (e.g., embedded dialog UI or via popup modal)
- Post processed data to Firestore

### 📝 **Optional Enhancements:**
- Voice input integration (speech-to-text feeding into NLU pipeline)
- Multi-language support
- Chatbot can answer app-related questions (FAQ-style support)

### 💡 **User Benefits:**
- Faster, more natural data entry (no form filling)
- Accessibility improvement for users preferring conversational input
- Fun and engaging way to interact with app

---

## 📅 **Proposed Timeline for AI Features:**

| Phase                         | Duration    |
|------------------------------|-------------|
| Email Parsing MVP             | 4-6 weeks   |
| Chatbot MVP                   | 3-4 weeks   |
| Integration & Testing         | 2 weeks     |
| UI Enhancements for AI inputs | 1-2 weeks   |

*Development times depend on complexity of NLP models and API integration.*

---

## 🏗️ **Technical Stack:**

| Component              | Technology                          |
|-----------------------|------------------------------------|
| Email Access           | Gmail API, IMAP                    |
| NLP for Email Parsing  | BERT-based classifier, spaCy       |
| OCR                    | Google ML Kit, Tesseract           |
| Database               | Firebase Firestore                 |
| Chatbot NLU            | Dialogflow / Gemini 2.0 flash API / Rasa     |
| Notifications          | Firebase Cloud Messaging           |

---

## ✨ **Potential Future Enhancements:**
- Auto-categorization of bills using AI clustering
- AI recommendations based on spending trends
- Integration with financial APIs (e.g., Plaid) for matching paid bills with transactions

---

## 📝 Next Steps:

1. Validate user interest in automated email import vs manual
2. Evaluate Gmail API access requirements (privacy, OAuth scopes)
3. Complete Gmail API integration:
   - Create Google Play Developer account ($25 one-time fee)
   - Publish app to Play Store (at least in testing track)
   - Complete Google's OAuth verification process
   - Implement full Gmail API integration with verified app
4. Define NLP pipeline (train own model or use existing APIs)
5. Prototype chatbot conversation flow
6. Create user flow diagrams for both features
7. Start backend integration and permissions handling