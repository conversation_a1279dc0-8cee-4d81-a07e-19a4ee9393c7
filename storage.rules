rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow users to upload and access only their own files
    match /bills/{userId}/{fileName} {
      allow create, update: if request.auth != null && request.auth.uid == userId;
      allow read, delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // User profile images
    match /profiles/{userId} {
      allow create, update: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null;
    }
  }
}
