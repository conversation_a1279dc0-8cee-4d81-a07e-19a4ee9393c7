package com.example.billmate.notifications

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.media.AudioAttributes
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.billmate.R
import com.example.billmate.MainActivity
import com.example.billmate.model.Bill
import java.util.Locale

/**
 * Manages all notification functionality for BillMate app
 */
class BillNotificationManager(private val context: Context) {

    companion object {
        // Notification Channel IDs
        const val CHANNEL_DUE_REMINDERS = "due_reminders"
        const val CHANNEL_OVERDUE_ALERTS = "overdue_alerts"
        const val CHANNEL_SUMMARIES = "summaries"

        // Notification IDs
        const val NOTIFICATION_ID_BASE = 1000
        const val NOTIFICATION_ID_OVERDUE_SUMMARY = 2000
        const val NOTIFICATION_ID_WEEKLY_SUMMARY = 3000

        // Intent Actions
        const val ACTION_MARK_PAID = "si.billmate2.app.MARK_PAID"
        const val ACTION_VIEW_BILL = "si.billmate2.app.VIEW_BILL"
        const val ACTION_SNOOZE = "si.billmate2.app.SNOOZE"

        // Intent Extras
        const val EXTRA_BILL_ID = "bill_id"
    }

    private val notificationManager = NotificationManagerCompat.from(context)
    private val notificationPreferences = NotificationPreferences(context)
    private val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
        vibratorManager.defaultVibrator
    } else {
        @Suppress("DEPRECATION")
        context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
    }

    init {
        createNotificationChannels()
    }

    /**
     * Create notification channels for different types of notifications
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_DUE_REMINDERS,
                    context.getString(R.string.notification_channel_due_reminders),
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = context.getString(R.string.notification_channel_due_reminders_desc)
                    enableVibration(true)
                },

                NotificationChannel(
                    CHANNEL_OVERDUE_ALERTS,
                    context.getString(R.string.notification_channel_overdue_alerts),
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = context.getString(R.string.notification_channel_overdue_alerts_desc)
                    enableVibration(true)
                },

                NotificationChannel(
                    CHANNEL_SUMMARIES,
                    context.getString(R.string.notification_channel_summaries),
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = context.getString(R.string.notification_channel_summaries_desc)
                    enableVibration(false)
                }
            )

            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { channel ->
                systemNotificationManager.createNotificationChannel(channel)
            }
        }
    }

    /**
     * Check if notification permission is granted
     */
    fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
    }

    /**
     * Show a due date reminder notification for a bill
     */
    fun showDueDateReminder(bill: Bill, daysUntilDue: Int) {
        if (!hasNotificationPermission()) return

        val title = when (daysUntilDue) {
            0 -> context.getString(R.string.notification_due_today_title)
            1 -> context.getString(R.string.notification_due_tomorrow_title)
            else -> context.getString(R.string.notification_due_in_days_title, daysUntilDue)
        }

        val content = context.getString(
            R.string.notification_bill_content,
            bill.title,
            formatAmount(bill.amount)
        )

        // Create rich notification with expanded content
        val bigTextStyle = NotificationCompat.BigTextStyle()
            .bigText(createRichNotificationText(bill, daysUntilDue))
            .setBigContentTitle(title)
            .setSummaryText(formatAmount(bill.amount))

        val notification = NotificationCompat.Builder(context, CHANNEL_DUE_REMINDERS)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(bigTextStyle)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setContentIntent(createViewBillIntent(bill.id))
            .addAction(createMarkPaidAction(bill.id))
            .addAction(createSnoozeAction(bill.id, daysUntilDue))
            .addAction(createViewDetailsAction(bill.id))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .applyAudioSettings()
            .build()

        val notificationId = NOTIFICATION_ID_BASE + bill.id.hashCode()
        notificationManager.notify(notificationId, notification)
    }

    /**
     * Show snoozed reminder notification for a bill
     */
    fun showSnoozedReminder(bill: Bill, daysUntilDue: Int) {
        if (!hasNotificationPermission()) return

        val title = "🔔 Odloženo obvestilo"
        val content = when (daysUntilDue) {
            0 -> "Račun ${bill.title} zapade danes!"
            1 -> "Račun ${bill.title} zapade jutri!"
            else -> "Račun ${bill.title} zapade čez $daysUntilDue dni!"
        }

        // Create rich snoozed notification
        val bigTextStyle = NotificationCompat.BigTextStyle()
            .bigText(createSnoozedNotificationText(bill, daysUntilDue))
            .setBigContentTitle("🔔 $title")
            .setSummaryText("Odloženo za 1 uro")

        val notification = NotificationCompat.Builder(context, CHANNEL_DUE_REMINDERS)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(bigTextStyle)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setContentIntent(createViewBillIntent(bill.id))
            .addAction(createMarkPaidAction(bill.id))
            .addAction(createSnoozeAction(bill.id, daysUntilDue))
            .addAction(createViewDetailsAction(bill.id))
            .setPriority(NotificationCompat.PRIORITY_HIGH) // Higher priority for snoozed
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setColor(0xFF2196F3.toInt()) // Blue color for snoozed notifications
            .applyAudioSettings()
            .build()

        // Trigger special vibration for snoozed notifications
        triggerCustomVibration(VibrationPattern.GENTLE)

        val notificationId = NOTIFICATION_ID_BASE + bill.id.hashCode()
        notificationManager.notify(notificationId, notification)
    }

    /**
     * Show overdue bill alert
     */
    fun showOverdueAlert(bill: Bill, daysOverdue: Int) {
        if (!hasNotificationPermission()) return

        val title = context.getString(R.string.notification_overdue_title)
        val content = context.getString(
            R.string.notification_overdue_content,
            bill.title,
            daysOverdue,
            formatAmount(bill.amount)
        )

        // Create rich overdue notification with urgency
        val bigTextStyle = NotificationCompat.BigTextStyle()
            .bigText(createRichOverdueText(bill, daysOverdue))
            .setBigContentTitle("⚠️ $title")
            .setSummaryText("Zapadel pred $daysOverdue dnevi")

        val notification = NotificationCompat.Builder(context, CHANNEL_OVERDUE_ALERTS)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(bigTextStyle)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setContentIntent(createViewBillIntent(bill.id))
            .addAction(createMarkPaidAction(bill.id))
            .addAction(createViewDetailsAction(bill.id))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setColor(0xFFD32F2F.toInt()) // Red color for urgency
            .applyAudioSettings()
            .build()

        // Trigger urgent vibration for overdue notifications
        triggerCustomVibration(VibrationPattern.URGENT)

        val notificationId = NOTIFICATION_ID_BASE + bill.id.hashCode()
        notificationManager.notify(notificationId, notification)
    }

    /**
     * Cancel notification for a specific bill
     */
    fun cancelBillNotification(billId: String) {
        val notificationId = NOTIFICATION_ID_BASE + billId.hashCode()
        notificationManager.cancel(notificationId)
    }

    /**
     * Show grouped notification for multiple bills due on the same day
     */
    fun showGroupedDueDateReminder(bills: List<Bill>, daysUntilDue: Int) {
        if (!hasNotificationPermission() || bills.isEmpty()) return

        val totalAmount = bills.sumOf { it.amount }
        val billCount = bills.size

        val title = when (daysUntilDue) {
            0 -> "Danes zapade $billCount računov"
            1 -> "Jutri zapade $billCount računov"
            else -> "Čez $daysUntilDue dni zapade $billCount računov"
        }

        val content = "Skupaj: ${formatAmount(totalAmount)}"

        // Create inbox style for multiple bills
        val inboxStyle = NotificationCompat.InboxStyle()
            .setBigContentTitle(title)
            .setSummaryText("Skupaj: ${formatAmount(totalAmount)}")

        bills.take(5).forEach { bill ->
            inboxStyle.addLine("💰 ${bill.title} - ${formatAmount(bill.amount)}")
        }

        if (bills.size > 5) {
            inboxStyle.addLine("... in še ${bills.size - 5} računov")
        }

        val notification = NotificationCompat.Builder(context, CHANNEL_DUE_REMINDERS)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(inboxStyle)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setContentIntent(createViewBillListIntent(daysUntilDue))
            .addAction(createViewAllBillsAction(daysUntilDue))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setNumber(billCount)
            .build()

        // Use a special ID for grouped notifications
        val notificationId = NOTIFICATION_ID_BASE + 1000 + daysUntilDue
        notificationManager.notify(notificationId, notification)
    }

    /**
     * Show weekly summary notification
     */
    fun showWeeklySummary(upcomingBills: List<Bill>, totalAmount: Double) {
        if (!hasNotificationPermission() || upcomingBills.isEmpty()) return

        val title = "📅 Tedenski pregled računov"
        val content = "${upcomingBills.size} računov zapade v naslednjem tednu (${formatAmount(totalAmount)})"

        // Create inbox style for weekly summary
        val inboxStyle = NotificationCompat.InboxStyle()
            .setBigContentTitle(title)
            .setSummaryText("Skupaj: ${formatAmount(totalAmount)}")

        // Group bills by due date
        val billsByDate = upcomingBills.groupBy { bill ->
            bill.dueDate?.let {
                java.text.SimpleDateFormat("dd. MMM", Locale("sl")).format(it)
            } ?: "Ni določen"
        }

        // Add up to 6 lines (dates with bills)
        billsByDate.entries.take(6).forEach { (date, bills) ->
            val billsText = bills.joinToString(", ") { "${it.title} (${formatAmount(it.amount)})" }
            inboxStyle.addLine("📅 $date: $billsText")
        }

        if (billsByDate.size > 6) {
            inboxStyle.addLine("... in še ${billsByDate.size - 6} dodatnih dni")
        }

        val notification = NotificationCompat.Builder(context, CHANNEL_SUMMARIES)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(inboxStyle)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setContentIntent(createWeeklySummaryIntent())
            .addAction(createViewAllUpcomingAction())
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setColor(0xFF4CAF50.toInt()) // Green color for summaries
            .build()

        notificationManager.notify(NOTIFICATION_ID_WEEKLY_SUMMARY, notification)
    }

    /**
     * Cancel all notifications
     */
    fun cancelAllNotifications() {
        notificationManager.cancelAll()
    }

    /**
     * Create intent to view bill details
     */
    private fun createViewBillIntent(billId: String): PendingIntent {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra(EXTRA_BILL_ID, billId)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        return PendingIntent.getActivity(
            context,
            billId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * Create "Mark as Paid" action
     */
    private fun createMarkPaidAction(billId: String): NotificationCompat.Action {
        val intent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = ACTION_MARK_PAID
            putExtra(EXTRA_BILL_ID, billId)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            billId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Action.Builder(
            R.drawable.ic_check,
            context.getString(R.string.notification_action_mark_paid),
            pendingIntent
        ).build()
    }

    /**
     * Create "View Details" action
     */
    private fun createViewDetailsAction(billId: String): NotificationCompat.Action {
        val pendingIntent = createViewBillIntent(billId)

        return NotificationCompat.Action.Builder(
            R.drawable.ic_view,
            context.getString(R.string.notification_action_view_details),
            pendingIntent
        ).build()
    }

    /**
     * Create intent to view bill list with filter
     */
    private fun createViewBillListIntent(daysUntilDue: Int): PendingIntent {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "bill_list")
            putExtra("filter", when (daysUntilDue) {
                0 -> "DUE_TODAY"
                1 -> "DUE_TOMORROW"
                else -> "UPCOMING"
            })
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        return PendingIntent.getActivity(
            context,
            daysUntilDue + 5000, // Unique request code
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * Create "View All Bills" action for grouped notifications
     */
    private fun createViewAllBillsAction(daysUntilDue: Int): NotificationCompat.Action {
        val pendingIntent = createViewBillListIntent(daysUntilDue)

        return NotificationCompat.Action.Builder(
            R.drawable.ic_view,
            "Poglej vse račune",
            pendingIntent
        ).build()
    }

    /**
     * Create snooze action for notifications
     */
    private fun createSnoozeAction(billId: String, daysUntilDue: Int): NotificationCompat.Action {
        val intent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = ACTION_SNOOZE
            putExtra(EXTRA_BILL_ID, billId)
            putExtra("days_until_due", daysUntilDue)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            billId.hashCode() + 3000, // Unique request code for snooze
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Action.Builder(
            R.drawable.ic_snooze,
            "Opomni čez 1h",
            pendingIntent
        ).build()
    }

    /**
     * Create intent for weekly summary
     */
    private fun createWeeklySummaryIntent(): PendingIntent {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "bill_list")
            putExtra("filter", "UPCOMING")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        return PendingIntent.getActivity(
            context,
            6000, // Unique request code for weekly summary
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * Create "View All Upcoming" action for weekly summary
     */
    private fun createViewAllUpcomingAction(): NotificationCompat.Action {
        val pendingIntent = createWeeklySummaryIntent()

        return NotificationCompat.Action.Builder(
            R.drawable.ic_view,
            "Poglej vse",
            pendingIntent
        ).build()
    }

    /**
     * Show a test notification to verify the system is working
     */
    fun showTestNotification() {
        if (!hasNotificationPermission()) return

        val notification = NotificationCompat.Builder(context, CHANNEL_DUE_REMINDERS)
            .setContentTitle("BillMate Test")
            .setContentText("Obvestila delujejo! Notifications are working!")
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        notificationManager.notify(999, notification)
    }

    /**
     * Create rich notification text for due date reminders
     */
    private fun createRichNotificationText(bill: Bill, daysUntilDue: Int): String {
        val amount = formatAmount(bill.amount)
        val dueDate = bill.dueDate?.let {
            java.text.SimpleDateFormat("dd. MMM yyyy", Locale("sl")).format(it)
        } ?: "Ni določen"

        val urgencyText = when (daysUntilDue) {
            0 -> "🔴 DANES zapade!"
            1 -> "🟡 JUTRI zapade!"
            else -> "🟢 Zapade čez $daysUntilDue dni"
        }

        return buildString {
            appendLine("$urgencyText")
            appendLine()
            appendLine("📄 ${bill.title}")
            appendLine("💰 $amount")
            appendLine("📅 Zapade: $dueDate")
            if (bill.description?.isNotBlank() == true) {
                appendLine()
                appendLine("📝 ${bill.description}")
            }
        }.trim()
    }

    /**
     * Create rich notification text for snoozed reminders
     */
    private fun createSnoozedNotificationText(bill: Bill, daysUntilDue: Int): String {
        val amount = formatAmount(bill.amount)
        val dueDate = bill.dueDate?.let {
            java.text.SimpleDateFormat("dd. MMM yyyy", Locale("sl")).format(it)
        } ?: "Ni določen"

        val urgencyText = when (daysUntilDue) {
            0 -> "🔔 DANES zapade - odloženo obvestilo!"
            1 -> "🔔 JUTRI zapade - odloženo obvestilo!"
            else -> "🔔 Zapade čez $daysUntilDue dni - odloženo obvestilo!"
        }

        return buildString {
            appendLine("$urgencyText")
            appendLine()
            appendLine("📄 ${bill.title}")
            appendLine("💰 $amount")
            appendLine("📅 Zapade: $dueDate")
            appendLine("⏰ Odloženo za 1 uro")
            if (bill.description?.isNotBlank() == true) {
                appendLine()
                appendLine("📝 ${bill.description}")
            }
            appendLine()
            appendLine("Ne pozabite plačati!")
        }.trim()
    }

    /**
     * Create rich notification text for overdue alerts
     */
    private fun createRichOverdueText(bill: Bill, daysOverdue: Int): String {
        val amount = formatAmount(bill.amount)
        val dueDate = bill.dueDate?.let {
            java.text.SimpleDateFormat("dd. MMM yyyy", Locale("sl")).format(it)
        } ?: "Ni določen"

        val urgencyLevel = when {
            daysOverdue >= 30 -> "🔴 KRITIČNO"
            daysOverdue >= 7 -> "🟠 NUJNO"
            else -> "🟡 POZORNOST"
        }

        return buildString {
            appendLine("$urgencyLevel - Račun je zapadel!")
            appendLine()
            appendLine("📄 ${bill.title}")
            appendLine("💰 $amount")
            appendLine("📅 Zapadel: $dueDate")
            appendLine("⏰ Zamuda: $daysOverdue dni")
            if (bill.description?.isNotBlank() == true) {
                appendLine()
                appendLine("📝 ${bill.description}")
            }
            appendLine()
            appendLine("Plačajte čim prej, da se izognete dodatnim stroškom!")
        }.trim()
    }

    /**
     * Apply sound and vibration settings to notification builder
     */
    private fun NotificationCompat.Builder.applyAudioSettings(): NotificationCompat.Builder {
        val settings = notificationPreferences.getCurrentSettings()

        // Apply sound settings
        if (settings.soundEnabled) {
            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            setSound(defaultSoundUri)
        } else {
            setSound(null)
        }

        // Apply vibration settings
        if (settings.vibrationEnabled && settings.vibrationPattern != VibrationPattern.NONE) {
            setVibrate(settings.vibrationPattern.pattern)
        } else {
            setVibrate(null)
        }

        return this
    }

    /**
     * Trigger custom vibration based on user preferences
     */
    private fun triggerCustomVibration(pattern: VibrationPattern) {
        val settings = notificationPreferences.getCurrentSettings()

        if (!settings.vibrationEnabled || pattern == VibrationPattern.NONE) return

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val vibrationEffect = VibrationEffect.createWaveform(pattern.pattern, -1)
            vibrator.vibrate(vibrationEffect)
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(pattern.pattern, -1)
        }
    }

    /**
     * Format amount with European formatting
     */
    private fun formatAmount(amount: Double): String {
        return "€${String.format(Locale.getDefault(), "%.2f", amount).replace('.', ',')}"
    }
}
