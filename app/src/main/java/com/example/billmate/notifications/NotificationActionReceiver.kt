package com.example.billmate.notifications

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import com.example.billmate.R
import com.example.billmate.repository.BillRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Handles actions from notification buttons (Mark as Paid, View Details)
 */
class NotificationActionReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "NotificationActionReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        val billId = intent.getStringExtra(BillNotificationManager.EXTRA_BILL_ID) ?: return

        when (intent.action) {
            BillNotificationManager.ACTION_MARK_PAID -> {
                handleMarkPaidAction(context, billId)
            }
            BillNotificationManager.ACTION_VIEW_BILL -> {
                handleViewBillAction(context, billId)
            }
            BillNotificationManager.ACTION_SNOOZE -> {
                val daysUntilDue = intent.getIntExtra("days_until_due", 0)
                handleSnooze(context, billId, daysUntilDue)
            }
        }
    }

    /**
     * Handle "Mark as Paid" action from notification
     */
    private fun handleMarkPaidAction(context: Context, billId: String) {
        val billRepository = BillRepository(context)
        val notificationManager = BillNotificationManager(context)

        // Use coroutine to handle async operation
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = billRepository.markBillPaid(billId, true)

                CoroutineScope(Dispatchers.Main).launch {
                    if (result.isSuccess) {
                        // Cancel the notification
                        notificationManager.cancelBillNotification(billId)

                        // Show success toast
                        Toast.makeText(
                            context,
                            context.getString(R.string.notification_marked_paid_success),
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        // Show error toast
                        Toast.makeText(
                            context,
                            context.getString(R.string.notification_marked_paid_error),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } catch (e: Exception) {
                CoroutineScope(Dispatchers.Main).launch {
                    Toast.makeText(
                        context,
                        context.getString(R.string.notification_marked_paid_error),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

    /**
     * Handle "View Bill" action from notification
     */
    private fun handleViewBillAction(context: Context, billId: String) {
        // This will be handled by the main activity intent
        // The notification already creates the proper intent
        val notificationManager = BillNotificationManager(context)
        notificationManager.cancelBillNotification(billId)
    }

    /**
     * Handle "Snooze" action from notification
     */
    private fun handleSnooze(context: Context, billId: String, daysUntilDue: Int) {
        try {
            Log.d(TAG, "Snoozing notification for bill: $billId for 1 hour")

            // Cancel the current notification
            val notificationManager = BillNotificationManager(context)
            notificationManager.cancelBillNotification(billId)

            // Schedule a new notification in 1 hour
            val notificationScheduler = NotificationScheduler(context)
            notificationScheduler.scheduleSnoozeNotification(billId, daysUntilDue)

            // Show confirmation toast
            Toast.makeText(context, "Obvestilo odloženo za 1 uro", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "Error handling snooze action", e)
            Toast.makeText(context, "Napaka pri odlaganju obvestila", Toast.LENGTH_SHORT).show()
        }
    }
}
