package com.example.billmate.notifications

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.example.billmate.model.Bill
import com.example.billmate.repository.BillRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

/**
 * Handles scheduling and canceling of bill notifications
 */
class NotificationScheduler(private val context: Context) {

    private val notificationPreferences = NotificationPreferences(context)
    private val dndManager = DoNotDisturbManager(context)
    private val batteryOptimizationManager = BatteryOptimizationManager(context)

    companion object {
        private const val TAG = "NotificationScheduler"

        // Request codes for different notification types
        private const val REQUEST_CODE_BASE = 10000
        private const val REQUEST_CODE_3_DAYS = 1
        private const val REQUEST_CODE_1_DAY = 2
        private const val REQUEST_CODE_SAME_DAY = 3
        private const val REQUEST_CODE_OVERDUE = 4
        private const val REQUEST_CODE_SNOOZE = 5
        private const val REQUEST_CODE_WEEKLY_SUMMARY = 6

        // Default notification time (9:00 AM)
        private const val DEFAULT_NOTIFICATION_HOUR = 9
        private const val DEFAULT_NOTIFICATION_MINUTE = 0
    }

    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

    /**
     * Schedule all notifications for a bill (3 days, 1 day, same day)
     */
    fun scheduleBillNotifications(bill: Bill) {
        val settings = notificationPreferences.getCurrentSettings()

        // Check if notifications are enabled globally
        if (!settings.notificationsEnabled) {
            Log.d(TAG, "Notifications disabled globally, skipping bill ${bill.id}")
            return
        }

        if (bill.dueDate == null || bill.isPaid) {
            Log.d(TAG, "Skipping notifications for bill ${bill.id}: ${if (bill.dueDate == null) "no due date" else "already paid"}")
            return
        }

        val dueDate = bill.dueDate
        val dueDateCalendar = Calendar.getInstance().apply { time = dueDate }

        // Schedule 3 days before notification (if enabled)
        if (settings.remind3Days) {
            scheduleNotificationForDate(
                bill = bill,
                targetDate = dueDateCalendar.apply { add(Calendar.DAY_OF_MONTH, -3) }.time,
                daysUntilDue = 3,
                requestCodeOffset = REQUEST_CODE_3_DAYS,
                notificationHour = settings.notificationHour,
                notificationMinute = settings.notificationMinute
            )
        }

        // Reset calendar for 1 day before (if enabled)
        if (settings.remind1Day) {
            dueDateCalendar.time = dueDate
            scheduleNotificationForDate(
                bill = bill,
                targetDate = dueDateCalendar.apply { add(Calendar.DAY_OF_MONTH, -1) }.time,
                daysUntilDue = 1,
                requestCodeOffset = REQUEST_CODE_1_DAY,
                notificationHour = settings.notificationHour,
                notificationMinute = settings.notificationMinute
            )
        }

        // Reset calendar for same day (if enabled)
        if (settings.remindSameDay) {
            dueDateCalendar.time = dueDate
            scheduleNotificationForDate(
                bill = bill,
                targetDate = dueDate,
                daysUntilDue = 0,
                requestCodeOffset = REQUEST_CODE_SAME_DAY,
                notificationHour = settings.notificationHour,
                notificationMinute = settings.notificationMinute
            )
        }

        Log.d(TAG, "Scheduled notifications for bill: ${bill.title} (due: ${bill.dueDate})")
    }

    /**
     * Schedule a notification for a specific date
     */
    private fun scheduleNotificationForDate(
        bill: Bill,
        targetDate: Date,
        daysUntilDue: Int,
        requestCodeOffset: Int,
        notificationHour: Int = DEFAULT_NOTIFICATION_HOUR,
        notificationMinute: Int = DEFAULT_NOTIFICATION_MINUTE
    ) {
        val notificationTime = Calendar.getInstance().apply {
            time = targetDate
            set(Calendar.HOUR_OF_DAY, notificationHour)
            set(Calendar.MINUTE, notificationMinute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)

            // Smart weekend handling - move weekend notifications to Friday
            if (daysUntilDue > 0) { // Only for advance reminders, not same-day
                adjustForWeekend()
            }
        }

        // Respect Do Not Disturb and quiet hours
        val adjustedTime = dndManager.getNextNotificationTime(notificationTime)

        // Log battery optimization status for debugging
        if (daysUntilDue == 0) { // Only log for same-day notifications to avoid spam
            batteryOptimizationManager.logBatteryOptimizationStatus()
        }

        // Don't schedule notifications in the past
        if (adjustedTime.timeInMillis <= System.currentTimeMillis()) {
            Log.d(TAG, "Skipping past notification for bill ${bill.id}, $daysUntilDue days before due")
            return
        }

        val intent = Intent(context, NotificationReceiver::class.java).apply {
            putExtra("bill_id", bill.id)
            putExtra("bill_title", bill.title)
            putExtra("bill_amount", bill.amount)
            putExtra("days_until_due", daysUntilDue)
            putExtra("notification_type", "due_reminder")
        }

        val requestCode = generateRequestCode(bill.id, requestCodeOffset)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    adjustedTime.timeInMillis,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    adjustedTime.timeInMillis,
                    pendingIntent
                )
            }

            val timeAdjustment = if (adjustedTime.timeInMillis != notificationTime.timeInMillis) {
                " (adjusted for DND/quiet hours)"
            } else {
                ""
            }

            Log.d(TAG, "Scheduled notification for ${bill.title} at ${adjustedTime.time} ($daysUntilDue days before due)$timeAdjustment")
        } catch (e: SecurityException) {
            Log.e(TAG, "Failed to schedule notification: ${e.message}")
        }
    }

    /**
     * Cancel all notifications for a bill
     */
    fun cancelBillNotifications(billId: String) {
        val requestCodes = listOf(
            generateRequestCode(billId, REQUEST_CODE_3_DAYS),
            generateRequestCode(billId, REQUEST_CODE_1_DAY),
            generateRequestCode(billId, REQUEST_CODE_SAME_DAY),
            generateRequestCode(billId, REQUEST_CODE_OVERDUE)
        )

        requestCodes.forEach { requestCode ->
            val intent = Intent(context, NotificationReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            alarmManager.cancel(pendingIntent)
        }

        Log.d(TAG, "Canceled notifications for bill: $billId")
    }

    /**
     * Schedule overdue notification for a bill
     */
    fun scheduleOverdueNotification(bill: Bill) {
        val settings = notificationPreferences.getCurrentSettings()

        // Check if notifications and overdue notifications are enabled
        if (!settings.notificationsEnabled || !settings.overdueNotifications) {
            Log.d(TAG, "Overdue notifications disabled, skipping bill ${bill.id}")
            return
        }

        if (bill.isPaid || bill.dueDate == null) return

        val now = Calendar.getInstance()
        val dueDate = Calendar.getInstance().apply { time = bill.dueDate }

        // Only schedule if bill is actually overdue
        if (now.timeInMillis <= dueDate.timeInMillis) return

        // Schedule overdue notification for next day at user's preferred time
        val overdueNotificationTime = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, settings.notificationHour)
            set(Calendar.MINUTE, settings.notificationMinute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val intent = Intent(context, NotificationReceiver::class.java).apply {
            putExtra("bill_id", bill.id)
            putExtra("bill_title", bill.title)
            putExtra("bill_amount", bill.amount)
            putExtra("days_overdue", calculateDaysOverdue(bill.dueDate))
            putExtra("notification_type", "overdue_alert")
        }

        val requestCode = generateRequestCode(bill.id, REQUEST_CODE_OVERDUE)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    overdueNotificationTime.timeInMillis,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    overdueNotificationTime.timeInMillis,
                    pendingIntent
                )
            }

            Log.d(TAG, "Scheduled overdue notification for ${bill.title}")
        } catch (e: SecurityException) {
            Log.e(TAG, "Failed to schedule overdue notification: ${e.message}")
        }
    }

    /**
     * Generate unique request code for bill and notification type
     */
    private fun generateRequestCode(billId: String, offset: Int): Int {
        return REQUEST_CODE_BASE + billId.hashCode() + offset
    }

    /**
     * Calculate days overdue for a bill
     */
    private fun calculateDaysOverdue(dueDate: Date): Int {
        val now = Calendar.getInstance()
        val due = Calendar.getInstance().apply { time = dueDate }
        val diffInMillis = now.timeInMillis - due.timeInMillis
        return (diffInMillis / (1000 * 60 * 60 * 24)).toInt()
    }

    /**
     * Schedule a snoozed notification (1 hour from now)
     */
    fun scheduleSnoozeNotification(billId: String, daysUntilDue: Int) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val billRepository = BillRepository(context)
                val bill = billRepository.getBillById(billId)

                if (bill == null || bill.isPaid) {
                    Log.d(TAG, "Bill not found or already paid, skipping snooze: $billId")
                    return@launch
                }

                // Schedule notification for 1 hour from now
                val snoozeTime = Calendar.getInstance().apply {
                    add(Calendar.HOUR_OF_DAY, 1)
                }

                val intent = Intent(context, NotificationReceiver::class.java).apply {
                    putExtra("bill_id", bill.id)
                    putExtra("bill_title", bill.title)
                    putExtra("bill_amount", bill.amount)
                    putExtra("days_until_due", daysUntilDue)
                    putExtra("is_snoozed", true)
                }

                val requestCode = generateRequestCode(billId, REQUEST_CODE_SNOOZE)
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    requestCode,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        snoozeTime.timeInMillis,
                        pendingIntent
                    )
                } else {
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        snoozeTime.timeInMillis,
                        pendingIntent
                    )
                }

                Log.d(TAG, "Scheduled snooze notification for bill ${bill.title} at ${snoozeTime.time}")

            } catch (e: Exception) {
                Log.e(TAG, "Error scheduling snooze notification", e)
            }
        }
    }

    /**
     * Schedule weekly summary notification (every Sunday at 7 PM)
     */
    fun scheduleWeeklySummary() {
        val settings = notificationPreferences.getCurrentSettings()

        if (!settings.notificationsEnabled) {
            Log.d(TAG, "Notifications disabled, skipping weekly summary")
            return
        }

        // Schedule for next Sunday at 7 PM
        val nextSunday = Calendar.getInstance().apply {
            // Find next Sunday
            while (get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
                add(Calendar.DAY_OF_MONTH, 1)
            }
            // If today is Sunday and it's past 7 PM, move to next Sunday
            if (get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY &&
                (get(Calendar.HOUR_OF_DAY) > 19 ||
                 (get(Calendar.HOUR_OF_DAY) == 19 && get(Calendar.MINUTE) >= 0))) {
                add(Calendar.WEEK_OF_YEAR, 1)
            }

            set(Calendar.HOUR_OF_DAY, 19) // 7 PM
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val intent = Intent(context, NotificationReceiver::class.java).apply {
            putExtra("notification_type", "weekly_summary")
        }

        val requestCode = generateRequestCode("weekly_summary", REQUEST_CODE_WEEKLY_SUMMARY)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                nextSunday.timeInMillis,
                pendingIntent
            )
        } else {
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                nextSunday.timeInMillis,
                pendingIntent
            )
        }

        Log.d(TAG, "Scheduled weekly summary for ${nextSunday.time}")
    }

    /**
     * Cancel weekly summary notifications
     */
    fun cancelWeeklySummary() {
        val intent = Intent(context, NotificationReceiver::class.java)
        val requestCode = generateRequestCode("weekly_summary", REQUEST_CODE_WEEKLY_SUMMARY)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        alarmManager.cancel(pendingIntent)
        Log.d(TAG, "Canceled weekly summary notifications")
    }

    /**
     * Adjust notification time to avoid weekends for advance reminders
     */
    private fun Calendar.adjustForWeekend() {
        when (get(Calendar.DAY_OF_WEEK)) {
            Calendar.SATURDAY -> {
                // Move Saturday notifications to Friday
                add(Calendar.DAY_OF_MONTH, -1)
                Log.d(TAG, "Moved Saturday notification to Friday")
            }
            Calendar.SUNDAY -> {
                // Move Sunday notifications to Friday
                add(Calendar.DAY_OF_MONTH, -2)
                Log.d(TAG, "Moved Sunday notification to Friday")
            }
        }
    }
}
