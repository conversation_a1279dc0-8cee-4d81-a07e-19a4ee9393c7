package com.example.billmate.notifications

import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.util.Log
import java.util.Calendar

/**
 * Manages Do Not Disturb integration and smart notification timing
 */
class DoNotDisturbManager(private val context: Context) {
    
    companion object {
        private const val TAG = "DoNotDisturbManager"
        
        // Quiet hours (default: 22:00 - 07:00)
        private const val QUIET_HOUR_START = 22
        private const val QUIET_HOUR_END = 7
    }
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    
    /**
     * Check if Do Not Disturb is currently active
     */
    fun isDoNotDisturbActive(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val filter = notificationManager.currentInterruptionFilter
            when (filter) {
                NotificationManager.INTERRUPTION_FILTER_NONE,
                NotificationManager.INTERRUPTION_FILTER_PRIORITY,
                NotificationManager.INTERRUPTION_FILTER_ALARMS -> {
                    Log.d(TAG, "Do Not Disturb is active: filter = $filter")
                    true
                }
                else -> false
            }
        } else {
            false
        }
    }
    
    /**
     * Check if current time is during quiet hours
     */
    fun isQuietHours(): Boolean {
        val now = Calendar.getInstance()
        val currentHour = now.get(Calendar.HOUR_OF_DAY)
        
        return if (QUIET_HOUR_START > QUIET_HOUR_END) {
            // Quiet hours span midnight (e.g., 22:00 - 07:00)
            currentHour >= QUIET_HOUR_START || currentHour < QUIET_HOUR_END
        } else {
            // Quiet hours within same day
            currentHour >= QUIET_HOUR_START && currentHour < QUIET_HOUR_END
        }
    }
    
    /**
     * Check if notifications should be suppressed
     */
    fun shouldSuppressNotifications(): Boolean {
        val dndActive = isDoNotDisturbActive()
        val quietHours = isQuietHours()
        
        Log.d(TAG, "DND check - DND active: $dndActive, Quiet hours: $quietHours")
        
        return dndActive || quietHours
    }
    
    /**
     * Get next appropriate time to show notification (respecting DND and quiet hours)
     */
    fun getNextNotificationTime(preferredTime: Calendar): Calendar {
        val nextTime = preferredTime.clone() as Calendar
        
        // If preferred time is during quiet hours, move to end of quiet hours
        if (isTimeInQuietHours(nextTime)) {
            nextTime.set(Calendar.HOUR_OF_DAY, QUIET_HOUR_END)
            nextTime.set(Calendar.MINUTE, 0)
            nextTime.set(Calendar.SECOND, 0)
            nextTime.set(Calendar.MILLISECOND, 0)
            
            // If that's still today and in the past, move to tomorrow
            val now = Calendar.getInstance()
            if (nextTime.timeInMillis <= now.timeInMillis) {
                nextTime.add(Calendar.DAY_OF_MONTH, 1)
            }
            
            Log.d(TAG, "Moved notification from quiet hours to: ${nextTime.time}")
        }
        
        return nextTime
    }
    
    /**
     * Check if a specific time is during quiet hours
     */
    private fun isTimeInQuietHours(time: Calendar): Boolean {
        val hour = time.get(Calendar.HOUR_OF_DAY)
        
        return if (QUIET_HOUR_START > QUIET_HOUR_END) {
            // Quiet hours span midnight
            hour >= QUIET_HOUR_START || hour < QUIET_HOUR_END
        } else {
            // Quiet hours within same day
            hour >= QUIET_HOUR_START && hour < QUIET_HOUR_END
        }
    }
    
    /**
     * Get DND status description for UI
     */
    fun getDndStatusDescription(): String {
        return when {
            isDoNotDisturbActive() -> "Ne moti je vključen"
            isQuietHours() -> "Tihi čas (${QUIET_HOUR_START}:00 - ${QUIET_HOUR_END}:00)"
            else -> "Obvestila so dovoljena"
        }
    }
    
    /**
     * Check if notification should be shown immediately or delayed
     */
    fun shouldDelayNotification(): Boolean {
        return shouldSuppressNotifications()
    }
    
    /**
     * Get delay time in milliseconds until notifications can be shown
     */
    fun getDelayUntilAllowed(): Long {
        if (!shouldSuppressNotifications()) return 0L
        
        val now = Calendar.getInstance()
        val allowedTime = getNextNotificationTime(now)
        
        return allowedTime.timeInMillis - now.timeInMillis
    }
}
