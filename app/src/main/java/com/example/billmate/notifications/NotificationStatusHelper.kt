package com.example.billmate.notifications

import android.content.Context
import com.example.billmate.model.Bill
import java.util.Calendar
import java.util.Date

/**
 * Helper class to determine notification status for bills
 */
class NotificationStatusHelper(private val context: Context) {
    
    private val notificationPreferences = NotificationPreferences(context)
    
    /**
     * Data class representing notification status for a bill
     */
    data class NotificationStatus(
        val hasActiveNotifications: Boolean = false,
        val nextNotificationDate: Date? = null,
        val notificationTypes: List<NotificationType> = emptyList(),
        val isOverdue: Boolean = false
    )
    
    /**
     * Types of notifications that can be scheduled
     */
    enum class NotificationType {
        THREE_DAYS_BEFORE,
        ONE_DAY_BEFORE,
        SAME_DAY,
        OVERDUE,
        SNOOZED
    }
    
    /**
     * Get notification status for a bill
     */
    fun getNotificationStatus(bill: Bill): NotificationStatus {
        val settings = notificationPreferences.getCurrentSettings()
        
        // If notifications are disabled globally or bill is paid, no notifications
        if (!settings.notificationsEnabled || bill.isPaid || bill.dueDate == null) {
            return NotificationStatus()
        }
        
        val now = Calendar.getInstance()
        val dueDate = Calendar.getInstance().apply { time = bill.dueDate }
        val daysDifference = calculateDaysDifference(now, dueDate)
        
        val notificationTypes = mutableListOf<NotificationType>()
        var nextNotificationDate: Date? = null
        
        // Check if bill is overdue
        val isOverdue = daysDifference < 0
        if (isOverdue && settings.overdueNotifications) {
            notificationTypes.add(NotificationType.OVERDUE)
            // Next overdue notification would be tomorrow
            nextNotificationDate = Calendar.getInstance().apply {
                add(Calendar.DAY_OF_MONTH, 1)
                set(Calendar.HOUR_OF_DAY, settings.notificationHour)
                set(Calendar.MINUTE, settings.notificationMinute)
            }.time
        }
        
        // Check upcoming notifications
        if (!isOverdue) {
            // 3 days before
            if (daysDifference <= 3 && settings.remind3Days) {
                notificationTypes.add(NotificationType.THREE_DAYS_BEFORE)
                if (daysDifference == 3) {
                    nextNotificationDate = getNotificationTimeForDay(bill.dueDate, -3, settings)
                }
            }
            
            // 1 day before
            if (daysDifference <= 1 && settings.remind1Day) {
                notificationTypes.add(NotificationType.ONE_DAY_BEFORE)
                if (daysDifference == 1) {
                    nextNotificationDate = getNotificationTimeForDay(bill.dueDate, -1, settings)
                }
            }
            
            // Same day
            if (daysDifference == 0 && settings.remindSameDay) {
                notificationTypes.add(NotificationType.SAME_DAY)
                nextNotificationDate = getNotificationTimeForDay(bill.dueDate, 0, settings)
            }
            
            // If no specific next notification set, find the earliest upcoming one
            if (nextNotificationDate == null && notificationTypes.isNotEmpty()) {
                nextNotificationDate = findNextNotificationDate(bill, settings, daysDifference)
            }
        }
        
        return NotificationStatus(
            hasActiveNotifications = notificationTypes.isNotEmpty(),
            nextNotificationDate = nextNotificationDate,
            notificationTypes = notificationTypes,
            isOverdue = isOverdue
        )
    }
    
    /**
     * Calculate days difference between two dates
     */
    private fun calculateDaysDifference(from: Calendar, to: Calendar): Int {
        val fromDate = Calendar.getInstance().apply {
            time = from.time
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        
        val toDate = Calendar.getInstance().apply {
            time = to.time
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        
        val diffInMillis = toDate.timeInMillis - fromDate.timeInMillis
        return (diffInMillis / (1000 * 60 * 60 * 24)).toInt()
    }
    
    /**
     * Get notification time for a specific day relative to due date
     */
    private fun getNotificationTimeForDay(dueDate: Date, dayOffset: Int, settings: NotificationSettings): Date {
        return Calendar.getInstance().apply {
            time = dueDate
            add(Calendar.DAY_OF_MONTH, dayOffset)
            set(Calendar.HOUR_OF_DAY, settings.notificationHour)
            set(Calendar.MINUTE, settings.notificationMinute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time
    }
    
    /**
     * Find the next notification date for a bill
     */
    private fun findNextNotificationDate(bill: Bill, settings: NotificationSettings, daysDifference: Int): Date? {
        val dueDate = bill.dueDate ?: return null
        
        return when {
            daysDifference >= 3 && settings.remind3Days -> 
                getNotificationTimeForDay(dueDate, -3, settings)
            daysDifference >= 1 && settings.remind1Day -> 
                getNotificationTimeForDay(dueDate, -1, settings)
            daysDifference >= 0 && settings.remindSameDay -> 
                getNotificationTimeForDay(dueDate, 0, settings)
            else -> null
        }
    }
    
    /**
     * Get a user-friendly description of notification status
     */
    fun getStatusDescription(status: NotificationStatus): String {
        return when {
            !status.hasActiveNotifications -> "Ni obvestil"
            status.isOverdue -> "Zapadel - obvestila aktivna"
            status.nextNotificationDate != null -> {
                val nextDate = java.text.SimpleDateFormat("dd. MMM", java.util.Locale("sl"))
                    .format(status.nextNotificationDate)
                "Naslednje obvestilo: $nextDate"
            }
            else -> "Obvestila aktivna"
        }
    }
}
