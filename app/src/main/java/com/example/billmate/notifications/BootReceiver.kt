package com.example.billmate.notifications

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.example.billmate.repository.BillRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * Receives boot completed broadcasts and reschedules notifications
 */
class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            Log.d(TAG, "Device boot completed, rescheduling notifications")
            rescheduleAllNotifications(context)
        }
    }

    /**
     * Reschedule notifications for all unpaid bills
     */
    private fun rescheduleAllNotifications(context: Context) {
        val billRepository = BillRepository(context)
        val notificationScheduler = NotificationScheduler(context)
        val notificationPreferences = NotificationPreferences(context)

        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Check if notifications are enabled
                val settings = notificationPreferences.getCurrentSettings()
                if (!settings.notificationsEnabled) {
                    Log.d(TAG, "Notifications disabled, skipping reschedule")
                    return@launch
                }

                // Get all bills
                val bills = billRepository.getBillsFlow().first()

                // Reschedule notifications for unpaid bills
                val unpaidBills = bills.filter { !it.isPaid && it.dueDate != null }

                Log.d(TAG, "Rescheduling notifications for ${unpaidBills.size} unpaid bills")

                unpaidBills.forEach { bill ->
                    try {
                        notificationScheduler.scheduleBillNotifications(bill)
                        Log.d(TAG, "Rescheduled notifications for bill: ${bill.title}")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to reschedule notifications for bill ${bill.id}", e)
                    }
                }

                Log.d(TAG, "Notification rescheduling completed")

                // Also reschedule weekly summary
                notificationScheduler.scheduleWeeklySummary()
                Log.d(TAG, "Rescheduled weekly summary")

            } catch (e: Exception) {
                Log.e(TAG, "Error rescheduling notifications", e)
            }
        }
    }
}
