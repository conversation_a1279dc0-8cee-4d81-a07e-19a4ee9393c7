package com.example.billmate.notifications

import android.content.Context
import android.content.SharedPreferences
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Data class for notification preferences
 */
data class NotificationSettings(
    val notificationsEnabled: Boolean = true,
    val remind3Days: Boolean = true,
    val remind1Day: Boolean = true,
    val remindSameDay: <PERSON>olean = true,
    val overdueNotifications: Boolean = true,
    val weeklySummary: <PERSON>olean = true,
    val notificationHour: Int = 9,
    val notificationMinute: Int = 0,
    val soundEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,
    val vibrationPattern: VibrationPattern = VibrationPattern.DEFAULT
)

enum class VibrationPattern(val displayName: String, val pattern: LongArray) {
    DEFAULT("Privzeto", longArrayOf(0, 250, 250, 250)),
    GENTLE("Nežno", longArrayOf(0, 100, 100, 100)),
    STRONG("Močno", longArrayOf(0, 500, 200, 500)),
    URGENT("Nujno", longArrayOf(0, 100, 50, 100, 50, 100, 50, 500)),
    NONE("Brez", longArrayOf())
}

/**
 * Manages notification preferences using SharedPreferences
 */
class NotificationPreferences(context: Context) {

    companion object {
        private const val PREFS_NAME = "notification_preferences"
        private const val KEY_NOTIFICATIONS_ENABLED = "notifications_enabled"
        private const val KEY_REMIND_3_DAYS = "remind_3_days"
        private const val KEY_REMIND_1_DAY = "remind_1_day"
        private const val KEY_REMIND_SAME_DAY = "remind_same_day"
        private const val KEY_OVERDUE_NOTIFICATIONS = "overdue_notifications"
        private const val KEY_WEEKLY_SUMMARY = "weekly_summary"
        private const val KEY_NOTIFICATION_HOUR = "notification_hour"
        private const val KEY_NOTIFICATION_MINUTE = "notification_minute"
        private const val KEY_SOUND_ENABLED = "sound_enabled"
        private const val KEY_VIBRATION_ENABLED = "vibration_enabled"
        private const val KEY_VIBRATION_PATTERN = "vibration_pattern"
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private val _settings = MutableStateFlow(loadSettings())
    val settings: StateFlow<NotificationSettings> = _settings.asStateFlow()

    /**
     * Load settings from SharedPreferences
     */
    private fun loadSettings(): NotificationSettings {
        return NotificationSettings(
            notificationsEnabled = sharedPreferences.getBoolean(KEY_NOTIFICATIONS_ENABLED, true),
            remind3Days = sharedPreferences.getBoolean(KEY_REMIND_3_DAYS, true),
            remind1Day = sharedPreferences.getBoolean(KEY_REMIND_1_DAY, true),
            remindSameDay = sharedPreferences.getBoolean(KEY_REMIND_SAME_DAY, true),
            overdueNotifications = sharedPreferences.getBoolean(KEY_OVERDUE_NOTIFICATIONS, true),
            weeklySummary = sharedPreferences.getBoolean(KEY_WEEKLY_SUMMARY, true),
            notificationHour = sharedPreferences.getInt(KEY_NOTIFICATION_HOUR, 9),
            notificationMinute = sharedPreferences.getInt(KEY_NOTIFICATION_MINUTE, 0),
            soundEnabled = sharedPreferences.getBoolean(KEY_SOUND_ENABLED, true),
            vibrationEnabled = sharedPreferences.getBoolean(KEY_VIBRATION_ENABLED, true),
            vibrationPattern = VibrationPattern.valueOf(
                sharedPreferences.getString(KEY_VIBRATION_PATTERN, VibrationPattern.DEFAULT.name)
                    ?: VibrationPattern.DEFAULT.name
            )
        )
    }

    /**
     * Save settings to SharedPreferences
     */
    private fun saveSettings(settings: NotificationSettings) {
        sharedPreferences.edit().apply {
            putBoolean(KEY_NOTIFICATIONS_ENABLED, settings.notificationsEnabled)
            putBoolean(KEY_REMIND_3_DAYS, settings.remind3Days)
            putBoolean(KEY_REMIND_1_DAY, settings.remind1Day)
            putBoolean(KEY_REMIND_SAME_DAY, settings.remindSameDay)
            putBoolean(KEY_OVERDUE_NOTIFICATIONS, settings.overdueNotifications)
            putBoolean(KEY_WEEKLY_SUMMARY, settings.weeklySummary)
            putInt(KEY_NOTIFICATION_HOUR, settings.notificationHour)
            putInt(KEY_NOTIFICATION_MINUTE, settings.notificationMinute)
            putBoolean(KEY_SOUND_ENABLED, settings.soundEnabled)
            putBoolean(KEY_VIBRATION_ENABLED, settings.vibrationEnabled)
            putString(KEY_VIBRATION_PATTERN, settings.vibrationPattern.name)
            apply()
        }
    }

    /**
     * Update notification enabled status
     */
    fun setNotificationsEnabled(enabled: Boolean) {
        val newSettings = _settings.value.copy(notificationsEnabled = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update 3-day reminder setting
     */
    fun setRemind3Days(enabled: Boolean) {
        val newSettings = _settings.value.copy(remind3Days = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update 1-day reminder setting
     */
    fun setRemind1Day(enabled: Boolean) {
        val newSettings = _settings.value.copy(remind1Day = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update same-day reminder setting
     */
    fun setRemindSameDay(enabled: Boolean) {
        val newSettings = _settings.value.copy(remindSameDay = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update overdue notifications setting
     */
    fun setOverdueNotifications(enabled: Boolean) {
        val newSettings = _settings.value.copy(overdueNotifications = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update weekly summary setting
     */
    fun setWeeklySummary(enabled: Boolean) {
        val newSettings = _settings.value.copy(weeklySummary = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update sound enabled setting
     */
    fun setSoundEnabled(enabled: Boolean) {
        val newSettings = _settings.value.copy(soundEnabled = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update vibration enabled setting
     */
    fun setVibrationEnabled(enabled: Boolean) {
        val newSettings = _settings.value.copy(vibrationEnabled = enabled)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update vibration pattern
     */
    fun setVibrationPattern(pattern: VibrationPattern) {
        val newSettings = _settings.value.copy(vibrationPattern = pattern)
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Update notification time
     */
    fun setNotificationTime(hour: Int, minute: Int) {
        val newSettings = _settings.value.copy(
            notificationHour = hour,
            notificationMinute = minute
        )
        _settings.value = newSettings
        saveSettings(newSettings)
    }

    /**
     * Get current settings value
     */
    fun getCurrentSettings(): NotificationSettings {
        return _settings.value
    }
}
