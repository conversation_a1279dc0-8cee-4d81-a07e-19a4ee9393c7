package com.example.billmate.notifications

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.example.billmate.model.Bill
import com.example.billmate.repository.BillRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Date

/**
 * Receives scheduled notification alarms and displays notifications
 */
class NotificationReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "NotificationReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received notification alarm")

        val billId = intent.getStringExtra("bill_id") ?: return
        val billTitle = intent.getStringExtra("bill_title") ?: "Unknown Bill"
        val billAmount = intent.getDoubleExtra("bill_amount", 0.0)
        val notificationType = intent.getStringExtra("notification_type") ?: "due_reminder"
        val isSnoozed = intent.getBooleanExtra("is_snoozed", false)

        val notificationManager = BillNotificationManager(context)
        val billRepository = BillRepository(context)

        // Use coroutine to check current bill status
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Verify bill still exists and is unpaid
                val currentBill = billRepository.getBillById(billId)

                if (currentBill == null) {
                    Log.d(TAG, "Bill $billId no longer exists, skipping notification")
                    return@launch
                }

                if (currentBill.isPaid) {
                    Log.d(TAG, "Bill $billId is already paid, skipping notification")
                    return@launch
                }

                // Show appropriate notification based on type
                when (notificationType) {
                    "due_reminder" -> {
                        val daysUntilDue = intent.getIntExtra("days_until_due", 0)
                        if (isSnoozed) {
                            notificationManager.showSnoozedReminder(currentBill, daysUntilDue)
                            Log.d(TAG, "Showed snoozed reminder for ${currentBill.title} ($daysUntilDue days)")
                        } else {
                            notificationManager.showDueDateReminder(currentBill, daysUntilDue)
                            Log.d(TAG, "Showed due date reminder for ${currentBill.title} ($daysUntilDue days)")
                        }
                    }

                    "overdue_alert" -> {
                        val daysOverdue = intent.getIntExtra("days_overdue", 1)
                        notificationManager.showOverdueAlert(currentBill, daysOverdue)
                        Log.d(TAG, "Showed overdue alert for ${currentBill.title} ($daysOverdue days overdue)")
                    }

                    "weekly_summary" -> {
                        handleWeeklySummary(context, notificationManager, billRepository)
                    }

                    else -> {
                        Log.w(TAG, "Unknown notification type: $notificationType")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error processing notification for bill $billId", e)
            }
        }
    }

    /**
     * Handle weekly summary notification
     */
    private fun handleWeeklySummary(
        context: Context,
        notificationManager: BillNotificationManager,
        billRepository: BillRepository
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Get upcoming bills for the next 7 days
                val upcomingBills = billRepository.getUpcomingBillsFlow().first()

                if (upcomingBills.isNotEmpty()) {
                    val totalAmount = upcomingBills.sumOf { it.amount }
                    notificationManager.showWeeklySummary(upcomingBills, totalAmount)
                    Log.d(TAG, "Showed weekly summary with ${upcomingBills.size} bills")
                } else {
                    Log.d(TAG, "No upcoming bills for weekly summary")
                }

                // Schedule next weekly summary
                val notificationScheduler = NotificationScheduler(context)
                notificationScheduler.scheduleWeeklySummary()

            } catch (e: Exception) {
                Log.e(TAG, "Error processing weekly summary", e)
            }
        }
    }
}
