package com.example.billmate.notifications

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.annotation.RequiresApi

/**
 * Manages battery optimization settings and provides guidance to users
 */
class BatteryOptimizationManager(private val context: Context) {
    
    companion object {
        private const val TAG = "BatteryOptimizationManager"
    }
    
    private val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
    
    /**
     * Check if the app is whitelisted from battery optimization
     */
    @SuppressLint("BatteryLife")
    fun isIgnoringBatteryOptimizations(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true // No battery optimization on older versions
        }
    }
    
    /**
     * Check if battery optimization is available on this device
     */
    fun isBatteryOptimizationAvailable(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
    }
    
    /**
     * Get intent to request battery optimization whitelist
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun getBatteryOptimizationIntent(): Intent {
        val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
        intent.data = Uri.parse("package:${context.packageName}")
        return intent
    }
    
    /**
     * Get intent to open battery optimization settings
     */
    fun getBatteryOptimizationSettingsIntent(): Intent {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
        } else {
            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${context.packageName}")
            }
        }
    }
    
    /**
     * Check if device is in power save mode
     */
    fun isPowerSaveMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            powerManager.isPowerSaveMode
        } else {
            false
        }
    }
    
    /**
     * Get battery optimization status for UI display
     */
    fun getBatteryOptimizationStatus(): BatteryOptimizationStatus {
        return when {
            !isBatteryOptimizationAvailable() -> BatteryOptimizationStatus.NOT_APPLICABLE
            isIgnoringBatteryOptimizations() -> BatteryOptimizationStatus.OPTIMIZED
            else -> BatteryOptimizationStatus.NOT_OPTIMIZED
        }
    }
    
    /**
     * Get user-friendly description of battery optimization status
     */
    fun getStatusDescription(): String {
        return when (getBatteryOptimizationStatus()) {
            BatteryOptimizationStatus.OPTIMIZED -> "Aplikacija je izvzeta iz optimizacije baterije"
            BatteryOptimizationStatus.NOT_OPTIMIZED -> "Optimizacija baterije lahko vpliva na obvestila"
            BatteryOptimizationStatus.NOT_APPLICABLE -> "Optimizacija baterije ni na voljo"
        }
    }
    
    /**
     * Get recommendation text for user
     */
    fun getRecommendationText(): String? {
        return when (getBatteryOptimizationStatus()) {
            BatteryOptimizationStatus.NOT_OPTIMIZED -> 
                "Za zanesljiva obvestila priporočamo, da aplikacijo izvzamete iz optimizacije baterije."
            else -> null
        }
    }
    
    /**
     * Check if notifications might be affected by power management
     */
    fun areNotificationsAtRisk(): Boolean {
        return when {
            !isBatteryOptimizationAvailable() -> false
            isPowerSaveMode() -> true
            !isIgnoringBatteryOptimizations() -> true
            else -> false
        }
    }
    
    /**
     * Get power management warning for notifications
     */
    fun getPowerManagementWarning(): String? {
        return when {
            isPowerSaveMode() && !isIgnoringBatteryOptimizations() -> 
                "Varčevanje z baterijo je vključeno. Obvestila morda ne bodo zanesljiva."
            isPowerSaveMode() -> 
                "Varčevanje z baterijo je vključeno, vendar je aplikacija izvzeta."
            !isIgnoringBatteryOptimizations() -> 
                "Optimizacija baterije lahko zamakne obvestila."
            else -> null
        }
    }
    
    /**
     * Log battery optimization status for debugging
     */
    fun logBatteryOptimizationStatus() {
        Log.d(TAG, "Battery optimization status:")
        Log.d(TAG, "  - Available: ${isBatteryOptimizationAvailable()}")
        Log.d(TAG, "  - Ignoring optimizations: ${isIgnoringBatteryOptimizations()}")
        Log.d(TAG, "  - Power save mode: ${isPowerSaveMode()}")
        Log.d(TAG, "  - Notifications at risk: ${areNotificationsAtRisk()}")
    }
}

/**
 * Battery optimization status enum
 */
enum class BatteryOptimizationStatus {
    OPTIMIZED,      // App is whitelisted from battery optimization
    NOT_OPTIMIZED,  // App is subject to battery optimization
    NOT_APPLICABLE  // Battery optimization not available on this device
}
