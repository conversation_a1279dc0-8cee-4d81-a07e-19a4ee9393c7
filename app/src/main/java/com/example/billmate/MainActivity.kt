package com.example.billmate

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import com.example.billmate.navigation.AppNavigation
import com.example.billmate.service.GmailService
import com.example.billmate.ui.theme.BillMateTheme
import com.example.billmate.utils.LocaleHelper
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.common.api.ApiException

class MainActivity : ComponentActivity() {
    // Gmail service instance
    private var gmailService: GmailService? = null

    // Activity result launcher for Google Sign-In
    private val signInLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val data = result.data
        Log.d("MainActivity", "Sign-in result received in launcher")

        // Handle the sign-in result
        gmailService?.handleSignInResult(data)
    }

    /**
     * Launch the sign-in intent using the ActivityResultLauncher
     */
    fun launchSignIn(signInIntent: Intent) {
        Log.d("MainActivity", "Launching sign-in intent with ActivityResultLauncher")
        signInLauncher.launch(signInIntent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d("MainActivity", "onCreate with language: ${LocaleHelper.getLanguage(this)}")

        enableEdgeToEdge()
        setContent {
            BillMateTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    AppNavigation(navController = navController)
                }
            }
        }
    }

    /**
     * Set the Gmail service instance.
     */
    fun setGmailService(service: GmailService) {
        gmailService = service
    }

    /**
     * Handle activity result for Google Sign-In.
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // Check if this is a sign-in result
        if (requestCode == GmailService.REQUEST_CODE_SIGN_IN) {
            // Handle the sign-in result
            gmailService?.handleSignInResult(data)
        }
    }

    override fun attachBaseContext(newBase: Context) {
        Log.d("MainActivity", "attachBaseContext")
        val context = LocaleHelper.applyLanguage(newBase)
        super.attachBaseContext(context)
    }

    override fun onResume() {
        super.onResume()
        Log.d("MainActivity", "onResume with language: ${LocaleHelper.getLanguage(this)}")
    }
}