package com.example.billmate.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.example.billmate.repository.BillRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * ViewModel for bill-related operations
 */
class BillViewModel(
    context: Context? = null,
    val billRepository: BillRepository = BillRepository(context)
) : ViewModel() {

    // All bills
    val bills = billRepository.getBillsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Upcoming bills (due in the next 7 days)
    val upcomingBills = billRepository.getUpcomingBillsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Get bills by category
    fun getBillsByCategory(category: BillCategory?): Flow<List<Bill>> {
        return billRepository.getBillsFlow(category)
    }

    // Add a new bill
    fun addBill(bill: Bill, onSuccess: (String) -> Unit, onError: (Throwable) -> Unit) {
        viewModelScope.launch {
            val result = billRepository.addBill(bill)
            if (result.isSuccess) {
                onSuccess(result.getOrNull() ?: "")
            } else {
                onError(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        }
    }

    // Update an existing bill
    fun updateBill(bill: Bill, onSuccess: () -> Unit, onError: (Throwable) -> Unit) {
        viewModelScope.launch {
            val result = billRepository.updateBill(bill)
            if (result.isSuccess) {
                onSuccess()
            } else {
                onError(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        }
    }

    // Delete a bill
    fun deleteBill(billId: String, onSuccess: () -> Unit, onError: (Throwable) -> Unit) {
        viewModelScope.launch {
            val result = billRepository.deleteBill(billId)
            if (result.isSuccess) {
                onSuccess()
            } else {
                onError(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        }
    }

    // Mark a bill as paid or unpaid
    fun markBillPaid(billId: String, isPaid: Boolean, onSuccess: () -> Unit, onError: (Throwable) -> Unit) {
        viewModelScope.launch {
            val result = billRepository.markBillPaid(billId, isPaid)
            if (result.isSuccess) {
                onSuccess()
            } else {
                onError(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        }
    }

    // Get a bill by ID
    fun getBillById(billId: String, onSuccess: (Bill) -> Unit, onError: (Throwable) -> Unit) {
        viewModelScope.launch {
            val result = billRepository.getBillByIdResult(billId)
            if (result.isSuccess) {
                result.getOrNull()?.let { onSuccess(it) }
            } else {
                onError(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        }
    }
}
