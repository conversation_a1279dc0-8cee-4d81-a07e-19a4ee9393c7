package com.example.billmate.ui.screens

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.DocumentScanner
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.utils.AnalyticsManager
import com.example.billmate.utils.OcrProcessor
import com.example.billmate.utils.formatToString
import com.google.android.gms.common.moduleinstall.ModuleInstall
import com.google.android.gms.common.moduleinstall.ModuleInstallRequest
import com.google.mlkit.vision.documentscanner.GmsDocumentScanner
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions.RESULT_FORMAT_JPEG
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions.SCANNER_MODE_FULL
import com.google.mlkit.vision.documentscanner.GmsDocumentScanning
import com.google.mlkit.vision.documentscanner.GmsDocumentScanningResult
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DocumentScanScreen(
    onNavigateBack: () -> Unit,
    onScanComplete: (Map<String, String>) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val ocrProcessor = remember { OcrProcessor() }

    var isProcessing by remember { mutableStateOf(false) }
    var extractedBillInfo by remember { mutableStateOf<OcrProcessor.BillInfo?>(null) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var scannerReady by remember { mutableStateOf(false) }

    // Document scanner options
    val options = remember {
        GmsDocumentScannerOptions.Builder()
            .setScannerMode(SCANNER_MODE_FULL)
            .setResultFormats(RESULT_FORMAT_JPEG)
            .setPageLimit(1) // Single page for bills
            .build()
    }

    val scanner = remember { GmsDocumentScanning.getClient(options) }

    // Activity result launcher for document scanning
    val documentScannerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            val scanningResult = GmsDocumentScanningResult.fromActivityResultIntent(result.data)
            scanningResult?.let { scanResult ->
                Log.d("DocumentScanScreen", "Scan successful, processing ${scanResult.pages?.size} pages")

                // Process the first page
                scanResult.pages?.firstOrNull()?.let { page ->
                    isProcessing = true
                    coroutineScope.launch {
                        try {
                            val billInfo = ocrProcessor.processImageUri(context, page.imageUri)
                            extractedBillInfo = billInfo

                            // Log analytics
                            AnalyticsManager.logDocumentScan(
                                success = true,
                                pageCount = scanResult.pages?.size ?: 0
                            )

                            Log.d("DocumentScanScreen", "OCR processing complete: $billInfo")
                        } catch (e: Exception) {
                            Log.e("DocumentScanScreen", "Error processing scanned document", e)
                            errorMessage = context.getString(R.string.document_scanner_error, e.message)

                            // Log failed processing
                            AnalyticsManager.logDocumentScan(
                                success = false,
                                pageCount = scanResult.pages?.size ?: 0
                            )
                        } finally {
                            isProcessing = false
                        }
                    }
                }
            }
        } else {
            Log.d("DocumentScanScreen", "Document scan cancelled or failed")
            errorMessage = context.getString(R.string.document_scan_cancelled)
        }
    }

    // Check if scanner is ready and install module if needed
    LaunchedEffect(Unit) {
        try {
            val moduleInstallClient = ModuleInstall.getClient(context)
            val moduleInstallRequest = ModuleInstallRequest.newBuilder()
                .addApi(GmsDocumentScanning.getClient(options))
                .build()

            moduleInstallClient.installModules(moduleInstallRequest)
                .addOnSuccessListener {
                    Log.d("DocumentScanScreen", "Document scanner module ready")
                    scannerReady = true
                }
                .addOnFailureListener { e ->
                    Log.e("DocumentScanScreen", "Failed to install document scanner module", e)
                    errorMessage = "Google Play Services required for document scanning"
                }
        } catch (e: Exception) {
            Log.e("DocumentScanScreen", "Error checking document scanner availability", e)
            errorMessage = "Google Play Services required for document scanning"
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.document_scan_title)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when {
                !scannerReady -> {
                    // Loading state while checking scanner availability
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = stringResource(R.string.processing_document),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }

                errorMessage != null -> {
                    // Error state
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onErrorContainer,
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Button(
                                onClick = {
                                    errorMessage = null
                                    scannerReady = false
                                    // Retry initialization
                                }
                            ) {
                                Text(stringResource(R.string.try_again))
                            }
                        }
                    }
                }

                extractedBillInfo != null -> {
                    // Results state
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.document_scanned_successfully),
                                style = MaterialTheme.typography.headlineSmall,
                                modifier = Modifier.padding(bottom = 16.dp)
                            )

                            Text(
                                text = stringResource(R.string.extracted_information),
                                style = MaterialTheme.typography.titleLarge,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            extractedBillInfo?.let { info ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.title)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(text = info.title)
                                }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.bill_amount)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(text = info.amount?.toString() ?: stringResource(R.string.no_date))
                                }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.bill_due_date)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(
                                        text = info.dueDate?.formatToString() ?: stringResource(R.string.no_date)
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Action buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Button(
                            onClick = {
                                // Use the extracted data
                                val data = mutableMapOf<String, String>()

                                extractedBillInfo?.let { info ->
                                    data["title"] = info.title
                                    info.amount?.let { data["amount"] = it.toString() }
                                    info.dueDate?.let { data["dueDate"] = it.time.toString() }
                                }

                                Log.d("DocumentScanScreen", "Passing data to navigation: $data")
                                onScanComplete(data)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Check, contentDescription = null)
                            Text(text = stringResource(R.string.use_data), modifier = Modifier.padding(start = 8.dp))
                        }

                        Button(
                            onClick = {
                                // Reset and try again
                                extractedBillInfo = null
                                errorMessage = null
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Close, contentDescription = null)
                            Text(text = stringResource(R.string.try_again), modifier = Modifier.padding(start = 8.dp))
                        }
                    }
                }

                else -> {
                    // Initial state - ready to scan
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                Icons.Default.DocumentScanner,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Text(
                                text = stringResource(R.string.document_scan_title),
                                style = MaterialTheme.typography.headlineSmall,
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = stringResource(R.string.document_scanner_description),
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Spacer(modifier = Modifier.height(24.dp))

                            Button(
                                onClick = {
                                    scanner.getStartScanIntent(context as Activity)
                                        .addOnSuccessListener { intentSender ->
                                            documentScannerLauncher.launch(
                                                androidx.activity.result.IntentSenderRequest.Builder(intentSender).build()
                                            )
                                        }
                                        .addOnFailureListener { e ->
                                            Log.e("DocumentScanScreen", "Failed to start document scanner", e)
                                            errorMessage = "Document scan failed"
                                        }
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(Icons.Default.DocumentScanner, contentDescription = null)
                                Text(
                                    text = stringResource(R.string.scan_document),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }
                }
            }

            if (isProcessing) {
                Spacer(modifier = Modifier.height(16.dp))
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(R.string.processing_document),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}
