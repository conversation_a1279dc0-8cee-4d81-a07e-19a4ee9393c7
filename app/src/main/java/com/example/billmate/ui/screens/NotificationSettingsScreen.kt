package com.example.billmate.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.NotificationsOff
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerDefaults
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.notifications.BatteryOptimizationManager
import com.example.billmate.notifications.DoNotDisturbManager
import com.example.billmate.notifications.NotificationPreferences
import com.example.billmate.notifications.VibrationPattern

/**
 * Screen for managing notification settings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val notificationPreferences = remember { NotificationPreferences(context) }
    val settings by notificationPreferences.settings.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Nastavitve obvestil") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Nazaj")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Main notification toggle
            NotificationSettingCard(
                title = "Omogoči obvestila",
                subtitle = "Prejemaj opominnike za zapadle račune",
                icon = Icons.Default.Notifications,
                checked = settings.notificationsEnabled,
                onCheckedChange = { notificationPreferences.setNotificationsEnabled(it) }
            )

            if (settings.notificationsEnabled) {
                // Due date reminders section
                SectionHeader("Opomniki za zapadlost")

                NotificationSettingItem(
                    title = "3 dni pred zapadlostjo",
                    subtitle = "Zgodnji opomnik za načrtovanje",
                    checked = settings.remind3Days,
                    onCheckedChange = { notificationPreferences.setRemind3Days(it) }
                )

                NotificationSettingItem(
                    title = "1 dan pred zapadlostjo",
                    subtitle = "Končni opomnik za plačilo",
                    checked = settings.remind1Day,
                    onCheckedChange = { notificationPreferences.setRemind1Day(it) }
                )

                NotificationSettingItem(
                    title = "Na dan zapadlosti",
                    subtitle = "Nujno obvestilo na dan zapadlosti",
                    checked = settings.remindSameDay,
                    onCheckedChange = { notificationPreferences.setRemindSameDay(it) }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Overdue alerts section
                SectionHeader("Opozorila za zapadle račune")

                NotificationSettingItem(
                    title = "Obvestila za zapadle račune",
                    subtitle = "Opozori me, ko računi zapadejo",
                    checked = settings.overdueNotifications,
                    onCheckedChange = { notificationPreferences.setOverdueNotifications(it) }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Weekly summary section
                SectionHeader("Tedenski pregled")

                NotificationSettingItem(
                    title = "Tedenski pregled računov",
                    subtitle = "Pregled vseh računov za naslednji teden (nedelja, 19:00)",
                    checked = settings.weeklySummary,
                    onCheckedChange = { notificationPreferences.setWeeklySummary(it) }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Sound and vibration section
                SectionHeader("Zvok in vibracije")

                NotificationSettingItem(
                    title = "Zvok obvestil",
                    subtitle = "Predvajaj zvok ob obvestilih",
                    checked = settings.soundEnabled,
                    onCheckedChange = { notificationPreferences.setSoundEnabled(it) }
                )

                NotificationSettingItem(
                    title = "Vibracije",
                    subtitle = "Vibriraj ob obvestilih",
                    checked = settings.vibrationEnabled,
                    onCheckedChange = { notificationPreferences.setVibrationEnabled(it) }
                )

                if (settings.vibrationEnabled) {
                    VibrationPatternCard(
                        currentPattern = settings.vibrationPattern,
                        onPatternChange = { notificationPreferences.setVibrationPattern(it) }
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Notification time section
                SectionHeader("Čas obvestil")

                NotificationTimeCard(
                    hour = settings.notificationHour,
                    minute = settings.notificationMinute,
                    onTimeChange = { hour, minute ->
                        notificationPreferences.setNotificationTime(hour, minute)
                    }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // System status section
                SectionHeader("Sistemski status")

                SystemStatusCard()
            }
        }
    }
}

@Composable
private fun NotificationSettingCard(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (checked) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Column(
                    modifier = Modifier.padding(start = 12.dp)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange
            )
        }
    }
}

@Composable
private fun NotificationSettingItem(
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
private fun NotificationTimeCard(
    hour: Int,
    minute: Int,
    onTimeChange: (Int, Int) -> Unit
) {
    var showTimePicker by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Čas dnevnih obvestil",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "Obvestila bodo poslana ob ${String.format("%02d:%02d", hour, minute)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                TextButton(
                    onClick = { showTimePicker = true }
                ) {
                    Text("Spremeni")
                }
            }
        }
    }

    // Time picker dialog
    if (showTimePicker) {
        TimePickerDialog(
            initialHour = hour,
            initialMinute = minute,
            onTimeSelected = { selectedHour, selectedMinute ->
                onTimeChange(selectedHour, selectedMinute)
                showTimePicker = false
            },
            onDismiss = { showTimePicker = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TimePickerDialog(
    initialHour: Int,
    initialMinute: Int,
    onTimeSelected: (Int, Int) -> Unit,
    onDismiss: () -> Unit
) {
    val timePickerState = rememberTimePickerState(
        initialHour = initialHour,
        initialMinute = initialMinute,
        is24Hour = true
    )

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier.padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Izberi čas obvestil",
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                TimePicker(
                    state = timePickerState,
                    colors = TimePickerDefaults.colors()
                )

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Prekliči")
                    }

                    TextButton(
                        onClick = {
                            onTimeSelected(timePickerState.hour, timePickerState.minute)
                        }
                    ) {
                        Text("Potrdi")
                    }
                }
            }
        }
    }
}

@Composable
private fun VibrationPatternCard(
    currentPattern: VibrationPattern,
    onPatternChange: (VibrationPattern) -> Unit
) {
    var showDropdown by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Vzorec vibracij",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "Trenutno: ${currentPattern.displayName}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                Box {
                    TextButton(
                        onClick = { showDropdown = true }
                    ) {
                        Text("Spremeni")
                    }

                    DropdownMenu(
                        expanded = showDropdown,
                        onDismissRequest = { showDropdown = false }
                    ) {
                        VibrationPattern.values().forEach { pattern ->
                            DropdownMenuItem(
                                text = { Text(pattern.displayName) },
                                onClick = {
                                    onPatternChange(pattern)
                                    showDropdown = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SystemStatusCard() {
    val context = LocalContext.current
    val dndManager = remember { DoNotDisturbManager(context) }
    val batteryOptimizationManager = remember { BatteryOptimizationManager(context) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Status sistema",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Do Not Disturb status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Ne moti",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = dndManager.getDndStatusDescription(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Icon(
                    imageVector = if (dndManager.shouldSuppressNotifications()) {
                        Icons.Default.NotificationsOff
                    } else {
                        Icons.Default.Notifications
                    },
                    contentDescription = null,
                    tint = if (dndManager.shouldSuppressNotifications()) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.primary
                    }
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Battery optimization status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Optimizacija baterije",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = batteryOptimizationManager.getStatusDescription(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    // Show recommendation if needed
                    batteryOptimizationManager.getRecommendationText()?.let { recommendation ->
                        Text(
                            text = recommendation,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.error,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                Icon(
                    imageVector = when (batteryOptimizationManager.getBatteryOptimizationStatus()) {
                        com.example.billmate.notifications.BatteryOptimizationStatus.OPTIMIZED -> Icons.Default.CheckCircle
                        com.example.billmate.notifications.BatteryOptimizationStatus.NOT_OPTIMIZED -> Icons.Default.Warning
                        com.example.billmate.notifications.BatteryOptimizationStatus.NOT_APPLICABLE -> Icons.Default.CheckCircle
                    },
                    contentDescription = null,
                    tint = when (batteryOptimizationManager.getBatteryOptimizationStatus()) {
                        com.example.billmate.notifications.BatteryOptimizationStatus.OPTIMIZED -> MaterialTheme.colorScheme.primary
                        com.example.billmate.notifications.BatteryOptimizationStatus.NOT_OPTIMIZED -> MaterialTheme.colorScheme.error
                        com.example.billmate.notifications.BatteryOptimizationStatus.NOT_APPLICABLE -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        }
    }
}

@Composable
private fun SectionHeader(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}
