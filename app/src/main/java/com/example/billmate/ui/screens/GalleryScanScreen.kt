package com.example.billmate.ui.screens

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.utils.AnalyticsManager
import com.example.billmate.utils.OcrProcessor
import com.example.billmate.utils.formatToString
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GalleryScanScreen(
    onNavigateBack: () -> Unit,
    onScanComplete: (Map<String, String>) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val ocrProcessor = remember { OcrProcessor() }

    var isProcessing by remember { mutableStateOf(false) }
    var extractedBillInfo by remember { mutableStateOf<OcrProcessor.BillInfo?>(null) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }

    // Image picker launcher
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        if (uri != null) {
            selectedImageUri = uri
            isProcessing = true
            errorMessage = null

            coroutineScope.launch {
                try {
                    Log.d("GalleryScanScreen", "Processing selected image: $uri")
                    val billInfo = ocrProcessor.processImageUri(context, uri)
                    extractedBillInfo = billInfo

                    // Log analytics
                    AnalyticsManager.logGalleryScan(
                        success = true,
                        hasResults = billInfo.title.isNotEmpty() || billInfo.amount != null
                    )

                    Log.d("GalleryScanScreen", "OCR processing complete: $billInfo")
                } catch (e: Exception) {
                    Log.e("GalleryScanScreen", "Error processing gallery image", e)
                    errorMessage = context.getString(R.string.image_processing_failed)

                    // Log failed processing
                    AnalyticsManager.logGalleryScan(
                        success = false,
                        hasResults = false
                    )
                } finally {
                    isProcessing = false
                }
            }
        } else {
            errorMessage = context.getString(R.string.no_image_selected)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.gallery_scan_title)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when {
                errorMessage != null -> {
                    // Error state
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onErrorContainer,
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Button(
                                onClick = {
                                    errorMessage = null
                                    selectedImageUri = null
                                    extractedBillInfo = null
                                }
                            ) {
                                Text(stringResource(R.string.try_again))
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }

                extractedBillInfo != null -> {
                    // Results state
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.image_selected_successfully),
                                style = MaterialTheme.typography.headlineSmall,
                                modifier = Modifier.padding(bottom = 16.dp)
                            )

                            Text(
                                text = stringResource(R.string.extracted_information),
                                style = MaterialTheme.typography.titleLarge,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            extractedBillInfo?.let { info ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.title)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(text = info.title)
                                }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.bill_amount)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(text = info.amount?.toString() ?: stringResource(R.string.no_date))
                                }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.bill_due_date)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(
                                        text = info.dueDate?.formatToString() ?: stringResource(R.string.no_date)
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Action buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Button(
                            onClick = {
                                // Use the extracted data
                                val data = mutableMapOf<String, String>()

                                extractedBillInfo?.let { info ->
                                    data["title"] = info.title
                                    info.amount?.let { data["amount"] = it.toString() }
                                    info.dueDate?.let { data["dueDate"] = it.time.toString() }
                                }

                                Log.d("GalleryScanScreen", "Passing data to navigation: $data")
                                onScanComplete(data)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Check, contentDescription = null)
                            Text(text = stringResource(R.string.use_data), modifier = Modifier.padding(start = 8.dp))
                        }

                        Button(
                            onClick = {
                                // Reset and try again
                                extractedBillInfo = null
                                selectedImageUri = null
                                errorMessage = null
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Close, contentDescription = null)
                            Text(text = stringResource(R.string.try_again), modifier = Modifier.padding(start = 8.dp))
                        }
                    }
                }

                else -> {
                    // Initial state - ready to select image
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                Icons.Default.PhotoLibrary,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Text(
                                text = stringResource(R.string.gallery_scan_title),
                                style = MaterialTheme.typography.headlineSmall,
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = stringResource(R.string.gallery_scanner_description),
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Spacer(modifier = Modifier.height(24.dp))

                            Button(
                                onClick = {
                                    imagePickerLauncher.launch("image/*")
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(Icons.Default.Image, contentDescription = null)
                                Text(
                                    text = stringResource(R.string.select_bill_image),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }
                }
            }

            if (isProcessing) {
                Spacer(modifier = Modifier.height(16.dp))
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(R.string.processing_image_from_gallery),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}
