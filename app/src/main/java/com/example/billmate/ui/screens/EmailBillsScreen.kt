package com.example.billmate.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.model.Bill
import com.example.billmate.model.EmailBillExtraction
import com.example.billmate.model.EmailMessage
import com.example.billmate.repository.EmailRepository
import com.example.billmate.ui.components.BottomNavBar
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Screen for displaying and managing bills extracted from emails.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailBillsScreen(
    onNavigateBack: () -> Unit,
    navController: androidx.navigation.NavController,
    emailRepository: EmailRepository
) {
    var isLoading by remember { mutableStateOf(false) }
    var emails by remember { mutableStateOf<List<EmailMessage>?>(null) }
    var extractions by remember { mutableStateOf<List<EmailBillExtraction>?>(null) }
    var savedBills by remember { mutableStateOf<List<Bill>?>(null) }
    var error by remember { mutableStateOf<String?>(null) }

    val coroutineScope = rememberCoroutineScope()

    // Load emails when the screen is first displayed
    LaunchedEffect(key1 = Unit) {
        loadEmails(emailRepository) { result ->
            isLoading = false
            emails = result
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.email_bills)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                            coroutineScope.launch {
                                isLoading = true
                                loadEmails(emailRepository) { result ->
                                    isLoading = false
                                    emails = result
                                    extractions = null
                                    savedBills = null
                                }
                            }
                        },
                        enabled = !isLoading
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = stringResource(R.string.refresh)
                        )
                    }
                }
            )
        },
        floatingActionButton = {
            if (emails != null && extractions == null) {
                FloatingActionButton(
                    onClick = {
                        coroutineScope.launch {
                            isLoading = true
                            extractBills(emailRepository, emails!!) { result ->
                                isLoading = false
                                extractions = result
                            }
                        }
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Email,
                        contentDescription = stringResource(R.string.extract_bills)
                    )
                }
            }
        },
        bottomBar = {
            BottomNavBar(navController = navController)
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> LoadingContent()
                error != null -> ErrorContent(error!!) {
                    error = null
                    coroutineScope.launch {
                        isLoading = true
                        loadEmails(emailRepository) { result ->
                            isLoading = false
                            emails = result
                        }
                    }
                }
                savedBills != null -> SavedBillsContent(savedBills!!, onNavigateBack)
                extractions != null -> ExtractionsContent(extractions!!) { selectedExtractions ->
                    coroutineScope.launch {
                        isLoading = true
                        saveBills(emailRepository, selectedExtractions) { result ->
                            isLoading = false
                            savedBills = result
                        }
                    }
                }
                emails != null -> EmailsContent(emails!!) {
                    coroutineScope.launch {
                        isLoading = true
                        extractBills(emailRepository, emails!!) { result ->
                            isLoading = false
                            extractions = result
                        }
                    }
                }
                else -> EmptyContent {
                    coroutineScope.launch {
                        isLoading = true
                        loadEmails(emailRepository) { result ->
                            isLoading = false
                            emails = result
                        }
                    }
                }
            }
        }
    }
}

/**
 * Load emails from the repository.
 */
private suspend fun loadEmails(
    emailRepository: EmailRepository,
    onResult: (List<EmailMessage>?) -> Unit
) {
    try {
        val result = emailRepository.getBillEmails()

        if (result.isSuccess) {
            onResult(result.getOrThrow())
        } else {
            onResult(null)
        }
    } catch (e: Exception) {
        onResult(null)
    }
}

/**
 * Extract bills from emails.
 */
private suspend fun extractBills(
    emailRepository: EmailRepository,
    emails: List<EmailMessage>,
    onResult: (List<EmailBillExtraction>?) -> Unit
) {
    try {
        val result = emailRepository.extractBillsFromEmails(emails)

        if (result.isSuccess) {
            onResult(result.getOrThrow())
        } else {
            onResult(null)
        }
    } catch (e: Exception) {
        onResult(null)
    }
}

/**
 * Save extracted bills to Firestore.
 */
private suspend fun saveBills(
    emailRepository: EmailRepository,
    extractions: List<EmailBillExtraction>,
    onResult: (List<Bill>?) -> Unit
) {
    try {
        val result = emailRepository.saveExtractedBills(extractions)

        if (result.isSuccess) {
            onResult(result.getOrThrow())
        } else {
            onResult(null)
        }
    } catch (e: Exception) {
        onResult(null)
    }
}

/**
 * Content to display when loading.
 */
@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.loading),
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

/**
 * Content to display when there's an error.
 */
@Composable
private fun ErrorContent(error: String, onRetry: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.error),
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = error,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Button(onClick = onRetry) {
            Text(stringResource(R.string.try_again))
        }
    }
}

/**
 * Content to display when there are no emails or extractions.
 */
@Composable
private fun EmptyContent(onRefresh: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.no_emails_found),
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = stringResource(R.string.no_emails_found_description),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Button(onClick = onRefresh) {
            Text(stringResource(R.string.refresh))
        }
    }
}

/**
 * Content to display when emails are loaded.
 */
@Composable
private fun EmailsContent(emails: List<EmailMessage>, onExtract: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = stringResource(R.string.found_emails, emails.size),
            style = MaterialTheme.typography.headlineSmall
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = stringResource(R.string.extract_bills_description),
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(16.dp))

        Button(
            onClick = onExtract,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        ) {
            Text(stringResource(R.string.extract_bills))
        }

        Spacer(modifier = Modifier.height(16.dp))

        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(emails) { email ->
                EmailItem(email)
            }
        }
    }
}

/**
 * Content to display when bill extractions are available.
 */
@Composable
private fun ExtractionsContent(
    extractions: List<EmailBillExtraction>,
    onSave: (List<EmailBillExtraction>) -> Unit
) {
    var selectedExtractions by remember { mutableStateOf(extractions.toSet()) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = stringResource(R.string.extracted_bills, extractions.size),
            style = MaterialTheme.typography.headlineSmall
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = stringResource(R.string.review_and_save_bills),
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(16.dp))

        Button(
            onClick = { onSave(selectedExtractions.toList()) },
            modifier = Modifier.align(Alignment.CenterHorizontally),
            enabled = selectedExtractions.isNotEmpty()
        ) {
            Text(stringResource(R.string.save_selected_bills, selectedExtractions.size))
        }

        Spacer(modifier = Modifier.height(16.dp))

        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(extractions) { extraction ->
                ExtractionItem(
                    extraction = extraction,
                    isSelected = selectedExtractions.contains(extraction),
                    onToggleSelection = { isSelected ->
                        selectedExtractions = if (isSelected) {
                            selectedExtractions + extraction
                        } else {
                            selectedExtractions - extraction
                        }
                    }
                )
            }
        }
    }
}

/**
 * Content to display when bills are saved.
 */
@Composable
private fun SavedBillsContent(bills: List<Bill>, onDone: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Check,
            contentDescription = null,
            modifier = Modifier.height(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(R.string.bills_saved_successfully, bills.size),
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = stringResource(R.string.bills_saved_description),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = onDone,
            modifier = Modifier.fillMaxWidth(0.8f)
        ) {
            Text(stringResource(R.string.done))
        }

        Spacer(modifier = Modifier.height(24.dp))

        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(bills) { bill ->
                SavedBillItem(bill)
            }
        }
    }
}

/**
 * Item to display an email.
 */
@Composable
private fun EmailItem(email: EmailMessage) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = email.subject,
                style = MaterialTheme.typography.titleMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = email.from,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(email.receivedDate),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

/**
 * Item to display a bill extraction.
 */
@Composable
private fun ExtractionItem(
    extraction: EmailBillExtraction,
    isSelected: Boolean,
    onToggleSelection: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = extraction.title ?: stringResource(R.string.unknown_bill),
                    style = MaterialTheme.typography.titleMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = stringResource(R.string.amount, "€${extraction.amount ?: 0.0}"),
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = stringResource(
                        R.string.due_date,
                        extraction.dueDate?.let {
                            SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(it)
                        } ?: stringResource(R.string.no_date)
                    ),
                    style = MaterialTheme.typography.bodySmall
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = stringResource(R.string.confidence, (extraction.confidence * 100).toInt()),
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            IconButton(
                onClick = { onToggleSelection(!isSelected) }
            ) {
                Icon(
                    imageVector = if (isSelected) Icons.Default.Check else Icons.Default.Close,
                    contentDescription = if (isSelected) stringResource(R.string.deselect) else stringResource(R.string.select),
                    tint = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * Item to display a saved bill.
 */
@Composable
private fun SavedBillItem(bill: Bill) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = bill.title,
                style = MaterialTheme.typography.titleMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = stringResource(R.string.amount, "€${bill.amount}"),
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = stringResource(
                    R.string.due_date,
                    bill.dueDate?.let {
                        SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(it)
                    } ?: stringResource(R.string.no_date)
                ),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}
