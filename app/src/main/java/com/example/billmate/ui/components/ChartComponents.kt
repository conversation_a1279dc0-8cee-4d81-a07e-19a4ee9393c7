package com.example.billmate.ui.components

import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.example.billmate.model.Bill
import com.example.billmate.utils.ChartUtils
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.PieChart

/**
 * Composable for displaying a pie chart of bill categories.
 */
@Composable
fun CategoryPieChart(
    bills: List<Bill>,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp),
        factory = { context ->
            Pie<PERSON>hart(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                ChartUtils.setupCategoryPieChart(this)
            }
        },
        update = { pieChart ->
            if (bills.isNotEmpty()) {
                val pieData = ChartUtils.createCategoryPieData(bills, pieChart)
                pieChart.data = pieData
                pieChart.invalidate() // Refresh chart
            }
        }
    )
}

/**
 * Composable for displaying a bar chart of monthly spending.
 */
@Composable
fun MonthlyBarChart(
    bills: List<Bill>,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    // Remember the chart to avoid recreating it on each recomposition
    val barChart = remember {
        BarChart(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            ChartUtils.setupMonthlyBarChart(this)
        }
    }

    // Update chart data when bills change
    DisposableEffect(bills) {
        if (bills.isNotEmpty()) {
            val barData = ChartUtils.createMonthlyBarData(bills, barChart)
            barChart.data = barData
            barChart.invalidate() // Refresh chart
        }

        onDispose { }
    }

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp),
        factory = { barChart }
    )
}

/**
 * Composable for displaying a bar chart of paid vs unpaid bills.
 */
@Composable
fun StatusBarChart(
    bills: List<Bill>,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(250.dp),
        factory = { context ->
            BarChart(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                ChartUtils.setupMonthlyBarChart(this) // Reuse the same setup
            }
        },
        update = { barChart ->
            if (bills.isNotEmpty()) {
                val barData = ChartUtils.createStatusBarData(bills, barChart)
                barChart.data = barData
                barChart.invalidate() // Refresh chart
            }
        }
    )
}
