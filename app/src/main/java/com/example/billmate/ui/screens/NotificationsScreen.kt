package com.example.billmate.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material3.Card
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TimeInput
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerDefaults
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.billmate.R
import com.example.billmate.service.NotificationService
import com.example.billmate.utils.PermissionManager
import com.example.billmate.utils.RequestNotificationPermission
import kotlin.math.roundToInt

/**
 * Screen for managing notification settings.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationsScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val notificationService = remember { NotificationService(context) }

    // State for notification settings
    var notificationsEnabled by remember {
        mutableStateOf(notificationService.areNotificationsEnabled())
    }
    var dueDateNotificationsEnabled by remember {
        mutableStateOf(notificationService.areDueDateNotificationsEnabled())
    }
    var upcomingNotificationsEnabled by remember {
        mutableStateOf(notificationService.areUpcomingNotificationsEnabled())
    }
    var daysBefore by remember {
        mutableFloatStateOf(notificationService.getNotificationDaysBefore().toFloat())
    }

    // State for time picker
    var showTimePicker by remember { mutableStateOf(false) }
    val timePickerState = rememberTimePickerState(
        initialHour = notificationService.getNotificationHour(),
        initialMinute = notificationService.getNotificationMinute(),
        is24Hour = true
    )

    // State for permission
    var hasNotificationPermission by remember {
        mutableStateOf(PermissionManager.hasNotificationPermission(context))
    }

    // Request notification permission if needed
    RequestNotificationPermission { isGranted ->
        hasNotificationPermission = isGranted
        if (!isGranted) {
            // Show a toast to inform the user that notifications won't work without permission
            android.widget.Toast.makeText(
                context,
                context.getString(R.string.notification_permission_required),
                android.widget.Toast.LENGTH_LONG
            ).show()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.notifications)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.notification_settings),
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Enable/disable all notifications
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.enable_notifications),
                            style = MaterialTheme.typography.bodyLarge
                        )

                        Switch(
                            checked = notificationsEnabled,
                            onCheckedChange = { enabled ->
                                notificationsEnabled = enabled
                                notificationService.setNotificationsEnabled(enabled)
                            }
                        )
                    }

                    Divider(modifier = Modifier.padding(vertical = 16.dp))

                    // Due date notifications
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.due_date_notifications),
                            style = MaterialTheme.typography.bodyLarge
                        )

                        Switch(
                            checked = dueDateNotificationsEnabled,
                            onCheckedChange = { enabled ->
                                dueDateNotificationsEnabled = enabled
                                notificationService.setDueDateNotificationsEnabled(enabled)
                            },
                            enabled = notificationsEnabled
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Upcoming bill notifications
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.upcoming_bill_notifications),
                            style = MaterialTheme.typography.bodyLarge
                        )

                        Switch(
                            checked = upcomingNotificationsEnabled,
                            onCheckedChange = { enabled ->
                                upcomingNotificationsEnabled = enabled
                                notificationService.setUpcomingNotificationsEnabled(enabled)
                            },
                            enabled = notificationsEnabled
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Days before due date slider
                    Text(
                        text = stringResource(R.string.days_before_due_date, daysBefore.roundToInt()),
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Slider(
                        value = daysBefore,
                        onValueChange = { value ->
                            daysBefore = value
                            notificationService.setNotificationDaysBefore(value.roundToInt())
                        },
                        valueRange = 1f..7f,
                        steps = 5,
                        enabled = notificationsEnabled && upcomingNotificationsEnabled,
                        modifier = Modifier.padding(top = 8.dp)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Notification time
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(enabled = notificationsEnabled) {
                                showTimePicker = true
                            },
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Schedule,
                                contentDescription = null
                            )
                            Text(
                                text = stringResource(R.string.notification_time),
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }

                        Text(
                            text = formatTime(
                                notificationService.getNotificationHour(),
                                notificationService.getNotificationMinute()
                            ),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Permission warning if needed
                    if (!hasNotificationPermission) {
                        Spacer(modifier = Modifier.height(16.dp))

                        androidx.compose.material3.Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = androidx.compose.material3.CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.notification_permission_required),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onErrorContainer
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                androidx.compose.material3.Button(
                                    onClick = {
                                        val intent = PermissionManager.getAppSettingsIntent(context)
                                        context.startActivity(intent)
                                    },
                                    modifier = Modifier.align(Alignment.End)
                                ) {
                                    Text(text = stringResource(R.string.open_settings))
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Test notification button
                    androidx.compose.material3.Button(
                        onClick = {
                            notificationService.sendTestNotification()
                            android.widget.Toast.makeText(
                                context,
                                context.getString(R.string.test_notification_sent),
                                android.widget.Toast.LENGTH_SHORT
                            ).show()
                        },
                        enabled = notificationsEnabled && hasNotificationPermission,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(text = stringResource(R.string.test_notification))
                    }
                }
            }
        }
    }

    // Show time picker dialog if needed
    if (showTimePicker) {
        TimePickerDialog(
            timePickerState = timePickerState,
            onDismiss = { showTimePicker = false },
            onConfirm = {
                notificationService.setNotificationHour(timePickerState.hour)
                notificationService.setNotificationMinute(timePickerState.minute)
                showTimePicker = false
                // Show a toast to confirm the time has been set
                android.widget.Toast.makeText(
                    context,
                    context.getString(R.string.notification_time_set,
                        formatTime(timePickerState.hour, timePickerState.minute)),
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        )
    }
}

/**
 * Format the time as a string.
 */
private fun formatTime(hour: Int, minute: Int): String {
    val hourString = if (hour < 10) "0$hour" else "$hour"
    val minuteString = if (minute < 10) "0$minute" else "$minute"
    return "$hourString:$minuteString"
}

/**
 * Time picker dialog.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TimePickerDialog(
    timePickerState: TimePickerState,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.select_notification_time),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                TimePicker(
                    state = timePickerState,
                    colors = TimePickerDefaults.colors()
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss
                    ) {
                        Text(stringResource(R.string.cancel))
                    }

                    androidx.compose.material3.TextButton(
                        onClick = onConfirm
                    ) {
                        Text(stringResource(R.string.ok))
                    }
                }
            }
        }
    }
}
