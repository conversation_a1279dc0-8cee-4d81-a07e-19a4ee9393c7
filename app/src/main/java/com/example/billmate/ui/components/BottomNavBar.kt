package com.example.billmate.ui.components

import android.util.Log
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.automirrored.filled.List

import androidx.compose.material.icons.filled.BarChart
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.example.billmate.R
import com.example.billmate.navigation.Screen

/**
 * Bottom navigation bar for the app
 */
@Composable
fun BottomNavBar(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    val items = listOf(
        BottomNavItem(
            route = Screen.Home.route,
            titleResId = R.string.home,
            icon = Icons.Default.Home
        ),

        BottomNavItem(
            route = Screen.BillList.route,
            titleResId = R.string.bills,
            icon = Icons.AutoMirrored.Filled.List
        ),
        BottomNavItem(
            route = Screen.Statistics.route,
            titleResId = R.string.statistics,
            icon = Icons.Default.BarChart
        ),
        BottomNavItem(
            route = Screen.Profile.route,
            titleResId = R.string.profile,
            icon = Icons.Default.Person
        )
    )

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    // Log the current destination
    Log.d("BottomNavBar", "Current destination: ${currentDestination?.route}")

    NavigationBar(
        modifier = modifier
    ) {
        items.forEach { item ->
            val selected = currentDestination?.hierarchy?.any { it.route == item.route } == true
            NavigationBarItem(
                icon = { Icon(item.icon, contentDescription = stringResource(id = item.titleResId)) },
                label = { Text(stringResource(id = item.titleResId)) },
                selected = selected,
                onClick = {
                    // Always navigate, even if selected, to ensure consistent behavior
                    // Log the navigation attempt
                    Log.d("BottomNavBar", "Navigating to route: ${item.route}")

                    // For Home route, always navigate to ensure it's properly displayed
                    if (item.route == Screen.Home.route) {
                        // Clear the back stack and navigate to Home
                        navController.navigate(item.route) {
                            // Pop up to the start destination and clear the back stack
                            popUpTo(navController.graph.findStartDestination().id) {
                                inclusive = true
                            }
                            // Avoid multiple copies of the same destination
                            launchSingleTop = true
                        }
                    } else if (!selected) {
                        // For other routes, only navigate if not already selected
                        navController.navigate(item.route) {
                            // Pop up to the start destination of the graph to
                            // avoid building up a large stack of destinations
                            popUpTo(navController.graph.findStartDestination().id) {
                                saveState = true
                            }
                            // Avoid multiple copies of the same destination when
                            // reselecting the same item
                            launchSingleTop = true
                            // Restore state when reselecting a previously selected item
                            restoreState = true
                        }
                    }
                }
            )
        }
    }
}

/**
 * Data class for bottom navigation items
 */
data class BottomNavItem(
    val route: String,
    val titleResId: Int,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
)
