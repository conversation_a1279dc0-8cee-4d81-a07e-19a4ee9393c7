package com.example.billmate.ui.screens

import android.app.Activity
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.foundation.clickable
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.repository.AuthRepository
import com.example.billmate.utils.AnalyticsManager
import com.example.billmate.utils.GoogleSignInHelper
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.common.api.ApiException
import kotlinx.coroutines.launch

@Composable
fun LoginScreen(
    onNavigateToRegister: () -> Unit,
    onNavigateToForgotPassword: () -> Unit,
    onLoginSuccess: () -> Unit,
    authRepository: AuthRepository = AuthRepository()
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    var isGoogleSignInLoading by remember { mutableStateOf(false) }

    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // Google Sign-In helper
    val googleSignInHelper = remember { GoogleSignInHelper(context) }

    // Activity result launcher for Google Sign-In
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
            try {
                // Get the account from the Google Sign-In result
                val account = googleSignInHelper.handleSignInResult(task)
                Log.d("LoginScreen", "Google Sign-In result: ${account != null}")

                if (account != null) {
                    isGoogleSignInLoading = true
                    Log.d("LoginScreen", "Google account email: ${account.email}, id: ${account.id}, displayName: ${account.displayName}")

                    // Try to get Firebase credential from Google account
                    val credential = googleSignInHelper.getAuthCredential(account)
                    Log.d("LoginScreen", "Google credential: ${credential != null}")

                    if (credential != null) {
                        // Use Firebase Authentication with Google credential
                        coroutineScope.launch {
                            try {
                                Log.d("LoginScreen", "Attempting to sign in with Google credential")
                                val authResult = authRepository.signInWithGoogle(credential)
                                isGoogleSignInLoading = false

                                if (authResult.isSuccess) {
                                    // Log analytics event
                                    Log.d("LoginScreen", "Google sign-in success")
                                    AnalyticsManager.logLogin("google")
                                    onLoginSuccess()
                                } else {
                                    val errorMsg = authResult.exceptionOrNull()?.message ?: "Google sign-in failed"
                                    Log.e("LoginScreen", "Google sign-in failed: $errorMsg")
                                    snackbarHostState.showSnackbar(errorMsg)
                                }
                            } catch (e: Exception) {
                                Log.e("LoginScreen", "Google sign-in exception", e)
                                isGoogleSignInLoading = false
                                snackbarHostState.showSnackbar("Google sign-in failed: ${e.message}")
                            }
                        }
                    } else {
                        // Fallback to email-based authentication if credential is null
                        val email = account.email ?: ""
                        val displayName = account.displayName ?: ""

                        Log.d("LoginScreen", "Using fallback email authentication with email: $email")

                        if (email.isNotEmpty()) {
                            coroutineScope.launch {
                                try {
                                    Log.d("LoginScreen", "Attempting to sign in with email: $email")
                                    // Try to sign in with email
                                    val result = authRepository.signInWithEmailPassword(email, "google-auth-password")

                                    if (result.isSuccess) {
                                        // Log analytics event
                                        Log.d("LoginScreen", "Email sign-in success")
                                        AnalyticsManager.logLogin("google")
                                        isGoogleSignInLoading = false
                                        onLoginSuccess()
                                    } else {
                                        // If sign-in fails, try to create an account
                                        Log.d("LoginScreen", "Email sign-in failed, attempting to create account")
                                        val createResult = authRepository.createUserWithEmailPassword(
                                            email = email,
                                            password = "google-auth-password",
                                            displayName = displayName
                                        )

                                        isGoogleSignInLoading = false
                                        if (createResult.isSuccess) {
                                            // Log analytics event
                                            Log.d("LoginScreen", "Account creation success")
                                            AnalyticsManager.logSignUp("google")
                                            onLoginSuccess()
                                        } else {
                                            val errorMsg = "Failed to create account with Google: ${createResult.exceptionOrNull()?.message}"
                                            Log.e("LoginScreen", errorMsg)
                                            snackbarHostState.showSnackbar(errorMsg)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("LoginScreen", "Exception during fallback authentication", e)
                                    isGoogleSignInLoading = false
                                    snackbarHostState.showSnackbar("Google sign-in failed: ${e.message}")
                                }
                            }
                        } else {
                            isGoogleSignInLoading = false
                            Log.e("LoginScreen", "No email returned from Google account")
                            coroutineScope.launch {
                                snackbarHostState.showSnackbar("Google sign-in failed: No email returned")
                            }
                        }
                    }
                } else {
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar("Google sign-in failed: No account returned")
                    }
                }
            } catch (e: ApiException) {
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Google sign-in failed: ${e.message}")
                }
            }
        }
    }

    // Log screen view
    LaunchedEffect(key1 = true) {
        AnalyticsManager.logScreenView("Login", "LoginScreen")
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.app_name),
                style = MaterialTheme.typography.headlineLarge
            )

            Spacer(modifier = Modifier.height(32.dp))

            OutlinedTextField(
                value = email,
                onValueChange = { email = it },
                label = { Text(stringResource(R.string.email)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Email,
                    imeAction = ImeAction.Next
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text(stringResource(R.string.password)) },
                modifier = Modifier.fillMaxWidth(),
                visualTransformation = PasswordVisualTransformation(),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Password,
                    imeAction = ImeAction.Done
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Forgot Password link
            Text(
                text = stringResource(R.string.forgot_password),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.primary
                ),
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(vertical = 8.dp)
                    .clickable { onNavigateToForgotPassword() }
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = {
                    if (email.isBlank() || password.isBlank()) {
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar("Please fill in all fields")
                        }
                        return@Button
                    }

                    isLoading = true
                    coroutineScope.launch {
                        val result = authRepository.signInWithEmailPassword(email, password)
                        isLoading = false

                        if (result.isSuccess) {
                            // Log analytics event
                            AnalyticsManager.logLogin("email")
                            onLoginSuccess()
                        } else {
                            snackbarHostState.showSnackbar(
                                result.exceptionOrNull()?.message ?: "Login failed"
                            )
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading && !isGoogleSignInLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(text = stringResource(R.string.login))
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Divider with "OR" text
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Divider(modifier = Modifier.weight(1f))
                Text(
                    text = stringResource(R.string.or),
                    modifier = Modifier.padding(horizontal = 16.dp),
                    style = MaterialTheme.typography.bodyMedium
                )
                Divider(modifier = Modifier.weight(1f))
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Google Sign-In button
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(4.dp),
                border = BorderStroke(width = 1.dp, color = Color.LightGray),
                onClick = {
                    if (!isLoading && !isGoogleSignInLoading) {
                        googleSignInLauncher.launch(googleSignInHelper.getSignInIntent())
                    }
                },
                enabled = !isLoading && !isGoogleSignInLoading
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (isGoogleSignInLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Image(
                            painter = painterResource(id = R.drawable.ic_google_logo),
                            contentDescription = stringResource(R.string.google_logo),
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(text = stringResource(R.string.sign_in_with_google))
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedButton(
                onClick = { onNavigateToRegister() },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading && !isGoogleSignInLoading
            ) {
                Text(text = stringResource(R.string.create_account))
            }
        }
    }
}
