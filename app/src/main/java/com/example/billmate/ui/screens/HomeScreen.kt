package com.example.billmate.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.DocumentScanner
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material.icons.filled.Receipt
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.NotificationsActive
import androidx.compose.material.icons.filled.NotificationsOff
import androidx.compose.material.icons.filled.Psychology
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue

import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.ai.AIInsightsManager
import com.example.billmate.ai.AIInsight

import com.example.billmate.ai.InsightPriority
import com.example.billmate.ai.InsightType
import com.example.billmate.model.Bill
import com.example.billmate.navigation.Screen
import com.example.billmate.notifications.BillNotificationManager
import com.example.billmate.notifications.NotificationStatusHelper
import com.example.billmate.repository.BillRepository
import com.example.billmate.ui.components.BottomNavBar
import com.example.billmate.ui.components.ModernTopAppBar
import com.example.billmate.ui.components.NotificationPermissionDialog
import java.text.NumberFormat
import java.util.Currency
import java.util.Date
import java.util.Locale
import com.example.billmate.utils.formatToString


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToAddBill: () -> Unit,
    onNavigateToScanBill: () -> Unit,
    onNavigateToQrCodeScan: () -> Unit,
    onNavigateToDocumentScan: () -> Unit,
    onNavigateToGalleryScan: () -> Unit,
    onNavigateToBillDetail: (String) -> Unit,
    onNavigateToStatistics: () -> Unit,

    onNavigateToEmailSetup: () -> Unit,
    navController: androidx.navigation.NavController,
    billRepository: BillRepository = BillRepository(LocalContext.current)
) {
    // Initialize AI Insights Manager
    val context = LocalContext.current
    val aiInsightsManager = remember { AIInsightsManager(context) }

    // Start background AI analysis
    LaunchedEffect(Unit) {
        aiInsightsManager.startBackgroundAnalysis()
    }

    // Collect AI insights
    val activeInsights by aiInsightsManager.activeInsights.collectAsState()
    val highPriorityInsights = remember(activeInsights) {
        activeInsights.filter { it.priority == InsightPriority.HIGH || it.priority == InsightPriority.URGENT }
    }

    // Get upcoming bills (due in the next 7 days)
    android.util.Log.d("PerformanceMonitor", "HomeScreen: Starting to collect upcoming bills")
    val startTime = System.currentTimeMillis()
    val upcomingBills by billRepository.getUpcomingBillsFlow().collectAsState(initial = emptyList())
    val collectTime = System.currentTimeMillis() - startTime
    android.util.Log.d("PerformanceMonitor", "HomeScreen: Collected ${upcomingBills.size} upcoming bills in $collectTime ms")

    // Calculate total due amount
    val calcStartTime = System.currentTimeMillis()
    val calcTime = System.currentTimeMillis() - calcStartTime
    android.util.Log.d("PerformanceMonitor", "HomeScreen: Calculated total due in $calcTime ms")

    Scaffold(
        topBar = {
            ModernTopAppBar(
                title = stringResource(R.string.app_name),
                titleIcon = Icons.Default.Home
            )
        },
        bottomBar = {
            BottomNavBar(navController = navController)
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // AI Insights Section (if any high priority insights)
            if (highPriorityInsights.isNotEmpty()) {
                AIInsightsCard(
                    insights = highPriorityInsights.take(2), // Show max 2 high priority insights
                    onInsightAction = { insight ->
                        when (insight.type) {
                            InsightType.RECURRING_SUGGESTION -> {
                                // Navigate to add bill with pre-filled data
                                val title = insight.actionData?.get("title") as? String
                                val amount = insight.actionData?.get("amount") as? Double
                                navController.navigate(
                                    Screen.AddEditBill.createRoute(
                                        title = title,
                                        amount = amount
                                    )
                                )
                            }
                            InsightType.SPENDING_ALERT -> {
                                // Navigate to statistics
                                onNavigateToStatistics()
                            }
                            else -> {
                                // Default action - dismiss insight
                                aiInsightsManager.dismissInsight(insight.id)
                            }
                        }
                    },
                    onDismiss = { insight ->
                        aiInsightsManager.dismissInsight(insight.id)
                    }
                )
            }

            // Enhanced Bills Dashboard Widget
            EnhancedBillsDashboard(
                billRepository = billRepository,
                upcomingBills = upcomingBills,
                onNavigateToBillList = { filter ->
                    navController.navigate(Screen.BillList.createRoute(filter))
                },
                onNavigateToBillDetail = onNavigateToBillDetail
            )

            // Quick Action Buttons
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                // First row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    QuickActionButton(
                        icon = Icons.Default.Edit,
                        text = stringResource(R.string.add_manually),
                        onClick = onNavigateToAddBill
                    )

                    QuickActionButton(
                        icon = Icons.Default.PhotoLibrary,
                        text = stringResource(R.string.scan_from_picture),
                        onClick = onNavigateToGalleryScan
                    )

                    QuickActionButton(
                        icon = Icons.Default.QrCode,
                        text = stringResource(R.string.scan_qr_code),
                        onClick = onNavigateToQrCodeScan
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Second row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    QuickActionButton(
                        icon = Icons.Default.Camera,
                        text = stringResource(R.string.scan_text),
                        onClick = onNavigateToScanBill
                    )

                    QuickActionButton(
                        icon = Icons.Default.DocumentScanner,
                        text = stringResource(R.string.scan_document),
                        onClick = onNavigateToDocumentScan
                    )

                    QuickActionButton(
                        icon = Icons.Default.Email,
                        text = stringResource(R.string.from_email),
                        onClick = onNavigateToEmailSetup
                    )
                }
            }


        }
    }
}

@Composable
fun UpcomingBillItem(
    bill: Bill,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = bill.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = bill.dueDate?.formatToString() ?: "",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Text(
            text = "€${String.format(Locale.getDefault(), "%.2f", bill.amount).replace('.', ',')}",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = if (bill.isPaid) MaterialTheme.colorScheme.tertiary else MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
fun QuickActionButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(80.dp)
            .padding(8.dp)
            .clickable(onClick = onClick)
    ) {
        Surface(
            modifier = Modifier.size(56.dp),
            shape = CircleShape,
            color = MaterialTheme.colorScheme.secondaryContainer
        ) {
            Box(contentAlignment = Alignment.Center) {
                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = MaterialTheme.colorScheme.onSecondaryContainer,
                    modifier = Modifier.size(24.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            maxLines = 2
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillItem(
    bill: Bill,
    onClick: () -> Unit,
    onStatusToggle: ((String, Boolean) -> Unit)? = null
) {
    // Get string resources outside of remember blocks
    val noDateString = stringResource(R.string.no_date)
    val paidString = stringResource(R.string.paid).uppercase()
    val unpaidString = stringResource(R.string.unpaid).uppercase()

    // Get notification status
    val context = LocalContext.current
    val notificationStatusHelper = remember { NotificationStatusHelper(context) }
    val notificationStatus = remember(bill.id, bill.isPaid, bill.dueDate) {
        notificationStatusHelper.getNotificationStatus(bill)
    }

    // Use remember to prevent unnecessary recompositions
    val title = remember(bill.id, bill.title) { bill.title }
    val amount = remember(bill.id, bill.amount) {
        "€${String.format(Locale.getDefault(), "%.2f", bill.amount).replace('.', ',')}"
    }
    val isPaid = remember(bill.id, bill.isPaid) { bill.isPaid }
    val dueDate = remember(bill.id, bill.dueDate, noDateString) {
        bill.dueDate?.formatToString() ?: noDateString
    }

    // Use remember for status text and color to avoid recomputing on every recomposition
    val statusText = remember(isPaid, paidString, unpaidString) {
        if (isPaid) paidString else unpaidString
    }

    // Get colors from the theme
    val primaryColor = MaterialTheme.colorScheme.primary
    val errorColor = MaterialTheme.colorScheme.error

    val statusColor = remember(isPaid, primaryColor, errorColor) {
        if (isPaid) primaryColor else errorColor
    }

    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )

                    // Notification status indicator
                    if (notificationStatus.hasActiveNotifications) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = when {
                                notificationStatus.isOverdue -> Icons.Default.NotificationsActive
                                notificationStatus.nextNotificationDate != null -> Icons.Default.Notifications
                                else -> Icons.Default.NotificationsOff
                            },
                            contentDescription = notificationStatusHelper.getStatusDescription(notificationStatus),
                            tint = when {
                                notificationStatus.isOverdue -> MaterialTheme.colorScheme.error
                                notificationStatus.nextNotificationDate != null -> MaterialTheme.colorScheme.primary
                                else -> MaterialTheme.colorScheme.onSurfaceVariant
                            },
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = stringResource(R.string.bill_amount),
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = amount,
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = stringResource(R.string.bill_due_date),
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = dueDate,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                // Clickable status area
                Column(
                    horizontalAlignment = Alignment.End,
                    modifier = if (onStatusToggle != null) {
                        Modifier.clickable {
                            onStatusToggle(bill.id, !bill.isPaid)
                        }
                    } else {
                        Modifier
                    }
                ) {
                    Text(
                        text = stringResource(R.string.status),
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Text(
                            text = statusText,
                            color = statusColor,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        // Show clickable icon if onStatusToggle is provided
                        if (onStatusToggle != null) {
                            Icon(
                                imageVector = if (isPaid) Icons.Default.CheckCircle else Icons.Default.Schedule,
                                contentDescription = if (isPaid) stringResource(R.string.mark_as_unpaid) else stringResource(R.string.mark_as_paid),
                                tint = statusColor,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }

            // Notification status row (if notifications are active)
            if (notificationStatus.hasActiveNotifications) {
                Spacer(modifier = Modifier.height(4.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = notificationStatusHelper.getStatusDescription(notificationStatus),
                        style = MaterialTheme.typography.bodySmall,
                        color = when {
                            notificationStatus.isOverdue -> MaterialTheme.colorScheme.error
                            notificationStatus.nextNotificationDate != null -> MaterialTheme.colorScheme.primary
                            else -> MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun EnhancedBillsDashboard(
    billRepository: BillRepository,
    upcomingBills: List<Bill>,
    onNavigateToBillList: (String?) -> Unit,
    onNavigateToBillDetail: (String) -> Unit
) {
    // Collect bill data
    val allBills by billRepository.getBillsFlow().collectAsState(initial = emptyList())

    // Calculate stats
    val totalBills = allBills.size
    val unpaidBills = allBills.filter { !it.isPaid }
    val unpaidCount = unpaidBills.size
    val unpaidAmount = unpaidBills.sumOf { it.amount }

    // Calculate overdue bills (past due date and not paid)
    val today = Date()
    val overdueBills = unpaidBills.filter { bill ->
        bill.dueDate?.before(today) == true
    }
    val overdueCount = overdueBills.size
    val overdueAmount = overdueBills.sumOf { it.amount }

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Pregled računov",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )

                Icon(
                    imageVector = Icons.Default.Assessment,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Interactive Stats grid
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Total bills - clickable
                ClickableStatCard(
                    title = "Skupaj",
                    value = totalBills.toString(),
                    subtitle = "računov",
                    icon = Icons.Default.Receipt,
                    color = MaterialTheme.colorScheme.primary,
                    onClick = { onNavigateToBillList(null) },
                    modifier = Modifier.weight(1f)
                )

                // Unpaid bills - clickable
                ClickableStatCard(
                    title = "Neplačani",
                    value = unpaidCount.toString(),
                    subtitle = "€${String.format(Locale.getDefault(), "%.0f", unpaidAmount).replace('.', ',')}",
                    icon = Icons.Default.Schedule,
                    color = MaterialTheme.colorScheme.tertiary,
                    onClick = { onNavigateToBillList("UNPAID") },
                    modifier = Modifier.weight(1f)
                )
            }

            if (overdueCount > 0) {
                Spacer(modifier = Modifier.height(12.dp))

                // Overdue warning - clickable
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onNavigateToBillList("OVERDUE") },
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.size(20.dp)
                        )

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "$overdueCount zapadlih računov",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                            Text(
                                text = "€${String.format(Locale.getDefault(), "%.2f", overdueAmount).replace('.', ',')}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }

                        Icon(
                            imageVector = Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            // Upcoming bills section
            if (upcomingBills.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Prihajajoči računi",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = "${upcomingBills.size} v naslednjih 7 dneh",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Show up to 3 upcoming bills
                upcomingBills.take(3).forEach { bill ->
                    UpcomingBillItem(
                        bill = bill,
                        onClick = { onNavigateToBillDetail(bill.id) }
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                }

                if (upcomingBills.size > 3) {
                    Text(
                        text = "in ${upcomingBills.size - 3} več...",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { onNavigateToBillList(null) },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Upravljaj račune")
                }

                // Temporary test notification button
                val context = LocalContext.current
                var showPermissionDialog by remember { mutableStateOf(false) }

                Button(
                    onClick = {
                        val notificationManager = BillNotificationManager(context)
                        if (notificationManager.hasNotificationPermission()) {
                            notificationManager.showTestNotification()
                        } else {
                            showPermissionDialog = true
                        }
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Test 🔔")
                }

                // Permission dialog
                NotificationPermissionDialog(
                    showDialog = showPermissionDialog,
                    onDismiss = { showPermissionDialog = false },
                    onPermissionGranted = {
                        showPermissionDialog = false
                        val notificationManager = BillNotificationManager(context)
                        notificationManager.showTestNotification()
                    },
                    onPermissionDenied = {
                        showPermissionDialog = false
                    }
                )
            }
        }
    }
}

@Composable
fun ClickableStatCard(
    title: String,
    value: String,
    subtitle: String,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = value,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = color
            )

            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun AIInsightsCard(
    insights: List<AIInsight>,
    onInsightAction: (AIInsight) -> Unit,
    onDismiss: (AIInsight) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Psychology,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.size(24.dp)
                    )
                    Text(
                        text = "AI Vpogledi",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }

                if (insights.size > 1) {
                    Text(
                        text = "${insights.size}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier
                            .background(
                                MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.2f),
                                CircleShape
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            insights.forEach { insight ->
                AIInsightItem(
                    insight = insight,
                    onAction = { onInsightAction(insight) },
                    onDismiss = { onDismiss(insight) }
                )

                if (insight != insights.last()) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
fun AIInsightItem(
    insight: AIInsight,
    onAction: () -> Unit,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = insight.title,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = insight.message,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }

                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Dismiss",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }

            if (insight.actionText != null) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onAction) {
                        Text(
                            text = insight.actionText,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        }
    }
}

