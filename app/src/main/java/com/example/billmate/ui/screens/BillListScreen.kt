package com.example.billmate.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.Business
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Receipt
import androidx.compose.material.icons.automirrored.filled.Sort
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.model.BillCategory
import com.example.billmate.repository.BillRepository
import com.example.billmate.ui.components.BottomNavBar
import com.example.billmate.ui.components.ModernTopAppBar
import com.example.billmate.ui.components.ModernTopAppBarAction
import java.util.Date

// Enums for filtering and sorting
enum class BillFilter {
    ALL, PAID, UNPAID, OVERDUE
}

enum class BillSortOption {
    DATE_ASC, DATE_DESC, AMOUNT_ASC, AMOUNT_DESC, STATUS, TITLE
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillListScreen(
    onNavigateToBillDetail: (String) -> Unit,
    navController: androidx.navigation.NavController,
    billRepository: BillRepository = BillRepository(LocalContext.current),
    initialFilter: String? = null
) {
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val tabs = listOf(
        "All", // These will be replaced with string resources in the Tab composable
        "Personal",
        "Business"
    )

    val selectedCategory = when (selectedTabIndex) {
        1 -> BillCategory.PERSONAL
        2 -> BillCategory.BUSINESS
        else -> null
    }

    // Filter and sort state
    var currentFilter by remember {
        mutableStateOf(
            when (initialFilter) {
                "UNPAID" -> BillFilter.UNPAID
                "PAID" -> BillFilter.PAID
                "OVERDUE" -> BillFilter.OVERDUE
                else -> BillFilter.ALL
            }
        )
    }
    var currentSort by remember { mutableStateOf(BillSortOption.DATE_DESC) }
    var showFilterMenu by remember { mutableStateOf(false) }
    var showSortMenu by remember { mutableStateOf(false) }

    // Snackbar and coroutine scope for status updates
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // Log the category selection for performance monitoring
    android.util.Log.d("PerformanceMonitor", "BillListScreen: Selected category: $selectedCategory")

    // Measure the time it takes to collect bills from the flow
    val startTime = System.currentTimeMillis()
    val bills by billRepository.getBillsFlow(selectedCategory).collectAsState(initial = null)

    // Log when bills are received
    bills?.let {
        val endTime = System.currentTimeMillis()
        android.util.Log.d("PerformanceMonitor", "BillListScreen: Collected ${it.size} bills in ${endTime - startTime} ms")
    }

    // Apply filtering and sorting
    val filteredAndSortedBills by remember {
        derivedStateOf {
            bills?.let { billList ->
                // Apply filter
                val today = Date()
                val filtered = when (currentFilter) {
                    BillFilter.ALL -> billList
                    BillFilter.PAID -> billList.filter { it.isPaid }
                    BillFilter.UNPAID -> billList.filter { !it.isPaid }
                    BillFilter.OVERDUE -> billList.filter { bill ->
                        !bill.isPaid && bill.dueDate?.before(today) == true
                    }
                }

                // Apply sort
                when (currentSort) {
                    BillSortOption.DATE_ASC -> filtered.sortedBy { it.dueDate }
                    BillSortOption.DATE_DESC -> filtered.sortedByDescending { it.dueDate }
                    BillSortOption.AMOUNT_ASC -> filtered.sortedBy { it.amount }
                    BillSortOption.AMOUNT_DESC -> filtered.sortedByDescending { it.amount }
                    BillSortOption.STATUS -> filtered.sortedBy { it.isPaid }
                    BillSortOption.TITLE -> filtered.sortedBy { it.title.lowercase() }
                }
            }
        }
    }

    // Status toggle function
    val onStatusToggle: (String, Boolean) -> Unit = { billId, newStatus ->
        coroutineScope.launch {
            val result = billRepository.markBillPaid(billId, newStatus)
            if (result.isSuccess) {
                val message = if (newStatus) {
                    context.getString(R.string.marked_as_paid)
                } else {
                    context.getString(R.string.marked_as_unpaid)
                }
                snackbarHostState.showSnackbar(message)
            } else {
                snackbarHostState.showSnackbar(
                    context.getString(R.string.failed_to_update, result.exceptionOrNull()?.message ?: "")
                )
            }
        }
    }

    Scaffold(
        topBar = {
            ModernTopAppBar(
                title = stringResource(R.string.bills),
                titleIcon = Icons.AutoMirrored.Filled.List,
                actions = {
                    // Filter button
                    Box {
                        ModernTopAppBarAction(
                            icon = Icons.Default.FilterList,
                            contentDescription = stringResource(R.string.filter),
                            onClick = { showFilterMenu = true }
                        )

                        DropdownMenu(
                            expanded = showFilterMenu,
                            onDismissRequest = { showFilterMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.all_bills)) },
                                onClick = {
                                    currentFilter = BillFilter.ALL
                                    showFilterMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.paid_bills)) },
                                onClick = {
                                    currentFilter = BillFilter.PAID
                                    showFilterMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.unpaid_bills)) },
                                onClick = {
                                    currentFilter = BillFilter.UNPAID
                                    showFilterMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Zapadli računi") },
                                onClick = {
                                    currentFilter = BillFilter.OVERDUE
                                    showFilterMenu = false
                                }
                            )
                        }
                    }

                    // Sort button
                    Box {
                        ModernTopAppBarAction(
                            icon = Icons.AutoMirrored.Filled.Sort,
                            contentDescription = stringResource(R.string.sort),
                            onClick = { showSortMenu = true }
                        )

                        DropdownMenu(
                            expanded = showSortMenu,
                            onDismissRequest = { showSortMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.sort_date_newest)) },
                                onClick = {
                                    currentSort = BillSortOption.DATE_DESC
                                    showSortMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.sort_date_oldest)) },
                                onClick = {
                                    currentSort = BillSortOption.DATE_ASC
                                    showSortMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.sort_amount_high)) },
                                onClick = {
                                    currentSort = BillSortOption.AMOUNT_DESC
                                    showSortMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.sort_amount_low)) },
                                onClick = {
                                    currentSort = BillSortOption.AMOUNT_ASC
                                    showSortMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.sort_status)) },
                                onClick = {
                                    currentSort = BillSortOption.STATUS
                                    showSortMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.sort_title)) },
                                onClick = {
                                    currentSort = BillSortOption.TITLE
                                    showSortMenu = false
                                }
                            )
                        }
                    }
                }
            )
        },
        bottomBar = {
            BottomNavBar(navController = navController)
        },
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState)
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            TabRow(selectedTabIndex = selectedTabIndex) {
                tabs.forEachIndexed { index, _ ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Icon(
                                    imageVector = when (index) {
                                        0 -> Icons.Default.Receipt
                                        1 -> Icons.Default.Person
                                        2 -> Icons.Default.Business
                                        else -> Icons.Default.Receipt
                                    },
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Text(
                                    text = when (index) {
                                        0 -> stringResource(R.string.all_bills)
                                        1 -> stringResource(R.string.category_personal)
                                        2 -> stringResource(R.string.category_business)
                                        else -> ""
                                    },
                                    style = MaterialTheme.typography.labelMedium
                                )
                            }
                        }
                    )
                }
            }

            // Filter and Sort Status Row
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Current filter chip
                    FilterChip(
                        selected = currentFilter != BillFilter.ALL,
                        onClick = { showFilterMenu = true },
                        label = {
                            Text(
                                when (currentFilter) {
                                    BillFilter.ALL -> stringResource(R.string.all_bills)
                                    BillFilter.PAID -> stringResource(R.string.paid)
                                    BillFilter.UNPAID -> stringResource(R.string.unpaid)
                                    BillFilter.OVERDUE -> "Zapadli"
                                },
                                maxLines = 1
                            )
                        },
                        leadingIcon = {
                            Icon(
                                Icons.Default.FilterList,
                                contentDescription = null
                            )
                        }
                    )

                    // Current sort chip
                    FilterChip(
                        selected = true,
                        onClick = { showSortMenu = true },
                        label = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Text(
                                    when (currentSort) {
                                        BillSortOption.DATE_DESC -> stringResource(R.string.sort_date_short)
                                        BillSortOption.DATE_ASC -> stringResource(R.string.sort_date_short)
                                        BillSortOption.AMOUNT_DESC -> stringResource(R.string.sort_amount_short)
                                        BillSortOption.AMOUNT_ASC -> stringResource(R.string.sort_amount_short)
                                        BillSortOption.STATUS -> stringResource(R.string.status)
                                        BillSortOption.TITLE -> stringResource(R.string.sort_title_short)
                                    },
                                    maxLines = 1
                                )

                                // Sort direction arrow
                                Icon(
                                    imageVector = when (currentSort) {
                                        BillSortOption.DATE_DESC -> Icons.Default.KeyboardArrowDown
                                        BillSortOption.DATE_ASC -> Icons.Default.KeyboardArrowUp
                                        BillSortOption.AMOUNT_DESC -> Icons.Default.KeyboardArrowDown
                                        BillSortOption.AMOUNT_ASC -> Icons.Default.KeyboardArrowUp
                                        BillSortOption.STATUS -> Icons.Default.KeyboardArrowDown // Paid first (descending)
                                        BillSortOption.TITLE -> Icons.Default.KeyboardArrowUp // A-Z (ascending)
                                    },
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        },
                        leadingIcon = {
                            Icon(
                                Icons.AutoMirrored.Filled.Sort,
                                contentDescription = null
                            )
                        }
                    )

                    // Show count of filtered bills
                    filteredAndSortedBills?.let { filteredBills ->
                        Spacer(modifier = Modifier.weight(1f))
                        Text(
                            text = stringResource(R.string.bills_count_format, filteredBills.size),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            if (bills == null) {
                // Loading state
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (filteredAndSortedBills?.isEmpty() == true) {
                // Empty state (either no bills or no bills match filter)
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = if (bills!!.isEmpty()) {
                                stringResource(R.string.no_bills)
                            } else {
                                stringResource(R.string.no_bills_match_filter)
                            },
                            style = MaterialTheme.typography.bodyLarge
                        )

                        if (bills!!.isNotEmpty() && currentFilter != BillFilter.ALL) {
                            Text(
                                text = stringResource(R.string.try_different_filter),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            } else {
                // Bills list
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = filteredAndSortedBills!!,
                        key = { bill -> bill.id }, // Use bill ID as key for better performance
                        contentType = { bill -> "bill" } // All items are of the same type
                    ) { bill ->
                        BillItem(
                            bill = bill,
                            onClick = { onNavigateToBillDetail(bill.id) },
                            onStatusToggle = onStatusToggle
                        )
                    }
                }
            }
        }
    }
}
