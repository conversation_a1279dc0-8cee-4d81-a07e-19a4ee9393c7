package com.example.billmate.ui.theme

import androidx.compose.ui.graphics.Color

// Dark theme colors
val Blue80 = Color(0xFF90CAF9)
val BlueGrey80 = Color(0xFFB0BEC5)
val Teal80 = Color(0xFF80CBC4)
val DarkBackground = Color(0xFF121212)
val DarkSurface = Color(0xFF1E1E1E)

// Light theme colors
val Blue40 = Color(0xFF1976D2)
val BlueGrey40 = Color(0xFF607D8B)
val Teal40 = Color(0xFF009688)
val LightBackground = Color(0xFFF5F5F5)
val LightSurface = Color(0xFFFFFFFF)

// Status colors
val SuccessGreen = Color(0xFF4CAF50)
val ErrorRed = Color(0xFFF44336)
val WarningAmber = Color(0xFFFFC107)
val InfoBlue = Color(0xFF2196F3)

// Legacy colors (kept for backward compatibility)
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)
val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)