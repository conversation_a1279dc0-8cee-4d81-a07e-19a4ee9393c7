package com.example.billmate.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.Button
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import android.util.Log
import com.example.billmate.R
import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.example.billmate.repository.BillRepository
import com.example.billmate.ui.components.DuplicateWarningDialog
import com.example.billmate.utils.DuplicateDetector
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import com.example.billmate.utils.formatToString

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditBillScreen(
    billId: String? = null,
    initialTitle: String? = null,
    initialAmount: Double? = null,
    initialDueDate: String? = null,
    onNavigateBack: () -> Unit,
    onSaveComplete: () -> Unit,
    billRepository: BillRepository = BillRepository(LocalContext.current)
) {
    val isEditMode = billId != null
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // Log the received data
    Log.d("AddEditBillScreen", "Received data: title=$initialTitle, amount=$initialAmount, dueDate=$initialDueDate")

    // Decode the URL-encoded title
    val decodedTitle = initialTitle?.let {
        try {
            java.net.URLDecoder.decode(it, "UTF-8")
        } catch (e: Exception) {
            Log.e("AddEditBillScreen", "Error decoding title", e)
            it
        }
    }

    Log.d("AddEditBillScreen", "Decoded title: $decodedTitle")

    var title by remember { mutableStateOf(decodedTitle ?: "") }
    var amount by remember { mutableStateOf(
        initialAmount?.let {
            // Format with comma instead of dot for European format
            String.format(Locale.getDefault(), "%.2f", it).replace('.', ',')
        } ?: ""
    ) }
    var description by remember { mutableStateOf("") }
    var dueDate by remember { mutableStateOf<Date?>(parseDateString(initialDueDate)) }
    var category by remember { mutableStateOf(BillCategory.PERSONAL) }
    var isLoading by remember { mutableStateOf(false) }

    // Duplicate detection state
    var showDuplicateDialog by remember { mutableStateOf(false) }
    var duplicateMatches by remember { mutableStateOf<List<DuplicateDetector.DuplicateMatch>>(emptyList()) }
    var pendingBill by remember { mutableStateOf<Bill?>(null) }

    // Date picker state
    val datePickerState = rememberDatePickerState()
    var showDatePicker by remember { mutableStateOf(false) }

    // Function to save bill with duplicate detection
    fun saveBillWithDuplicateCheck(bill: Bill, skipDuplicateCheck: Boolean = false) {
        isLoading = true
        coroutineScope.launch {
            try {
                // Skip duplicate check for edit mode or when explicitly requested
                if (isEditMode || skipDuplicateCheck) {
                    // Proceed with saving directly
                    val result = if (isEditMode) {
                        Log.d("AddEditBillScreen", "Updating existing bill with ID: ${bill.id}")
                        billRepository.updateBill(bill)
                    } else {
                        Log.d("AddEditBillScreen", "Adding new bill")
                        billRepository.addBill(bill).map {
                            Log.d("AddEditBillScreen", "New bill added with ID: $it")
                            Unit
                        }
                    }

                    isLoading = false
                    Log.d("AddEditBillScreen", "Save operation completed, success: ${result.isSuccess}")

                    if (result.isSuccess) {
                        Log.d("AddEditBillScreen", "Save successful, navigating back")
                        onSaveComplete()
                    } else {
                        val errorMessage = result.exceptionOrNull()?.message ?: "Unknown error"
                        Log.e("AddEditBillScreen", "Save failed: $errorMessage", result.exceptionOrNull())
                        snackbarHostState.showSnackbar(
                            context.getString(R.string.failed_to_save, errorMessage)
                        )
                    }
                } else {
                    // Check for duplicates first
                    val duplicateResult = billRepository.checkForDuplicates(bill)
                    isLoading = false

                    if (duplicateResult.isSuccess) {
                        val duplicates = duplicateResult.getOrNull() ?: emptyList()
                        val highConfidenceDuplicates = duplicates.filter { it.confidence >= 0.7 }

                        if (highConfidenceDuplicates.isNotEmpty()) {
                            // Show duplicate warning dialog
                            duplicateMatches = highConfidenceDuplicates
                            pendingBill = bill
                            showDuplicateDialog = true
                        } else {
                            // No high-confidence duplicates, proceed with saving
                            saveBillWithDuplicateCheck(bill, skipDuplicateCheck = true)
                        }
                    } else {
                        // Error checking duplicates, proceed with saving anyway
                        Log.w("AddEditBillScreen", "Error checking duplicates, proceeding with save")
                        saveBillWithDuplicateCheck(bill, skipDuplicateCheck = true)
                    }
                }
            } catch (e: Exception) {
                isLoading = false
                Log.e("AddEditBillScreen", "Error in saveBillWithDuplicateCheck", e)
                snackbarHostState.showSnackbar("Error: ${e.message}")
            }
        }
    }

    // Load bill data if in edit mode
    LaunchedEffect(billId) {
        if (isEditMode && billId != null) {
            isLoading = true
            val result = billRepository.getBillByIdResult(billId)
            isLoading = false

            if (result.isSuccess) {
                val bill = result.getOrNull()!!
                title = bill.title
                amount = String.format(Locale.getDefault(), "%.2f", bill.amount).replace('.', ',')
                description = bill.description
                dueDate = bill.dueDate
                category = bill.category
            } else {
                snackbarHostState.showSnackbar("Failed to load bill: ${result.exceptionOrNull()?.message}")
                onNavigateBack()
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(if (isEditMode) stringResource(R.string.edit_bill) else stringResource(R.string.add_bill_title)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Title
            OutlinedTextField(
                value = title,
                onValueChange = { title = it },
                label = { Text(stringResource(R.string.title)) },
                modifier = Modifier.fillMaxWidth()
            )

            // Amount
            OutlinedTextField(
                value = amount,
                onValueChange = { amount = it },
                label = { Text(stringResource(R.string.bill_amount)) },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                modifier = Modifier.fillMaxWidth()
            )

            // Description
            OutlinedTextField(
                value = description,
                onValueChange = { description = it },
                label = { Text(stringResource(R.string.bill_description)) },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3
            )

            // Due Date
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = dueDate?.formatToString() ?: "",
                    onValueChange = { },
                    label = { Text(stringResource(R.string.bill_due_date)) },
                    modifier = Modifier.weight(1f),
                    readOnly = true
                )

                IconButton(onClick = { showDatePicker = true }) {
                    Icon(Icons.Default.DateRange, contentDescription = stringResource(R.string.select_date))
                }
            }

            // Category
            Text(
                text = stringResource(R.string.bill_category),
                style = MaterialTheme.typography.bodyLarge
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = category == BillCategory.PERSONAL,
                    onClick = { category = BillCategory.PERSONAL }
                )
                Text(stringResource(R.string.category_personal))

                Spacer(modifier = Modifier.weight(1f))

                RadioButton(
                    selected = category == BillCategory.BUSINESS,
                    onClick = { category = BillCategory.BUSINESS }
                )
                Text(stringResource(R.string.category_business))
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Save Button
            Button(
                onClick = {
                    // Validate inputs
                    if (title.isBlank()) {
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar(context.getString(R.string.title_required))
                        }
                        return@Button
                    }

                    // Parse amount, handling both comma and dot as decimal separator
                    val amountValue = amount.replace(',', '.').toDoubleOrNull()
                    if (amountValue == null || amountValue <= 0) {
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar(context.getString(R.string.valid_amount))
                        }
                        return@Button
                    }

                    // Get the current user ID
                    val currentUserId = billRepository.getCurrentUserId()
                    Log.d("AddEditBillScreen", "Current user ID: $currentUserId")

                    // Create bill object
                    val bill = Bill(
                        id = billId ?: "",
                        title = title,
                        amount = amountValue,
                        description = description,
                        dueDate = dueDate,
                        category = category,
                        // Make sure to include the userId for edit mode
                        userId = currentUserId
                    )

                    Log.d("AddEditBillScreen", "Created bill object: $bill")

                    // Use the new duplicate detection function
                    saveBillWithDuplicateCheck(bill)
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading
            ) {
                Text(if (isLoading) stringResource(R.string.saving) else stringResource(R.string.save))
            }
        }

        // Date Picker Dialog
        if (showDatePicker) {
            DatePickerDialog(
                onDismissRequest = { showDatePicker = false },
                confirmButton = {
                    TextButton(
                        onClick = {
                            datePickerState.selectedDateMillis?.let {
                                dueDate = Date(it)
                            }
                            showDatePicker = false
                        }
                    ) {
                        Text(stringResource(R.string.ok))
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showDatePicker = false }) {
                        Text(stringResource(R.string.cancel))
                    }
                }
            ) {
                DatePicker(state = datePickerState)
            }
        }

        // Duplicate Warning Dialog
        if (showDuplicateDialog && pendingBill != null) {
            DuplicateWarningDialog(
                duplicates = duplicateMatches,
                onDismiss = {
                    showDuplicateDialog = false
                    duplicateMatches = emptyList()
                    pendingBill = null
                },
                onAddAnyway = {
                    showDuplicateDialog = false
                    val bill = pendingBill!!
                    pendingBill = null
                    duplicateMatches = emptyList()
                    // Save the bill without duplicate check
                    saveBillWithDuplicateCheck(bill, skipDuplicateCheck = true)
                },
                onCancel = {
                    showDuplicateDialog = false
                    duplicateMatches = emptyList()
                    pendingBill = null
                }
            )
        }
    }
}

// Function to parse date string to Date
private fun parseDateString(dateStr: String?): Date? {
    Log.d("AddEditBillScreen", "Parsing date string: '$dateStr'")
    if (dateStr == null || dateStr.isBlank()) return null

    return try {
        // First, try to parse as timestamp (from OCR/QR scan results)
        try {
            val timestamp = dateStr.toLongOrNull()
            if (timestamp != null) {
                val date = Date(timestamp)
                Log.d("AddEditBillScreen", "Successfully parsed '$dateStr' as timestamp -> $date")
                return date
            }
        } catch (e: Exception) {
            Log.d("AddEditBillScreen", "Not a timestamp, trying date formats")
        }

        // Try parsing with different date formats
        val formats = listOf(
            "dd.MM.yyyy",   // Slovenian format (11.05.2018)
            "yyyy-MM-dd",   // ISO format
            "dd. MMM yyyy", // European format used in the app
            "MMM dd, yyyy", // US format (fallback)
            "MM/dd/yyyy",   // US format
            "dd/MM/yyyy"    // European format
        )

        for (format in formats) {
            try {
                val sdf = SimpleDateFormat(format, Locale.getDefault())
                val parsedDate = sdf.parse(dateStr)
                Log.d("AddEditBillScreen", "Successfully parsed '$dateStr' with format '$format' -> $parsedDate")
                return parsedDate
            } catch (e: Exception) {
                Log.d("AddEditBillScreen", "Failed to parse '$dateStr' with format '$format': ${e.message}")
                // Try next format
            }
        }

        // If all formats fail, return null
        Log.w("AddEditBillScreen", "Failed to parse date '$dateStr' with any format")
        null
    } catch (e: Exception) {
        Log.e("AddEditBillScreen", "Error parsing date '$dateStr'", e)
        null
    }
}
