package com.example.billmate.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import android.util.Log
import com.example.billmate.R
import com.example.billmate.utils.AnalyticsManager
import com.example.billmate.utils.LocaleHelper

/**
 * Data class representing a language option.
 */
data class Language(val code: String, val nameResId: Int)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageSelectionScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val currentLanguage = remember { LocaleHelper.getLanguage(context) }
    var selectedLanguage by remember { mutableStateOf(currentLanguage) }
    var showConfirmDialog by remember { mutableStateOf(false) }

    Log.d("LanguageSelectionScreen", "Current language: $currentLanguage")

    // Log screen view
    LaunchedEffect(key1 = true) {
        AnalyticsManager.logScreenView("LanguageSelection", "LanguageSelectionScreen")
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.select_language)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            LazyColumn {
                val languages = listOf(
                    Language("en", R.string.language_english),
                    Language("sl", R.string.language_slovenian)
                )

                items(languages) { language ->
                    LanguageItem(
                        language = language,
                        isSelected = selectedLanguage == language.code,
                        onClick = {
                            if (selectedLanguage != language.code) {
                                Log.d("LanguageSelectionScreen", "Selected language: ${language.code}")
                                selectedLanguage = language.code
                                showConfirmDialog = true
                            }
                        }
                    )
                    Divider()
                }
            }
        }

        if (showConfirmDialog) {
            AlertDialog(
                onDismissRequest = { showConfirmDialog = false },
                title = { Text(stringResource(R.string.language_changed)) },
                text = { Text(stringResource(R.string.language_changed)) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            // Set the new language
                            Log.d("LanguageSelectionScreen", "Changing language to: $selectedLanguage")
                            LocaleHelper.setLocale(context, selectedLanguage)
                            showConfirmDialog = false

                            // Get the current activity and restart it
                            val activity = context as? android.app.Activity
                            activity?.let {
                                Log.d("LanguageSelectionScreen", "Restarting activity")
                                val intent = it.intent
                                it.finish()
                                it.startActivity(intent)
                                it.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
                            } ?: onNavigateBack()
                        }
                    ) {
                        Text(stringResource(R.string.ok))
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = {
                            selectedLanguage = currentLanguage
                            showConfirmDialog = false
                        }
                    ) {
                        Text(stringResource(R.string.cancel))
                    }
                }
            )
        }
    }
}

@Composable
fun LanguageItem(
    language: Language,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(language.nameResId),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(1f)
        )

        if (isSelected) {
            Spacer(modifier = Modifier.width(8.dp))
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}
