package com.example.billmate.ui.components

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.core.app.NotificationManagerCompat
import com.example.billmate.R

/**
 * Dialog to request notification permissions from the user
 */
@Composable
fun NotificationPermissionDialog(
    showDialog: <PERSON><PERSON><PERSON>,
    onDismiss: () -> Unit,
    onPermissionGranted: () -> Unit,
    onPermissionDenied: () -> Unit
) {
    val context = LocalContext.current
    var showSettingsDialog by remember { mutableStateOf(false) }
    
    // Permission launcher for Android 13+
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            onPermissionGranted()
        } else {
            // Check if we should show rationale or if user denied permanently
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                showSettingsDialog = true
            } else {
                onPermissionDenied()
            }
        }
    }
    
    // Check current permission status
    val hasNotificationPermission = remember {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.checkSelfPermission(Manifest.permission.POST_NOTIFICATIONS) == 
                android.content.pm.PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
    }
    
    // Auto-grant if already has permission
    LaunchedEffect(hasNotificationPermission) {
        if (hasNotificationPermission && showDialog) {
            onPermissionGranted()
        }
    }
    
    if (showDialog && !hasNotificationPermission) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = stringResource(R.string.notification_permission_title),
                    style = MaterialTheme.typography.headlineSmall
                )
            },
            text = {
                Text(
                    text = stringResource(R.string.notification_permission_message),
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                        } else {
                            // For older versions, direct to settings
                            showSettingsDialog = true
                        }
                    }
                ) {
                    Text(stringResource(R.string.notification_permission_grant))
                }
            },
            dismissButton = {
                TextButton(onClick = {
                    onDismiss()
                    onPermissionDenied()
                }) {
                    Text(stringResource(R.string.notification_permission_deny))
                }
            }
        )
    }
    
    // Settings dialog for when user needs to manually enable
    if (showSettingsDialog) {
        AlertDialog(
            onDismissRequest = { 
                showSettingsDialog = false
                onDismiss()
            },
            title = {
                Text(
                    text = stringResource(R.string.notification_permission_title),
                    style = MaterialTheme.typography.headlineSmall
                )
            },
            text = {
                Text(
                    text = "Prosimo omogočite obvestila v nastavitvah aplikacije za najboljšo izkušnjo.",
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        // Open app settings
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                            data = Uri.fromParts("package", context.packageName, null)
                        }
                        context.startActivity(intent)
                        showSettingsDialog = false
                        onDismiss()
                    }
                ) {
                    Text("Odpri nastavitve")
                }
            },
            dismissButton = {
                TextButton(onClick = {
                    showSettingsDialog = false
                    onDismiss()
                    onPermissionDenied()
                }) {
                    Text("Ne zdaj")
                }
            }
        )
    }
}
