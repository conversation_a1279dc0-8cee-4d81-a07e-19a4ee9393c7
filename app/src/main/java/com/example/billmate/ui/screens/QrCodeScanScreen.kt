package com.example.billmate.ui.screens

import android.Manifest
import android.graphics.BitmapFactory
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.example.billmate.R
import com.example.billmate.utils.AnalyticsManager
import com.example.billmate.utils.QrCodeProcessor
import com.google.mlkit.vision.common.InputImage
import kotlinx.coroutines.launch
import java.util.concurrent.Executors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QrCodeScanScreen(
    onNavigateBack: () -> Unit,
    onScanComplete: (Map<String, String>) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val qrCodeProcessor = remember { QrCodeProcessor() }

    var cameraProvider by remember { mutableStateOf<ProcessCameraProvider?>(null) }
    var previewView by remember { mutableStateOf<PreviewView?>(null) }
    var qrCodeResult by remember { mutableStateOf<QrCodeProcessor.QrCodeResult?>(null) }
    var isScanning by remember { mutableStateOf(false) }

    // Stable text to prevent recomposition
    val overlayText = stringResource(R.string.point_camera_at_qr_code)

    // Derived state to minimize recompositions
    val showLoadingIndicator by remember { derivedStateOf { isScanning } }

    var hasCameraPermission by remember {
        mutableStateOf(
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.CAMERA
            ) == android.content.pm.PackageManager.PERMISSION_GRANTED
        )
    }

    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasCameraPermission = isGranted
        if (!isGranted) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("Camera permission is required to scan QR codes")
            }
        }
    }

    LaunchedEffect(key1 = true) {
        if (!hasCameraPermission) {
            cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
        }

        // Log screen view
        AnalyticsManager.logScreenView("QrCodeScan", "QrCodeScanScreen")
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.qr_code_scan_title)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (qrCodeResult != null) {
                // Show QR code result
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(rememberScrollState()),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(R.string.qr_code_detected),
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // QR Code Information Card
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            qrCodeResult?.let { result ->
                                Text(
                                    text = "${stringResource(R.string.qr_code_type)}: ${result.type}",
                                    style = MaterialTheme.typography.bodyLarge,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )

                                result.title?.let {
                                    Text(
                                        text = "${stringResource(R.string.qr_code_title)}: $it",
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(bottom = 8.dp)
                                    )
                                }

                                result.amount?.let {
                                    Text(
                                        text = "${stringResource(R.string.qr_code_amount)}: €${String.format("%.2f", it).replace('.', ',')}",
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(bottom = 8.dp)
                                    )
                                }

                                result.dueDate?.let {
                                    Text(
                                        text = "${stringResource(R.string.qr_code_due_date)}: $it",
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(bottom = 8.dp)
                                    )
                                }

                                result.accountNumber?.let {
                                    Text(
                                        text = "${stringResource(R.string.qr_code_account)}: $it",
                                        style = MaterialTheme.typography.bodyMedium,
                                        modifier = Modifier.padding(bottom = 8.dp)
                                    )
                                }

                                result.referenceNumber?.let {
                                    Text(
                                        text = "${stringResource(R.string.qr_code_reference)}: $it",
                                        style = MaterialTheme.typography.bodyMedium,
                                        modifier = Modifier.padding(bottom = 8.dp)
                                    )
                                }
                            }
                        }
                    }

                    // Action buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Button(
                            onClick = {
                                // Use the extracted data
                                val data = mutableMapOf<String, String>()

                                qrCodeResult?.let { result ->
                                    result.title?.let { data["title"] = it }
                                    result.amount?.let { data["amount"] = it.toString() }
                                    result.dueDate?.let { data["dueDate"] = it }
                                    result.paymentUrl?.let { data["paymentUrl"] = it }
                                    result.accountNumber?.let { data["accountNumber"] = it }
                                    result.referenceNumber?.let { data["referenceNumber"] = it }
                                }

                                Log.d("QrCodeScanScreen", "Passing data to navigation: $data")
                                onScanComplete(data)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Check, contentDescription = null)
                            Text(text = stringResource(R.string.use_data), modifier = Modifier.padding(start = 8.dp))
                        }

                        Spacer(modifier = Modifier.size(16.dp))

                        Button(
                            onClick = {
                                // Reset and try again
                                qrCodeResult = null
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Close, contentDescription = null)
                            Text(text = stringResource(R.string.try_again), modifier = Modifier.padding(start = 8.dp))
                        }
                    }
                }
            } else if (hasCameraPermission) {
                // Camera preview
                AndroidView(
                    factory = { ctx ->
                        val preview = PreviewView(ctx)
                        previewView = preview
                        val executor = Executors.newSingleThreadExecutor()

                        cameraProviderFuture.addListener({
                            val provider = cameraProviderFuture.get()
                            cameraProvider = provider

                            val previewUseCase = Preview.Builder().build().also {
                                it.setSurfaceProvider(preview.surfaceProvider)
                            }

                            // Set up image analysis for QR code scanning
                            val imageAnalysis = ImageAnalysis.Builder()
                                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                                .build()

                            imageAnalysis.setAnalyzer(executor) { imageProxy ->
                                if (!isScanning && qrCodeResult == null) {
                                    val mediaImage = imageProxy.image
                                    if (mediaImage != null) {
                                        val inputImage = InputImage.fromMediaImage(
                                            mediaImage,
                                            imageProxy.imageInfo.rotationDegrees
                                        )

                                        coroutineScope.launch {
                                            try {
                                                isScanning = true
                                                Log.d("QrCodeScanScreen", "Processing QR code image...")
                                                val barcodes = qrCodeProcessor.processInputImage(inputImage)
                                                Log.d("QrCodeScanScreen", "Found ${barcodes.size} barcodes")

                                                val result = qrCodeProcessor.extractBillInfoFromQrCode(barcodes)

                                                if (result != null) {
                                                    Log.d("QrCodeScanScreen", "QR code result: ${result.type}, data: ${result.rawData}")
                                                    qrCodeResult = result

                                                    // Log analytics event
                                                    AnalyticsManager.logQrCodeScan(
                                                        success = true,
                                                        qrCodeType = result.type.name,
                                                        dataLength = result.rawData.length
                                                    )
                                                } else {
                                                    Log.d("QrCodeScanScreen", "No QR code result extracted")
                                                }
                                            } catch (e: Exception) {
                                                Log.e("QrCodeScanScreen", "Error processing QR code", e)
                                                // Log failed scan
                                                AnalyticsManager.logQrCodeScan(
                                                    success = false,
                                                    qrCodeType = "ERROR",
                                                    dataLength = 0
                                                )
                                            } finally {
                                                isScanning = false
                                                imageProxy.close()
                                            }
                                        }
                                    } else {
                                        imageProxy.close()
                                    }
                                } else {
                                    imageProxy.close()
                                }
                            }

                            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                            try {
                                provider.unbindAll()

                                // Bind camera with preview and image analysis
                                provider.bindToLifecycle(
                                    lifecycleOwner,
                                    cameraSelector,
                                    previewUseCase,
                                    imageAnalysis
                                )

                                Log.d("CameraX", "Camera bound successfully for QR scanning")
                            } catch (e: Exception) {
                                Log.e("CameraX", "QR Camera binding failed", e)
                            }
                        }, ContextCompat.getMainExecutor(ctx))

                        preview
                    },
                    modifier = Modifier.fillMaxSize()
                )

                // QR code scanning overlay
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        modifier = Modifier
                            .size(250.dp)
                            .padding(16.dp),
                        colors = androidx.compose.material3.CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                Icons.Default.QrCode,
                                contentDescription = "QR Code",
                                modifier = Modifier.size(48.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.height(8.dp))

                            // Fixed height container to prevent text jumping
                            Box(
                                modifier = Modifier.height(40.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = overlayText,
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = TextAlign.Center,
                                    maxLines = 2
                                )
                            }

                            // Fixed height container for loading indicator
                            Box(
                                modifier = Modifier.height(32.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                if (showLoadingIndicator) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            } else {
                // No camera permission UI
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(stringResource(R.string.camera_permission_required_qr))

                    Button(
                        onClick = { cameraPermissionLauncher.launch(Manifest.permission.CAMERA) },
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.open_settings))
                    }
                }
            }
        }
    }
}
