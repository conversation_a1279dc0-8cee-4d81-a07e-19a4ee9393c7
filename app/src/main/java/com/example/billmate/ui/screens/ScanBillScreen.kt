package com.example.billmate.ui.screens

import android.Manifest
import android.content.ContentValues
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.camera.core.ImageProxy
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close

import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FilterChip
import androidx.compose.material3.RadioButton
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.example.billmate.R
import com.example.billmate.utils.AnalyticsManager
import com.example.billmate.utils.OcrProcessor
import com.example.billmate.utils.formatToString

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.Executors

/**
 * Enum for different scanning modes.
 */




@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanBillScreen(
    onNavigateBack: () -> Unit,
    onScanComplete: (Map<String, String>) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val ocrProcessor = remember { OcrProcessor() }

    var cameraProvider by remember { mutableStateOf<ProcessCameraProvider?>(null) }
    var previewView by remember { mutableStateOf<PreviewView?>(null) }

    var hasCameraPermission by remember {
        mutableStateOf(
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED
        )
    }

    var isScanning by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<Uri?>(null) }
    var capturedBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var extractedBillInfo by remember { mutableStateOf<OcrProcessor.BillInfo?>(null) }
    var imageCapture by remember { mutableStateOf<ImageCapture?>(null) }

    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { granted ->
            hasCameraPermission = granted
            if (!granted) {
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Camera permission is required to scan bills")
                }
            }
        }
    )

    LaunchedEffect(key1 = true) {
        if (!hasCameraPermission) {
            cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
        }

        // Log screen view
        AnalyticsManager.logScreenView("ScanBill", "ScanBillScreen")
    }

    // Note: Removed automatic rebinding to prevent BufferQueue issues
    // Camera will be bound once during initialization and use both use cases

    // Function to capture image
    val captureImage: () -> Unit = {
        // Get the current image capture instance
        imageCapture?.let { imageCaptureInstance ->

        // Create output file
        val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
            .format(System.currentTimeMillis())
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, name)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/BillMate")
            }
        }

        // Create output options
        val outputOptions = ImageCapture.OutputFileOptions
            .Builder(
                context.contentResolver,
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )
            .build()

        // Capture the image
        isScanning = true
        imageCaptureInstance.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    output.savedUri?.let { uri ->
                        capturedImageUri = uri

                        // Process the captured image
                        coroutineScope.launch {
                            try {
                                // Load bitmap from URI
                                val bitmap = withContext(Dispatchers.IO) {
                                    val inputStream = context.contentResolver.openInputStream(uri)
                                    BitmapFactory.decodeStream(inputStream)
                                }

                                bitmap?.let {
                                    capturedBitmap = it

                                    // Process with OCR
                                    val text = ocrProcessor.processImage(it)
                                    val billInfo = ocrProcessor.extractBillInfo(text)
                                    extractedBillInfo = billInfo

                                    // Log analytics event
                                    AnalyticsManager.logOcrScan(
                                        success = billInfo.amount != null,
                                        textLength = text.text.length
                                    )
                                }
                            } catch (e: Exception) {
                                Log.e("ScanBillScreen", "Error processing image", e)
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar("Error processing image: ${e.message}")
                                }
                            } finally {
                                isScanning = false
                            }
                        }
                    }
                }

                override fun onError(exception: ImageCaptureException) {
                    Log.e("ScanBillScreen", "Image capture failed", exception)
                    isScanning = false
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar("Image capture failed: ${exception.message}")
                    }
                }
            }
        )
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.bill_text_scan_title)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        floatingActionButton = {
            if (hasCameraPermission && !isScanning && capturedImageUri == null) {
                FloatingActionButton(
                    onClick = captureImage
                ) {
                    Icon(Icons.Default.Camera, contentDescription = "Capture")
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (capturedBitmap != null && extractedBillInfo != null) {
                // Show captured image and extracted info
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(rememberScrollState()),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Captured image
                    capturedBitmap?.let { bitmap ->
                        Image(
                            bitmap = bitmap.asImageBitmap(),
                            contentDescription = "Captured Bill",
                            modifier = Modifier
                                .size(200.dp)
                                .padding(bottom = 16.dp)
                        )
                    }

                    // Extracted information
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.extracted_information),
                                style = MaterialTheme.typography.titleLarge,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            // Show OCR results
                            extractedBillInfo?.let { info ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.title)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(text = info.title)
                                }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.bill_amount)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(text = info.amount?.toString() ?: stringResource(R.string.no_date))
                                }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${stringResource(R.string.bill_due_date)}:",
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(
                                        text = info.dueDate?.formatToString() ?: stringResource(R.string.no_date)
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Action buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Button(
                            onClick = {
                                // Use the extracted data
                                val data = mutableMapOf<String, String>()

                                extractedBillInfo?.let { info ->
                                    data["title"] = info.title
                                    info.amount?.let { data["amount"] = it.toString() }
                                    info.dueDate?.let { data["dueDate"] = it.time.toString() }
                                }

                                Log.d("ScanBillScreen", "Passing data to navigation: $data")
                                onScanComplete(data)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Check, contentDescription = null)
                            Text(text = stringResource(R.string.use_data), modifier = Modifier.padding(start = 8.dp))
                        }

                        Spacer(modifier = Modifier.size(16.dp))

                        Button(
                            onClick = {
                                // Reset and try again
                                capturedImageUri = null
                                capturedBitmap = null
                                extractedBillInfo = null
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Close, contentDescription = null)
                            Text(text = stringResource(R.string.try_again), modifier = Modifier.padding(start = 8.dp))
                        }
                    }
                }
            } else if (hasCameraPermission) {
                // Camera preview
                AndroidView(
                    factory = { ctx ->
                        val preview = PreviewView(ctx)
                        previewView = preview
                        val executor = Executors.newSingleThreadExecutor()

                        cameraProviderFuture.addListener({
                            val provider = cameraProviderFuture.get()
                            cameraProvider = provider

                            val previewUseCase = Preview.Builder().build().also {
                                it.setSurfaceProvider(preview.surfaceProvider)
                            }

                            imageCapture = ImageCapture.Builder()
                                .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                                .build()

                            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                            try {
                                provider.unbindAll()

                                // Bind camera for OCR (image capture only)
                                provider.bindToLifecycle(
                                    lifecycleOwner,
                                    cameraSelector,
                                    previewUseCase,
                                    imageCapture!!
                                )

                                Log.d("CameraX", "Camera bound successfully for OCR")
                            } catch (e: Exception) {
                                Log.e("CameraX", "Binding failed", e)
                            }
                        }, ContextCompat.getMainExecutor(ctx))

                        preview
                    },
                    modifier = Modifier.fillMaxSize()
                )

                if (isScanning) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
            } else {
                // No camera permission UI
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(stringResource(R.string.camera_permission_required))

                    Button(
                        onClick = { cameraPermissionLauncher.launch(Manifest.permission.CAMERA) },
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.open_settings))
                    }
                }
            }
        }
    }
}
