package com.example.billmate.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.example.billmate.R
import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.example.billmate.repository.BillRepository
import com.example.billmate.utils.formatToString
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillDetailScreen(
    billId: String,
    onNavigateBack: () -> Unit,
    onNavigateToEdit: (String) -> Unit,
    billRepository: BillRepository = BillRepository(LocalContext.current)
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    var bill by remember { mutableStateOf<Bill?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    // Load bill data
    LaunchedEffect(billId) {
        isLoading = true
        val result = billRepository.getBillByIdResult(billId)
        isLoading = false

        if (result.isSuccess) {
            bill = result.getOrNull()
        } else {
            snackbarHostState.showSnackbar(context.getString(R.string.failed_to_load, result.exceptionOrNull()?.message ?: ""))
            onNavigateBack()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.bill_details)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            if (isLoading) {
                CircularProgressIndicator()
            } else if (bill != null) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    // Bill details card
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = bill!!.title,
                                style = MaterialTheme.typography.headlineMedium
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "${stringResource(R.string.bill_amount)}:",
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Text(
                                    text = stringResource(R.string.amount_with_currency,
                                        String.format(Locale.getDefault(), "%.2f", bill!!.amount).replace('.', ',')
                                    ),
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "${stringResource(R.string.bill_due_date)}:",
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Text(
                                    text = bill!!.dueDate?.formatToString() ?: stringResource(R.string.no_date),
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "${stringResource(R.string.bill_category)}:",
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Text(
                                    text = when (bill!!.category) {
                                        BillCategory.PERSONAL -> stringResource(R.string.category_personal)
                                        BillCategory.BUSINESS -> stringResource(R.string.category_business)
                                    },
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "${stringResource(R.string.status)}:",
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Text(
                                    text = if (bill!!.isPaid) stringResource(R.string.paid).uppercase() else stringResource(R.string.unpaid).uppercase(),
                                    color = if (bill!!.isPaid)
                                        MaterialTheme.colorScheme.primary
                                    else
                                        MaterialTheme.colorScheme.error,
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }

                            if (bill!!.description.isNotBlank()) {
                                Spacer(modifier = Modifier.height(16.dp))

                                Text(
                                    text = "${stringResource(R.string.bill_description)}:",
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                Text(
                                    text = bill!!.description,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Payment status buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // Mark as Paid button - only show if bill is unpaid
                        if (!bill!!.isPaid) {
                            Button(
                                onClick = {
                                    coroutineScope.launch {
                                        // Set to paid
                                        val newPaidStatus = true

                                        // Log the status change
                                        android.util.Log.d("BillDetailScreen", "Current isPaid: ${bill!!.isPaid}, Setting to: $newPaidStatus, Bill ID: $billId")

                                        // Update the bill's paid status
                                        val result = billRepository.markBillPaid(billId, newPaidStatus)

                                        if (result.isSuccess) {
                                            // Get the updated bill directly from the result
                                            val updatedBill = result.getOrNull()
                                            android.util.Log.d("BillDetailScreen", "Updated bill from repository: $updatedBill, isPaid: ${updatedBill?.isPaid}")

                                            // Create a new bill with the forced isPaid status
                                            val forcedBill = updatedBill?.copy(isPaid = newPaidStatus)
                                            android.util.Log.d("BillDetailScreen", "Forced bill status: $forcedBill, isPaid: ${forcedBill?.isPaid}")

                                            // Update the bill in the UI with the forced status
                                            bill = forcedBill

                                            // Show success message
                                            snackbarHostState.showSnackbar(context.getString(R.string.marked_as_paid))

                                            // Force a refresh of the bill list by navigating back
                                            onNavigateBack()
                                        } else {
                                            snackbarHostState.showSnackbar(
                                                context.getString(R.string.failed_to_update, result.exceptionOrNull()?.message ?: "")
                                            )
                                        }
                                    }
                                },
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(text = stringResource(R.string.mark_as_paid))
                            }
                        }

                        // Mark as Unpaid button - only show if bill is paid
                        if (bill!!.isPaid) {
                            Button(
                                onClick = {
                                    coroutineScope.launch {
                                        // Set to unpaid
                                        val newPaidStatus = false

                                        // Log the status change
                                        android.util.Log.d("BillDetailScreen", "Current isPaid: ${bill!!.isPaid}, Setting to: $newPaidStatus, Bill ID: $billId")

                                        // Update the bill's paid status
                                        val result = billRepository.markBillPaid(billId, newPaidStatus)

                                        if (result.isSuccess) {
                                            // Get the updated bill directly from the result
                                            val updatedBill = result.getOrNull()
                                            android.util.Log.d("BillDetailScreen", "Updated bill from repository: $updatedBill, isPaid: ${updatedBill?.isPaid}")

                                            // Create a new bill with the forced isPaid status
                                            val forcedBill = updatedBill?.copy(isPaid = newPaidStatus)
                                            android.util.Log.d("BillDetailScreen", "Forced bill status: $forcedBill, isPaid: ${forcedBill?.isPaid}")

                                            // Update the bill in the UI with the forced status
                                            bill = forcedBill

                                            // Show success message
                                            snackbarHostState.showSnackbar(context.getString(R.string.marked_as_unpaid))

                                            // Force a refresh of the bill list by navigating back
                                            onNavigateBack()
                                        } else {
                                            snackbarHostState.showSnackbar(
                                                context.getString(R.string.failed_to_update, result.exceptionOrNull()?.message ?: "")
                                            )
                                        }
                                    }
                                },
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(text = stringResource(R.string.mark_as_unpaid))
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Edit and Delete buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // Edit button
                        OutlinedButton(
                            onClick = { onNavigateToEdit(billId) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = null,
                                modifier = Modifier.padding(end = 8.dp)
                            )
                            Text(text = stringResource(R.string.edit_bill))
                        }

                        // Delete button
                        OutlinedButton(
                            onClick = { showDeleteConfirmation = true },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = null,
                                modifier = Modifier.padding(end = 8.dp)
                            )
                            Text(text = stringResource(R.string.delete_bill))
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }
            } else {
                Text(stringResource(R.string.bill_not_found))
            }
        }

        // Delete confirmation dialog
        if (showDeleteConfirmation) {
            AlertDialog(
                onDismissRequest = { showDeleteConfirmation = false },
                title = { Text(stringResource(R.string.delete_bill)) },
                text = { Text(stringResource(R.string.bill_delete_confirmation)) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showDeleteConfirmation = false
                            coroutineScope.launch {
                                val result = billRepository.deleteBill(billId)

                                if (result.isSuccess) {
                                    snackbarHostState.showSnackbar(context.getString(R.string.bill_deleted))
                                    onNavigateBack()
                                } else {
                                    snackbarHostState.showSnackbar(
                                        context.getString(R.string.failed_to_delete, result.exceptionOrNull()?.message ?: "")
                                    )
                                }
                            }
                        }
                    ) {
                        Text(stringResource(R.string.delete))
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showDeleteConfirmation = false }) {
                        Text(stringResource(R.string.cancel))
                    }
                }
            )
        }
    }
}
