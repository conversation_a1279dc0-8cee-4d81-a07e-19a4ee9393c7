package com.example.billmate.utils

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.LocaleList
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import java.util.Locale

/**
 * Utility class for language-related operations.
 */
object LanguageUtils {

    // Supported languages
    enum class Language(val code: String, val nameResId: Int) {
        ENGLISH("en", com.example.billmate.R.string.language_english),
        SLOVENIAN("sl", com.example.billmate.R.string.language_slovenian);

        companion object {
            fun fromCode(code: String): Language {
                return values().find { it.code == code } ?: ENGLISH
            }
        }
    }

    /**
     * Set the app language.
     */
    fun setAppLanguage(context: Context, languageCode: String) {
        // Log the language change
        Log.d("LanguageUtils", "Setting language to: $languageCode")

        try {
            // Create a locale from the language code
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            // Create configuration with the new locale
            val config = Configuration(context.resources.configuration)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                config.setLocales(LocaleList(locale))
            } else {
                @Suppress("DEPRECATION")
                config.locale = locale
            }

            // Update the configuration
            @Suppress("DEPRECATION")
            context.resources.updateConfiguration(config, context.resources.displayMetrics)

            // Also set the AppCompatDelegate locales for better compatibility
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)

            // Save the selected language
            val prefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
            prefs.edit().putString("language", languageCode).apply()

            Log.d("LanguageUtils", "Language set successfully to: $languageCode")
        } catch (e: Exception) {
            Log.e("LanguageUtils", "Error setting language: ${e.message}", e)
        }
    }

    /**
     * Get the current app language.
     */
    fun getCurrentLanguage(context: Context): String {
        val prefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        return prefs.getString("language", getDeviceLanguage()) ?: getDeviceLanguage()
    }

    /**
     * Get the device language.
     */
    private fun getDeviceLanguage(): String {
        return Locale.getDefault().language
    }

    /**
     * Apply the saved language on app start.
     */
    fun applyLanguageOnStart(context: Context) {
        val languageCode = getCurrentLanguage(context)
        setAppLanguage(context, languageCode)
    }
}
