package com.example.billmate.utils

import android.content.Context
import android.graphics.Color
import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.Pie<PERSON>hart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.formatter.PercentFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import java.util.Calendar
import java.util.Date

/**
 * Utility class for creating and configuring charts.
 */
object ChartUtils {

    /**
     * Configure a pie chart for category distribution.
     */
    fun setupCategoryPieChart(pieChart: <PERSON><PERSON><PERSON>) {
        pieChart.apply {
            description.isEnabled = false
            setUsePercentValues(true)
            setExtraOffsets(5f, 10f, 5f, 5f)
            dragDecelerationFrictionCoef = 0.95f
            isDrawHoleEnabled = true
            setHoleColor(Color.WHITE)
            setTransparentCircleColor(Color.WHITE)
            setTransparentCircleAlpha(110)
            holeRadius = 58f
            transparentCircleRadius = 61f
            setDrawCenterText(true)
            rotationAngle = 0f
            isRotationEnabled = true
            isHighlightPerTapEnabled = true
            animateY(1400)
            legend.isEnabled = true
            setEntryLabelColor(Color.WHITE)
            setEntryLabelTextSize(12f)
        }
    }

    /**
     * Create pie chart data for category distribution.
     */
    fun createCategoryPieData(bills: List<Bill>, chart: PieChart): PieData {
        // Group bills by category and sum amounts
        val categoryMap = bills.groupBy { it.category }
            .mapValues { entry -> entry.value.sumOf { it.amount } }

        // Create pie entries
        val entries = ArrayList<PieEntry>()
        categoryMap.forEach { (category, amount) ->
            entries.add(PieEntry(amount.toFloat(), category.name))
        }

        // Create dataset
        val dataSet = PieDataSet(entries, "Categories")
        dataSet.apply {
            sliceSpace = 3f
            selectionShift = 5f
            colors = ColorTemplate.MATERIAL_COLORS.toList()
        }

        // Create pie data
        val pieData = PieData(dataSet)
        pieData.apply {
            setValueFormatter(PercentFormatter(chart))
            setValueTextSize(11f)
            setValueTextColor(Color.WHITE)
        }

        return pieData
    }

    /**
     * Configure a bar chart for monthly spending.
     */
    fun setupMonthlyBarChart(barChart: BarChart) {
        barChart.apply {
            description.isEnabled = false
            setPinchZoom(false)
            setDrawBarShadow(false)
            setDrawGridBackground(false)

            val xAxis = xAxis
            xAxis.position = XAxis.XAxisPosition.BOTTOM
            xAxis.granularity = 1f
            xAxis.setCenterAxisLabels(true)
            xAxis.setDrawGridLines(false)

            axisLeft.setDrawGridLines(false)
            axisRight.isEnabled = false

            legend.isEnabled = true

            animateY(1400)
        }
    }

    /**
     * Create bar chart data for monthly spending.
     */
    fun createMonthlyBarData(bills: List<Bill>, chart: BarChart): BarData {
        // Get bills from the last 6 months
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.MONTH, -5) // Go back 5 months to show 6 months total
        val sixMonthsAgo = calendar.time

        val filteredBills = bills.filter { bill ->
            bill.dueDate?.after(sixMonthsAgo) ?: false
        }

        // Group bills by month and category
        val monthlyData = mutableMapOf<Int, MutableMap<BillCategory, Float>>()

        // Initialize with empty data for all 6 months
        for (i in 0 until 6) {
            val monthKey = (calendar.get(Calendar.MONTH) + i) % 12
            monthlyData[monthKey] = mutableMapOf(
                BillCategory.PERSONAL to 0f,
                BillCategory.BUSINESS to 0f
            )
        }

        // Fill with actual data
        filteredBills.forEach { bill ->
            bill.dueDate?.let { dueDate ->
                val cal = Calendar.getInstance()
                cal.time = dueDate
                val month = cal.get(Calendar.MONTH)

                val categoryMap = monthlyData.getOrPut(month) {
                    mutableMapOf(BillCategory.PERSONAL to 0f, BillCategory.BUSINESS to 0f)
                }

                categoryMap[bill.category] = categoryMap.getOrDefault(bill.category, 0f) + bill.amount.toFloat()
            }
        }

        // Create entries for personal bills
        val personalEntries = ArrayList<BarEntry>()
        monthlyData.entries.sortedBy { it.key }.forEachIndexed { index, entry ->
            personalEntries.add(BarEntry(index.toFloat(), entry.value.getOrDefault(BillCategory.PERSONAL, 0f)))
        }

        // Create entries for business bills
        val businessEntries = ArrayList<BarEntry>()
        monthlyData.entries.sortedBy { it.key }.forEachIndexed { index, entry ->
            businessEntries.add(BarEntry(index.toFloat(), entry.value.getOrDefault(BillCategory.BUSINESS, 0f)))
        }

        // Create datasets
        val personalDataSet = BarDataSet(personalEntries, "Personal")
        personalDataSet.color = ColorTemplate.MATERIAL_COLORS[0]

        val businessDataSet = BarDataSet(businessEntries, "Business")
        businessDataSet.color = ColorTemplate.MATERIAL_COLORS[1]

        // Create bar data
        val barData = BarData(personalDataSet, businessDataSet)
        barData.barWidth = 0.3f

        // Set X axis labels (month names)
        val monthNames = arrayOf("Jan", "Feb", "Mar", "Apr", "May", "Jun",
                                "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")

        val labels = monthlyData.keys.sorted().map { monthNames[it] }.toTypedArray()
        chart.xAxis.valueFormatter = IndexAxisValueFormatter(labels)

        // Set the position of the bars
        chart.xAxis.axisMinimum = -0.5f
        chart.xAxis.axisMaximum = labels.size - 0.5f
        barData.groupBars(0f, 0.4f, 0.05f)

        return barData
    }

    /**
     * Create bar chart data for paid vs unpaid bills.
     */
    fun createStatusBarData(bills: List<Bill>, chart: BarChart): BarData {
        // Count paid and unpaid bills
        val paidCount = bills.count { it.isPaid }
        val unpaidCount = bills.size - paidCount

        // Create entries
        val entries = ArrayList<BarEntry>()
        entries.add(BarEntry(0f, paidCount.toFloat()))
        entries.add(BarEntry(1f, unpaidCount.toFloat()))

        // Create dataset
        val dataSet = BarDataSet(entries, "Bill Status")
        dataSet.colors = listOf(ColorTemplate.MATERIAL_COLORS[3], ColorTemplate.MATERIAL_COLORS[2])

        // Create bar data
        val barData = BarData(dataSet)
        barData.barWidth = 0.9f

        // Set X axis labels
        val labels = arrayOf("Paid", "Unpaid")
        chart.xAxis.valueFormatter = IndexAxisValueFormatter(labels)

        return barData
    }
}
