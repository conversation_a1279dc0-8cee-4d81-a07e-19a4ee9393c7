package com.example.billmate.utils

import android.content.Context
import android.util.Log
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.Properties

/**
 * Utility class to load environment variables from .env file
 */
object EnvConfig {
    private val properties = Properties()
    private var isInitialized = false
    private var context: Context? = null

    /**
     * Initialize the environment configuration
     */
    fun init(context: Context) {
        this.context = context

        if (isInitialized) {
            Log.d("EnvConfig", "Already initialized, skipping")
            return
        }

        try {
            // List all files in assets to check if env.properties exists
            val assetList = context.assets.list("")
            Log.d("EnvConfig", "Assets directory contents: ${assetList?.joinToString(", ")}")

            // Check if env.properties exists in assets
            if (assetList?.contains("env.properties") != true) {
                Log.w("EnvConfig", "env.properties file not found in assets, using hardcoded values")
                throw Exception("env.properties file not found")
            }

            // Try to load from assets
            val assetManager = context.assets
            val inputStream = assetManager.open("env.properties")
            val reader = BufferedReader(InputStreamReader(inputStream))

            Log.d("EnvConfig", "Successfully opened env.properties file")

            var line: String?
            while (reader.readLine().also { line = it } != null) {
                Log.d("EnvConfig", "Reading line: $line")
                line?.let {
                    if (it.isNotBlank() && !it.startsWith("#")) {
                        val parts = it.split("=", limit = 2)
                        if (parts.size == 2) {
                            val key = parts[0].trim()
                            val value = parts[1].trim()
                            properties[key] = value
                            Log.d("EnvConfig", "Loaded key: $key with value: ${value.take(5)}...")
                        }
                    }
                }
            }

            reader.close()
            inputStream.close()
            isInitialized = true
            Log.d("EnvConfig", "Environment variables loaded successfully")
            Log.d("EnvConfig", "API Key: ${getGeminiApiKey().take(5)}...")
        } catch (e: Exception) {
            Log.e("EnvConfig", "Failed to load environment variables", e)
            e.printStackTrace()

            // Fallback to hardcoded values for development
            Log.d("EnvConfig", "Using hardcoded API keys")
            properties["GEMINI_API_KEY"] = "AIzaSyAvUzFiyZyU7YIF-jRrTdl-gjuVo6RFaV0"
            properties["GMAIL_CLIENT_ID"] = "635502146020-09osbs8sg138m65v6aaqs67gnik73prs.apps.googleusercontent.com"
            properties["GMAIL_CLIENT_ID_DEBUG"] = "635502146020-09osbs8sg138m65v6aaqs67gnik73prs.apps.googleusercontent.com"
            isInitialized = true
        }
    }

    /**
     * Get an environment variable
     */
    fun get(key: String): String {
        if (!isInitialized) {
            Log.w("EnvConfig", "EnvConfig not initialized. Call init() first.")
            return ""
        }

        return properties.getProperty(key, "")
    }

    /**
     * Get the Gemini API key
     */
    fun getGeminiApiKey(): String {
        return get("GEMINI_API_KEY")
    }

    /**
     * Get the Gmail client ID
     */
    fun getGmailClientId(): String {
        val packageName = context?.packageName ?: ""
        return if (packageName.endsWith(".debug")) {
            get("GMAIL_CLIENT_ID_DEBUG")
        } else {
            get("GMAIL_CLIENT_ID")
        }
    }

    // Gmail client secret is not needed for Android OAuth
}
