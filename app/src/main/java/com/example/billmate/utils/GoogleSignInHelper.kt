package com.example.billmate.utils

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.google.firebase.auth.AuthCredential
import com.google.firebase.auth.GoogleAuthProvider

/**
 * Helper class for Google Sign-In functionality.
 */
class GoogleSignInHelper(private val context: Context) {

    companion object {
        private const val TAG = "GoogleSignInHelper"

        // Default web client ID from Firebase Console
        // This is automatically included in the google-services.json file
        private const val DEFAULT_WEB_CLIENT_ID = "************-rvf8qoasd5f2v9mq9cjgfasdkjh3k2l4.apps.googleusercontent.com"
    }

    // Google Sign-In client
    private val googleSignInClient: GoogleSignInClient by lazy {
        try {
            // Get the web client ID from google-services.json for Firebase Authentication
            Log.d(TAG, "Initializing Google Sign-In with Firebase web client ID")

            // Use the web client ID from Firebase for proper authentication
            // This is the web client ID from google-services.json
            val webClientId = "************-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com"
            Log.d(TAG, "Web client ID: ${webClientId.take(15)}...")

            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken(webClientId)  // Required for Firebase Auth
                .requestEmail()
                .requestProfile()
                .build()

            Log.d(TAG, "Google Sign-In options built successfully with ID token request")
            GoogleSignIn.getClient(context, gso)
        } catch (e: Exception) {
            // Log the error
            Log.e(TAG, "Error initializing Google Sign-In with web client ID", e)

            // Fallback to basic sign-in if there's an issue
            Log.w(TAG, "Using fallback Google Sign-In without ID token")
            val fallbackGso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestEmail()
                .requestProfile()
                .build()

            GoogleSignIn.getClient(context, fallbackGso)
        }
    }

    /**
     * Get the sign-in intent to launch with the activity result launcher.
     */
    fun getSignInIntent(): Intent {
        return googleSignInClient.signInIntent
    }

    /**
     * Handle the sign-in result.
     */
    fun handleSignInResult(completedTask: Task<GoogleSignInAccount>): GoogleSignInAccount? {
        return try {
            Log.d(TAG, "Handling Google Sign-In result")
            val account = completedTask.getResult(ApiException::class.java)
            Log.d(TAG, "Google Sign-In successful, account: ${account.email}, id: ${account.id}")
            account
        } catch (e: ApiException) {
            // Log the error
            Log.e(TAG, "Google sign-in failed: ${e.statusCode} - ${e.message}", e)
            AnalyticsManager.logCustomEvent("google_sign_in_error", android.os.Bundle().apply {
                putString("error_code", e.statusCode.toString())
                putString("error_message", e.message ?: "Unknown error")
            })
            null
        } catch (e: Exception) {
            // Log any other exceptions
            Log.e(TAG, "Unexpected error in Google Sign-In", e)
            null
        }
    }

    /**
     * Get Firebase AuthCredential from Google Sign-In account.
     */
    fun getAuthCredential(account: GoogleSignInAccount): AuthCredential? {
        return try {
            Log.d(TAG, "Getting auth credential from Google account")
            val idToken = account.idToken
            Log.d(TAG, "ID token present: ${idToken != null}")

            if (idToken != null) {
                val credential = GoogleAuthProvider.getCredential(idToken, null)
                Log.d(TAG, "Auth credential created successfully")
                credential
            } else {
                // For testing, let's create a custom credential
                Log.d(TAG, "No ID token found, using email for authentication")
                // Since we don't have an ID token, we'll use the email for authentication
                // This is just for testing and should be removed in production
                account.email?.let { email ->
                    // We'll use the email to sign in or create an account
                    return null
                }

                Log.w(TAG, "No ID token or email found in Google account")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting auth credential", e)
            null
        }
    }

    /**
     * Sign out from Google.
     */
    fun signOut() {
        googleSignInClient.signOut()
    }
}
