package com.example.billmate.utils

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

// Extension property for DataStore
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

/**
 * Manager for theme preferences.
 */
class ThemeManager(private val context: Context) {
    companion object {
        private val IS_DARK_MODE = booleanPreferencesKey("is_dark_mode")
        private val USE_DYNAMIC_COLORS = booleanPreferencesKey("use_dynamic_colors")

        // Singleton instance
        @Volatile
        private var INSTANCE: ThemeManager? = null

        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                val instance = ThemeManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }

    /**
     * Get the current theme preference as a Flow.
     */
    val isDarkMode: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[IS_DARK_MODE] ?: false
        }

    /**
     * Get the current dynamic colors preference as a Flow.
     */
    val useDynamicColors: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[USE_DYNAMIC_COLORS] ?: true // Default to true for dynamic colors
        }

    /**
     * Set the theme preference.
     */
    suspend fun setDarkMode(isDarkMode: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[IS_DARK_MODE] = isDarkMode
        }

        // Log theme change
        AnalyticsManager.logThemeChanged(isDarkMode)
    }

    /**
     * Set the dynamic colors preference.
     */
    suspend fun setDynamicColors(useDynamicColors: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[USE_DYNAMIC_COLORS] = useDynamicColors
        }

        // Log dynamic colors change
        AnalyticsManager.logCustomEvent("dynamic_colors_changed", android.os.Bundle().apply {
            putBoolean("use_dynamic_colors", useDynamicColors)
        })
    }

    /**
     * Toggle the current theme.
     */
    suspend fun toggleTheme() {
        context.dataStore.edit { preferences ->
            val current = preferences[IS_DARK_MODE] ?: false
            preferences[IS_DARK_MODE] = !current

            // Log theme change
            AnalyticsManager.logThemeChanged(!current)
        }
    }
}
