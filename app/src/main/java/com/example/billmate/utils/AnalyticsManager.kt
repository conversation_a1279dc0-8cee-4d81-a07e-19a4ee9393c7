package com.example.billmate.utils

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase

/**
 * Utility class to manage Firebase Analytics events.
 */
object AnalyticsManager {
    private val firebaseAnalytics: FirebaseAnalytics by lazy {
        Firebase.analytics
    }

    // Authentication events
    fun logLogin(method: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.LOGIN) {
            param(FirebaseAnalytics.Param.METHOD, method)
        }
    }

    fun logSignUp(method: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SIGN_UP) {
            param(FirebaseAnalytics.Param.METHOD, method)
        }
    }

    // Bill management events
    fun logBillAdded(category: String, hasImage: Boolean) {
        firebaseAnalytics.logEvent("bill_added") {
            param("category", category)
            param("has_image", hasImage.toString())
        }
    }

    fun logBillEdited(category: String) {
        firebaseAnalytics.logEvent("bill_edited") {
            param("category", category)
        }
    }

    fun logBillDeleted(category: String) {
        firebaseAnalytics.logEvent("bill_deleted") {
            param("category", category)
        }
    }

    fun logBillPaid(category: String, daysBeforeDue: Int) {
        firebaseAnalytics.logEvent("bill_paid") {
            param("category", category)
            param("days_before_due", daysBeforeDue.toLong())
        }
    }

    // OCR events
    fun logOcrScan(success: Boolean, textLength: Int) {
        firebaseAnalytics.logEvent("ocr_scan") {
            param("success", success.toString())
            param("text_length", textLength.toLong())
        }
    }

    // QR Code events
    fun logQrCodeScan(success: Boolean, qrCodeType: String, dataLength: Int) {
        firebaseAnalytics.logEvent("qr_code_scan") {
            param("success", success.toString())
            param("qr_code_type", qrCodeType)
            param("data_length", dataLength.toLong())
        }
    }

    // Screen view events
    fun logScreenView(screenName: String, screenClass: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, screenClass)
        }
    }

    // Document scanning events
    fun logDocumentScan(success: Boolean, pageCount: Int) {
        firebaseAnalytics.logEvent("document_scan") {
            param("success", success.toString())
            param("page_count", pageCount.toLong())
        }
    }

    // Gallery scanning events
    fun logGalleryScan(success: Boolean, hasResults: Boolean) {
        firebaseAnalytics.logEvent("gallery_scan") {
            param("success", success.toString())
            param("has_results", hasResults.toString())
        }
    }

    // Theme events
    fun logThemeChanged(isDarkMode: Boolean) {
        firebaseAnalytics.logEvent("theme_changed") {
            param("dark_mode", isDarkMode.toString())
        }
    }

    // Custom event with dynamic parameters
    fun logCustomEvent(eventName: String, params: Bundle) {
        firebaseAnalytics.logEvent(eventName, params)
    }
}
