package com.example.billmate.utils

import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Utility class to process QR codes and barcodes from images.
 */
class QrCodeProcessor {
    // Use default scanner that detects all formats for better compatibility
    private val scanner = BarcodeScanning.getClient()

    /**
     * Process an image and extract QR codes/barcodes using ML Kit.
     */
    suspend fun processImage(bitmap: Bitmap): List<Barcode> = suspendCancellableCoroutine { continuation ->
        val image = InputImage.fromBitmap(bitmap, 0)
        scanner.process(image)
            .addOnSuccessListener { barcodes ->
                continuation.resume(barcodes)
            }
            .addOnFailureListener { e ->
                continuation.resumeWithException(e)
            }
    }

    /**
     * Process an InputImage and extract QR codes/barcodes using ML Kit.
     */
    suspend fun processInputImage(inputImage: InputImage): List<Barcode> = suspendCancellableCoroutine { continuation ->
        Log.d("QrCodeProcessor", "Starting barcode processing...")
        scanner.process(inputImage)
            .addOnSuccessListener { barcodes ->
                Log.d("QrCodeProcessor", "Barcode processing successful, found ${barcodes.size} barcodes")
                continuation.resume(barcodes)
            }
            .addOnFailureListener { e ->
                Log.e("QrCodeProcessor", "Barcode processing failed", e)
                continuation.resumeWithException(e)
            }
    }

    /**
     * Extract bill information from QR code data.
     * This method attempts to parse common QR code formats that might contain bill information.
     */
    fun extractBillInfoFromQrCode(barcodes: List<Barcode>): QrCodeResult? {
        Log.d("QrCodeProcessor", "Processing ${barcodes.size} barcodes")
        if (barcodes.isEmpty()) return null

        for (barcode in barcodes) {
            val rawValue = barcode.rawValue
            val displayValue = barcode.displayValue
            Log.d("QrCodeProcessor", "QR Code detected: format=${barcode.format}, rawValue='$rawValue', displayValue='${displayValue}', valueType=${barcode.valueType}")

            // Use displayValue if rawValue is null/empty (common with some QR formats)
            val dataToProcess = rawValue ?: displayValue
            if (dataToProcess.isNullOrEmpty()) {
                Log.w("QrCodeProcessor", "Both rawValue and displayValue are empty, skipping")
                continue
            }

            // Try to extract bill information from different QR code formats
            val billInfo = when (barcode.format) {
                Barcode.FORMAT_QR_CODE -> parseQrCodeData(dataToProcess)
                Barcode.FORMAT_DATA_MATRIX -> parseDataMatrixCode(dataToProcess)
                else -> parseGenericBarcode(dataToProcess, barcode.format)
            }

            if (billInfo != null) {
                return billInfo
            }
        }

        // If no structured data found, return the first QR code's raw data
        val firstBarcode = barcodes.first()
        val rawData = firstBarcode.rawValue ?: firstBarcode.displayValue ?: ""

        Log.d("QrCodeProcessor", "Returning generic result with rawData length: ${rawData.length}")

        return QrCodeResult(
            type = QrCodeType.GENERIC,
            rawData = rawData,
            title = if (rawData.isNotEmpty()) "Scanned Code" else null,
            amount = null,
            dueDate = null,
            paymentUrl = null,
            accountNumber = null,
            referenceNumber = rawData.takeIf { it.isNotEmpty() }
        )
    }

    /**
     * Parse QR code data for common bill payment formats.
     */
    private fun parseQrCodeData(data: String): QrCodeResult? {
        // Try to parse different QR code formats

        // 1. Check for UPNQR format (Universal Payment QR - Slovenia/Europe)
        if (data.startsWith("UPNQR")) {
            return parseUpnqrCode(data)
        }

        // 2. Check for URL format (payment links)
        if (data.startsWith("http://") || data.startsWith("https://")) {
            return QrCodeResult(
                type = QrCodeType.PAYMENT_URL,
                rawData = data,
                paymentUrl = data,
                title = extractTitleFromUrl(data),
                amount = extractAmountFromUrl(data),
                dueDate = null,
                accountNumber = null,
                referenceNumber = null
            )
        }

        // 2. Check for structured payment data (e.g., EPC QR codes for SEPA payments)
        if (data.startsWith("BCD\n")) {
            return parseEpcQrCode(data)
        }

        // 3. Check for JSON format
        if (data.trim().startsWith("{") && data.trim().endsWith("}")) {
            return parseJsonQrCode(data)
        }

        // 4. Check for key-value pairs separated by newlines or semicolons
        if (data.contains("amount", ignoreCase = true) ||
            data.contains("bill", ignoreCase = true) ||
            data.contains("payment", ignoreCase = true)) {
            return parseKeyValueQrCode(data)
        }

        return null
    }

    /**
     * Parse EPC QR code format (European Payment Council standard).
     */
    private fun parseEpcQrCode(data: String): QrCodeResult? {
        val lines = data.split("\n")
        if (lines.size < 12) return null

        try {
            val amount = lines.getOrNull(5)?.toDoubleOrNull()
            val beneficiaryName = lines.getOrNull(7)
            val remittanceInfo = lines.getOrNull(10)

            return QrCodeResult(
                type = QrCodeType.EPC_PAYMENT,
                rawData = data,
                title = beneficiaryName ?: "Payment",
                amount = amount,
                dueDate = null,
                paymentUrl = null,
                accountNumber = lines.getOrNull(6), // IBAN
                referenceNumber = remittanceInfo
            )
        } catch (e: Exception) {
            Log.e("QrCodeProcessor", "Error parsing EPC QR code", e)
            return null
        }
    }

    /**
     * Parse JSON format QR codes.
     */
    private fun parseJsonQrCode(data: String): QrCodeResult? {
        // This is a simplified JSON parser - in a real app you might want to use a proper JSON library
        try {
            val title = extractJsonValue(data, "title") ?: extractJsonValue(data, "name") ?: extractJsonValue(data, "description")
            val amountStr = extractJsonValue(data, "amount") ?: extractJsonValue(data, "total") ?: extractJsonValue(data, "sum")
            val amount = amountStr?.toDoubleOrNull()
            val dueDate = extractJsonValue(data, "dueDate") ?: extractJsonValue(data, "due_date")
            val paymentUrl = extractJsonValue(data, "paymentUrl") ?: extractJsonValue(data, "payment_url") ?: extractJsonValue(data, "url")
            val accountNumber = extractJsonValue(data, "account") ?: extractJsonValue(data, "accountNumber")
            val referenceNumber = extractJsonValue(data, "reference") ?: extractJsonValue(data, "ref")

            return QrCodeResult(
                type = QrCodeType.JSON,
                rawData = data,
                title = title,
                amount = amount,
                dueDate = dueDate,
                paymentUrl = paymentUrl,
                accountNumber = accountNumber,
                referenceNumber = referenceNumber
            )
        } catch (e: Exception) {
            Log.e("QrCodeProcessor", "Error parsing JSON QR code", e)
            return null
        }
    }

    /**
     * Parse key-value format QR codes.
     */
    private fun parseKeyValueQrCode(data: String): QrCodeResult? {
        val pairs = mutableMapOf<String, String>()

        // Try different separators
        val lines = when {
            data.contains("\n") -> data.split("\n")
            data.contains(";") -> data.split(";")
            data.contains("|") -> data.split("|")
            else -> listOf(data)
        }

        for (line in lines) {
            val keyValue = when {
                line.contains("=") -> line.split("=", limit = 2)
                line.contains(":") -> line.split(":", limit = 2)
                else -> continue
            }

            if (keyValue.size == 2) {
                pairs[keyValue[0].trim().lowercase()] = keyValue[1].trim()
            }
        }

        if (pairs.isEmpty()) return null

        val title = pairs["title"] ?: pairs["name"] ?: pairs["description"] ?: pairs["bill"]
        val amountStr = pairs["amount"] ?: pairs["total"] ?: pairs["sum"] ?: pairs["price"]
        val amount = amountStr?.toDoubleOrNull()
        val dueDate = pairs["duedate"] ?: pairs["due_date"] ?: pairs["date"]
        val paymentUrl = pairs["paymenturl"] ?: pairs["payment_url"] ?: pairs["url"]
        val accountNumber = pairs["account"] ?: pairs["accountnumber"] ?: pairs["iban"]
        val referenceNumber = pairs["reference"] ?: pairs["ref"] ?: pairs["id"]

        return QrCodeResult(
            type = QrCodeType.KEY_VALUE,
            rawData = data,
            title = title,
            amount = amount,
            dueDate = dueDate,
            paymentUrl = paymentUrl,
            accountNumber = accountNumber,
            referenceNumber = referenceNumber
        )
    }

    /**
     * Parse Data Matrix codes.
     */
    private fun parseDataMatrixCode(data: String): QrCodeResult? {
        // Data Matrix codes often contain structured data similar to QR codes
        return parseQrCodeData(data)
    }

    /**
     * Parse generic barcodes.
     */
    private fun parseGenericBarcode(data: String, format: Int): QrCodeResult? {
        // For generic barcodes, we'll treat them as reference numbers
        return QrCodeResult(
            type = QrCodeType.BARCODE,
            rawData = data,
            title = "Scanned Barcode",
            amount = null,
            dueDate = null,
            paymentUrl = null,
            accountNumber = null,
            referenceNumber = data
        )
    }

    /**
     * Extract title from URL.
     */
    private fun extractTitleFromUrl(url: String): String? {
        // Try to extract meaningful title from URL parameters
        val patterns = listOf("title=", "name=", "description=", "bill=")
        for (pattern in patterns) {
            val index = url.indexOf(pattern, ignoreCase = true)
            if (index != -1) {
                val start = index + pattern.length
                val end = url.indexOf("&", start).takeIf { it != -1 } ?: url.length
                return url.substring(start, end).replace("%20", " ")
            }
        }
        return null
    }

    /**
     * Extract amount from URL.
     */
    private fun extractAmountFromUrl(url: String): Double? {
        val patterns = listOf("amount=", "total=", "sum=", "price=")
        for (pattern in patterns) {
            val index = url.indexOf(pattern, ignoreCase = true)
            if (index != -1) {
                val start = index + pattern.length
                val end = url.indexOf("&", start).takeIf { it != -1 } ?: url.length
                return url.substring(start, end).toDoubleOrNull()
            }
        }
        return null
    }

    /**
     * Simple JSON value extractor.
     */
    private fun extractJsonValue(json: String, key: String): String? {
        val pattern = "\"$key\"\\s*:\\s*\"([^\"]*)\""
        val regex = Regex(pattern, RegexOption.IGNORE_CASE)
        return regex.find(json)?.groupValues?.get(1)
    }

    /**
     * Data class to hold QR code scan results.
     */
    data class QrCodeResult(
        val type: QrCodeType,
        val rawData: String,
        val title: String?,
        val amount: Double?,
        val dueDate: String?,
        val paymentUrl: String?,
        val accountNumber: String?,
        val referenceNumber: String?
    )

    /**
     * Parse UPNQR (Universal Payment QR) format used in Slovenia and other European countries.
     */
    private fun parseUpnqrCode(data: String): QrCodeResult? {
        try {
            val lines = data.split("\n")
            Log.d("QrCodeProcessor", "Parsing UPNQR with ${lines.size} lines")

            // UPNQR format has specific line positions
            // Based on the example: amount is typically at the end, date and reference are in the middle
            var amount: Double? = null
            var dueDate: String? = null
            var title: String? = null
            var payeeName: String? = null
            var referenceNumber: String? = null
            var iban: String? = null

            for (i in lines.indices) {
                val line = lines[i].trim()

                // Skip empty lines
                if (line.isEmpty()) continue

                // Try to parse amount (usually a number at the end)
                if (amount == null) {
                    line.toDoubleOrNull()?.let { rawAmount ->
                        // UPNQR amounts are typically in cents, convert to euros
                        amount = if (rawAmount >= 100) {
                            rawAmount / 100.0
                        } else {
                            rawAmount
                        }
                        Log.d("QrCodeProcessor", "Found amount: $rawAmount -> converted to: $amount")
                    }
                }

                // Try to parse date (DD.MM.YYYY format)
                if (dueDate == null && line.matches(Regex("\\d{2}\\.\\d{2}\\.\\d{4}"))) {
                    dueDate = line
                    Log.d("QrCodeProcessor", "Found due date: $line")
                }

                // Try to parse reference number (contains "računa št." or similar patterns)
                if (referenceNumber == null && (line.contains("računa št.") || line.contains("invoice") || line.matches(Regex(".*\\d{2}-\\d{3}-\\d{7}")))) {
                    referenceNumber = line
                    Log.d("QrCodeProcessor", "Found reference: $line")
                }

                // Try to parse IBAN (starts with SI and contains numbers)
                if (iban == null && line.startsWith("SI") && line.length >= 15) {
                    iban = line
                    Log.d("QrCodeProcessor", "Found IBAN: $line")
                }

                // Try to parse payee name (look for company names with d.o.o., d.d., etc.)
                if (payeeName == null && (line.contains("d.o.o.") || line.contains("d.d.") || line.contains("s.p."))) {
                    payeeName = line
                    Log.d("QrCodeProcessor", "Found payee: $line")
                }
            }

            // Generate title from reference or payee
            title = when {
                referenceNumber != null -> "Payment: ${referenceNumber.take(30)}"
                payeeName != null -> "Payment to $payeeName"
                else -> "UPNQR Payment"
            }

            return QrCodeResult(
                type = QrCodeType.UPNQR,
                rawData = data,
                title = title,
                amount = amount,
                dueDate = dueDate,
                paymentUrl = null,
                accountNumber = iban,
                referenceNumber = referenceNumber
            )
        } catch (e: Exception) {
            Log.e("QrCodeProcessor", "Error parsing UPNQR code", e)
            return null
        }
    }

    /**
     * Enum for different types of QR codes.
     */
    enum class QrCodeType {
        GENERIC,
        PAYMENT_URL,
        EPC_PAYMENT,
        JSON,
        KEY_VALUE,
        BARCODE,
        UPNQR
    }
}
