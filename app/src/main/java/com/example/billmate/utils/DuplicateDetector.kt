package com.example.billmate.utils

import android.util.Log
import com.example.billmate.model.Bill
import java.util.Date
import kotlin.math.abs

/**
 * Utility class for detecting duplicate bills based on various criteria.
 */
object DuplicateDetector {
    
    private const val TAG = "DuplicateDetector"
    
    // Thresholds for duplicate detection
    private const val AMOUNT_TOLERANCE = 0.01 // €0.01 tolerance for amount comparison
    private const val DATE_TOLERANCE_DAYS = 3 // 3 days tolerance for due date comparison
    private const val TITLE_SIMILARITY_THRESHOLD = 0.8 // 80% similarity for title comparison
    
    /**
     * Data class representing a potential duplicate match.
     */
    data class DuplicateMatch(
        val existingBill: Bill,
        val confidence: Double, // 0.0 to 1.0, where 1.0 is exact match
        val matchReasons: List<String>
    )
    
    /**
     * Check if a new bill is a potential duplicate of existing bills.
     * 
     * @param newBill The bill to check for duplicates
     * @param existingBills List of existing bills to compare against
     * @return List of potential duplicate matches, sorted by confidence (highest first)
     */
    fun findPotentialDuplicates(
        newBill: Bill,
        existingBills: List<Bill>
    ): List<DuplicateMatch> {
        Log.d(TAG, "Checking for duplicates of bill: ${newBill.title}, amount: ${newBill.amount}")
        
        val matches = mutableListOf<DuplicateMatch>()
        
        for (existingBill in existingBills) {
            val match = checkBillSimilarity(newBill, existingBill)
            if (match != null) {
                matches.add(match)
            }
        }
        
        // Sort by confidence (highest first)
        val sortedMatches = matches.sortedByDescending { it.confidence }
        
        Log.d(TAG, "Found ${sortedMatches.size} potential duplicates")
        return sortedMatches
    }
    
    /**
     * Check if a new bill is likely a duplicate (high confidence match).
     * 
     * @param newBill The bill to check
     * @param existingBills List of existing bills
     * @param confidenceThreshold Minimum confidence to consider as duplicate (default 0.7)
     * @return The highest confidence duplicate match, or null if no high-confidence duplicate found
     */
    fun findHighConfidenceDuplicate(
        newBill: Bill,
        existingBills: List<Bill>,
        confidenceThreshold: Double = 0.7
    ): DuplicateMatch? {
        val matches = findPotentialDuplicates(newBill, existingBills)
        return matches.firstOrNull { it.confidence >= confidenceThreshold }
    }
    
    /**
     * Check similarity between two bills and return a match if they're similar enough.
     */
    private fun checkBillSimilarity(newBill: Bill, existingBill: Bill): DuplicateMatch? {
        val matchReasons = mutableListOf<String>()
        var totalScore = 0.0
        var maxPossibleScore = 0.0
        
        // 1. Title similarity (weight: 30%)
        val titleWeight = 0.3
        maxPossibleScore += titleWeight
        val titleSimilarity = calculateStringSimilarity(newBill.title, existingBill.title)
        if (titleSimilarity >= TITLE_SIMILARITY_THRESHOLD) {
            totalScore += titleWeight * titleSimilarity
            matchReasons.add("Similar title (${(titleSimilarity * 100).toInt()}% match)")
        }
        
        // 2. Amount similarity (weight: 40%)
        val amountWeight = 0.4
        maxPossibleScore += amountWeight
        if (areAmountsSimilar(newBill.amount, existingBill.amount)) {
            totalScore += amountWeight
            matchReasons.add("Same amount (€${newBill.amount})")
        }
        
        // 3. Due date similarity (weight: 20%)
        val dateWeight = 0.2
        maxPossibleScore += dateWeight
        if (areDatesSimilar(newBill.dueDate, existingBill.dueDate)) {
            totalScore += dateWeight
            matchReasons.add("Similar due date")
        }
        
        // 4. Category match (weight: 10%)
        val categoryWeight = 0.1
        maxPossibleScore += categoryWeight
        if (newBill.category == existingBill.category) {
            totalScore += categoryWeight
            matchReasons.add("Same category")
        }
        
        // Calculate confidence as percentage of maximum possible score
        val confidence = if (maxPossibleScore > 0) totalScore / maxPossibleScore else 0.0
        
        // Only consider it a potential match if confidence is above 50% and we have at least 2 matching criteria
        return if (confidence >= 0.5 && matchReasons.size >= 2) {
            Log.d(TAG, "Potential duplicate found: ${existingBill.title} (confidence: ${(confidence * 100).toInt()}%)")
            DuplicateMatch(existingBill, confidence, matchReasons)
        } else {
            null
        }
    }
    
    /**
     * Calculate string similarity using Levenshtein distance.
     */
    private fun calculateStringSimilarity(str1: String, str2: String): Double {
        if (str1.isEmpty() && str2.isEmpty()) return 1.0
        if (str1.isEmpty() || str2.isEmpty()) return 0.0
        
        val s1 = str1.lowercase().trim()
        val s2 = str2.lowercase().trim()
        
        if (s1 == s2) return 1.0
        
        val maxLength = maxOf(s1.length, s2.length)
        val distance = levenshteinDistance(s1, s2)
        
        return 1.0 - (distance.toDouble() / maxLength)
    }
    
    /**
     * Calculate Levenshtein distance between two strings.
     */
    private fun levenshteinDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }
        
        for (i in 0..s1.length) {
            dp[i][0] = i
        }
        
        for (j in 0..s2.length) {
            dp[0][j] = j
        }
        
        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }
        
        return dp[s1.length][s2.length]
    }
    
    /**
     * Check if two amounts are similar within tolerance.
     */
    private fun areAmountsSimilar(amount1: Double, amount2: Double): Boolean {
        return abs(amount1 - amount2) <= AMOUNT_TOLERANCE
    }
    
    /**
     * Check if two dates are similar within tolerance.
     */
    private fun areDatesSimilar(date1: Date?, date2: Date?): Boolean {
        if (date1 == null || date2 == null) return false
        
        val diffInMillis = abs(date1.time - date2.time)
        val diffInDays = diffInMillis / (24 * 60 * 60 * 1000)
        
        return diffInDays <= DATE_TOLERANCE_DAYS
    }
}
