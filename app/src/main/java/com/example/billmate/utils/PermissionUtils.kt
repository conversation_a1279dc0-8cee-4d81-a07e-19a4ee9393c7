package com.example.billmate.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat

/**
 * Utility class for checking permissions
 */
object PermissionUtils {
    
    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): <PERSON><PERSON>an {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Check if storage permission is granted
     */
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Check if notification permission is granted
     */
    fun hasNotificationPermission(context: Context): <PERSON><PERSON><PERSON> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // For older versions, notifications are enabled by default
            true
        }
    }
    
    /**
     * Check if all essential permissions are granted
     */
    fun hasAllEssentialPermissions(context: Context): Boolean {
        return hasCameraPermission(context) && hasStoragePermission(context)
    }
    
    /**
     * Check if all permissions (including optional ones) are granted
     */
    fun hasAllPermissions(context: Context): Boolean {
        return hasCameraPermission(context) && 
               hasStoragePermission(context) && 
               hasNotificationPermission(context)
    }
}
