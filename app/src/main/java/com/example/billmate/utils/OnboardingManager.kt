package com.example.billmate.utils

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

// DataStore extension
private val Context.onboardingDataStore: DataStore<Preferences> by preferencesDataStore(name = "onboarding_preferences")

/**
 * Manager for handling onboarding flow state and progress
 */
class OnboardingManager(private val context: Context) {
    companion object {
        private val ONBOARDING_COMPLETED = booleanPreferencesKey("onboarding_completed")
        private val WELCOME_COMPLETED = booleanPreferencesKey("welcome_completed")
        private val PERMISSIONS_COMPLETED = booleanPreferencesKey("permissions_completed")
        private val FEATURES_COMPLETED = booleanPreferencesKey("features_completed")
        private val PREFERENCES_COMPLETED = booleanPreferencesKey("preferences_completed")
        private val FIRST_BILL_COMPLETED = booleanPreferencesKey("first_bill_completed")

        // Singleton instance
        @Volatile
        private var INSTANCE: OnboardingManager? = null

        fun getInstance(context: Context): OnboardingManager {
            return INSTANCE ?: synchronized(this) {
                val instance = OnboardingManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }

    /**
     * Check if onboarding is completed
     */
    val isOnboardingCompleted: Flow<Boolean> = context.onboardingDataStore.data
        .map { preferences ->
            preferences[ONBOARDING_COMPLETED] ?: false
        }

    /**
     * Get individual step completion status
     */
    val welcomeCompleted: Flow<Boolean> = context.onboardingDataStore.data
        .map { preferences -> preferences[WELCOME_COMPLETED] ?: false }

    val permissionsCompleted: Flow<Boolean> = context.onboardingDataStore.data
        .map { preferences -> preferences[PERMISSIONS_COMPLETED] ?: false }

    val featuresCompleted: Flow<Boolean> = context.onboardingDataStore.data
        .map { preferences -> preferences[FEATURES_COMPLETED] ?: false }

    val preferencesCompleted: Flow<Boolean> = context.onboardingDataStore.data
        .map { preferences -> preferences[PREFERENCES_COMPLETED] ?: false }

    val firstBillCompleted: Flow<Boolean> = context.onboardingDataStore.data
        .map { preferences -> preferences[FIRST_BILL_COMPLETED] ?: false }

    /**
     * Mark welcome step as completed
     */
    suspend fun markWelcomeCompleted() {
        context.onboardingDataStore.edit { preferences ->
            preferences[WELCOME_COMPLETED] = true
        }
    }

    /**
     * Mark permissions step as completed
     */
    suspend fun markPermissionsCompleted() {
        context.onboardingDataStore.edit { preferences ->
            preferences[PERMISSIONS_COMPLETED] = true
        }
    }

    /**
     * Mark features tour as completed
     */
    suspend fun markFeaturesCompleted() {
        context.onboardingDataStore.edit { preferences ->
            preferences[FEATURES_COMPLETED] = true
        }
    }

    /**
     * Mark preferences setup as completed
     */
    suspend fun markPreferencesCompleted() {
        context.onboardingDataStore.edit { preferences ->
            preferences[PREFERENCES_COMPLETED] = true
        }
    }

    /**
     * Mark first bill guide as completed
     */
    suspend fun markFirstBillCompleted() {
        context.onboardingDataStore.edit { preferences ->
            preferences[FIRST_BILL_COMPLETED] = true
        }
    }

    /**
     * Mark entire onboarding as completed
     */
    suspend fun markOnboardingCompleted() {
        context.onboardingDataStore.edit { preferences ->
            preferences[ONBOARDING_COMPLETED] = true
            preferences[WELCOME_COMPLETED] = true
            preferences[PERMISSIONS_COMPLETED] = true
            preferences[FEATURES_COMPLETED] = true
            preferences[PREFERENCES_COMPLETED] = true
            preferences[FIRST_BILL_COMPLETED] = true
        }

        // Log onboarding completion
        AnalyticsManager.logCustomEvent("onboarding_completed", android.os.Bundle())
    }

    /**
     * Reset onboarding (for testing purposes)
     */
    suspend fun resetOnboarding() {
        context.onboardingDataStore.edit { preferences ->
            preferences.clear()
        }
    }

    /**
     * Get onboarding progress (0.0 to 1.0)
     */
    suspend fun getOnboardingProgress(): Flow<Float> = context.onboardingDataStore.data
        .map { preferences ->
            val steps = listOf(
                preferences[WELCOME_COMPLETED] ?: false,
                preferences[PERMISSIONS_COMPLETED] ?: false,
                preferences[FEATURES_COMPLETED] ?: false,
                preferences[PREFERENCES_COMPLETED] ?: false,
                preferences[FIRST_BILL_COMPLETED] ?: false
            )
            val completedSteps = steps.count { it }
            completedSteps.toFloat() / steps.size
        }
}
