package com.example.billmate.utils

import android.content.Context
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.storage.FirebaseStorage

/**
 * Manager class for Firebase initialization and configuration.
 */
object FirebaseManager {
    
    /**
     * Initialize Firebase components.
     */
    fun initialize(context: Context) {
        // Initialize Firebase
        if (FirebaseApp.getApps(context).isEmpty()) {
            FirebaseApp.initializeApp(context)
        }
        
        // Configure Firestore
        val firestore = FirebaseFirestore.getInstance()
        val settings = FirebaseFirestoreSettings.Builder()
            .setPersistenceEnabled(true) // Enable offline persistence
            .build()
        firestore.firestoreSettings = settings
        
        // Log initialization
        AnalyticsManager.logCustomEvent("firebase_initialized", android.os.Bundle())
    }
    
    /**
     * Get the current user ID or null if not logged in.
     */
    fun getCurrentUserId(): String? {
        return FirebaseAuth.getInstance().currentUser?.uid
    }
    
    /**
     * Check if a user is currently logged in.
     */
    fun isUserLoggedIn(): Boolean {
        return FirebaseAuth.getInstance().currentUser != null
    }
    
    /**
     * Get a reference to a bill image in Firebase Storage.
     */
    fun getBillImageReference(billId: String) = 
        FirebaseStorage.getInstance().reference
            .child("bills")
            .child(getCurrentUserId() ?: "anonymous")
            .child(billId)
}
