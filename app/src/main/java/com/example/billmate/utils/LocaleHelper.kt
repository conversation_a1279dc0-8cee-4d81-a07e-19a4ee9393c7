package com.example.billmate.utils

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.os.LocaleList
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import java.util.Locale

/**
 * Helper class for locale/language operations.
 */
object LocaleHelper {
    private const val TAG = "LocaleHelper"
    private const val LANGUAGE_PREFERENCE = "language_preference"

    /**
     * Set the app language.
     */
    fun setLocale(context: Context, languageCode: String): Context {
        Log.d(TAG, "Setting locale to: $languageCode")

        try {
            // Save the selected language
            saveLanguagePreference(context, languageCode)

            // Create a locale from the language code
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            // Update the app's resources
            val resources = context.resources
            val configuration = Configuration(resources.configuration)

            // Set the locale based on Android version
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val localeList = LocaleList(locale)
                LocaleList.setDefault(localeList)
                configuration.setLocales(localeList)
            } else {
                @Suppress("DEPRECATION")
                configuration.locale = locale
            }

            // Update the configuration
            @Suppress("DEPRECATION")
            resources.updateConfiguration(configuration, resources.displayMetrics)

            // Also set the AppCompatDelegate locales for better compatibility
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)

            // Create a new context with the updated configuration
            return context.createConfigurationContext(configuration)
        } catch (e: Exception) {
            Log.e(TAG, "Error setting locale: ${e.message}", e)
            return context
        }
    }

    /**
     * Get the current language code.
     */
    fun getLanguage(context: Context): String {
        val preferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        val defaultLanguage = Locale.getDefault().language
        val savedLanguage = preferences.getString(LANGUAGE_PREFERENCE, defaultLanguage) ?: defaultLanguage
        Log.d(TAG, "Current language: $savedLanguage")
        return savedLanguage
    }

    /**
     * Save the selected language code to preferences.
     */
    private fun saveLanguagePreference(context: Context, languageCode: String) {
        val preferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        preferences.edit().putString(LANGUAGE_PREFERENCE, languageCode).apply()
        Log.d(TAG, "Saved language preference: $languageCode")
    }

    /**
     * Update the app's resources with the selected language.
     */
    private fun updateResources(context: Context, languageCode: String): Context {
        try {
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            val resources = context.resources
            val configuration = Configuration(resources.configuration)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val localeList = LocaleList(locale)
                LocaleList.setDefault(localeList)
                configuration.setLocales(localeList)
                return context.createConfigurationContext(configuration)
            } else {
                @Suppress("DEPRECATION")
                configuration.locale = locale
                @Suppress("DEPRECATION")
                resources.updateConfiguration(configuration, resources.displayMetrics)
                return context
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating resources: ${e.message}", e)
            return context
        }
    }

    /**
     * Apply the saved language to the given context.
     */
    fun applyLanguage(context: Context): Context {
        val languageCode = getLanguage(context)
        Log.d(TAG, "Applying saved language: $languageCode")
        return updateResources(context, languageCode)
    }

    /**
     * Check if the device language is supported by the app.
     */
    fun isLanguageSupported(languageCode: String): Boolean {
        return languageCode == "en" || languageCode == "sl"
    }

    /**
     * Get the display name of the language.
     */
    fun getLanguageDisplayName(languageCode: String): String {
        return when (languageCode) {
            "en" -> "English"
            "sl" -> "Slovenščina"
            else -> Locale(languageCode).displayLanguage
        }
    }
}
