package com.example.billmate.utils

import android.content.Context
import android.util.Log
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.example.billmate.service.BillCheckWorker
import com.example.billmate.service.NotificationService
import java.util.Calendar
import java.util.concurrent.TimeUnit

/**
 * Utility class for scheduling background work with WorkManager.
 */
object WorkManagerUtil {
    private const val TAG = "WorkManagerUtil"
    
    /**
     * Schedule the bill check worker to run daily at the user's preferred notification time.
     */
    fun scheduleBillCheckWorker(context: Context) {
        val notificationService = NotificationService(context)
        
        // If notifications are disabled, cancel any existing work
        if (!notificationService.areNotificationsEnabled()) {
            Log.d(TAG, "Notifications are disabled, canceling bill check worker")
            WorkManager.getInstance(context).cancelUniqueWork(BillCheckWorker.WORK_NAME)
            return
        }
        
        // Calculate initial delay to the next notification time
        val initialDelay = calculateInitialDelay(
            notificationService.getNotificationHour(),
            notificationService.getNotificationMinute()
        )
        
        Log.d(TAG, "Scheduling bill check worker with initial delay of $initialDelay minutes")
        
        // Define constraints - we need network connectivity to check bills
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        
        // Create a periodic work request that runs daily
        val billCheckRequest = PeriodicWorkRequestBuilder<BillCheckWorker>(
            24, TimeUnit.HOURS
        )
            .setConstraints(constraints)
            .setInitialDelay(initialDelay, TimeUnit.MINUTES)
            .build()
        
        // Schedule the work
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            BillCheckWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.UPDATE,
            billCheckRequest
        )
        
        Log.d(TAG, "Bill check worker scheduled")
    }
    
    /**
     * Calculate the initial delay in minutes until the next notification time.
     */
    private fun calculateInitialDelay(hour: Int, minute: Int): Long {
        val now = Calendar.getInstance()
        val target = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // If the target time is already past for today, schedule for tomorrow
            if (before(now)) {
                add(Calendar.DAY_OF_YEAR, 1)
            }
        }
        
        val diffInMillis = target.timeInMillis - now.timeInMillis
        return TimeUnit.MILLISECONDS.toMinutes(diffInMillis)
    }
}
