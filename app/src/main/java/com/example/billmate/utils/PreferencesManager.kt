package com.example.billmate.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * Utility class for managing shared preferences.
 */
object PreferencesManager {
    private const val TAG = "PreferencesManager"
    private const val PREFERENCES_NAME = "billmate_preferences"
    
    /**
     * Get the shared preferences instance.
     */
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFERENCES_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * Get a boolean value from shared preferences.
     */
    fun getBoolean(context: Context, key: String, defaultValue: Boolean): Boolean {
        return getPreferences(context).getBoolean(key, defaultValue)
    }
    
    /**
     * Set a boolean value in shared preferences.
     */
    fun setBoolean(context: Context, key: String, value: Boolean) {
        getPreferences(context).edit().putBoolean(key, value).apply()
        Log.d(TAG, "Set boolean preference: $key = $value")
    }
    
    /**
     * Get a string value from shared preferences.
     */
    fun getString(context: Context, key: String, defaultValue: String): String {
        return getPreferences(context).getString(key, defaultValue) ?: defaultValue
    }
    
    /**
     * Set a string value in shared preferences.
     */
    fun setString(context: Context, key: String, value: String) {
        getPreferences(context).edit().putString(key, value).apply()
        Log.d(TAG, "Set string preference: $key = $value")
    }
    
    /**
     * Get an integer value from shared preferences.
     */
    fun getInt(context: Context, key: String, defaultValue: Int): Int {
        return getPreferences(context).getInt(key, defaultValue)
    }
    
    /**
     * Set an integer value in shared preferences.
     */
    fun setInt(context: Context, key: String, value: Int) {
        getPreferences(context).edit().putInt(key, value).apply()
        Log.d(TAG, "Set int preference: $key = $value")
    }
    
    /**
     * Get a long value from shared preferences.
     */
    fun getLong(context: Context, key: String, defaultValue: Long): Long {
        return getPreferences(context).getLong(key, defaultValue)
    }
    
    /**
     * Set a long value in shared preferences.
     */
    fun setLong(context: Context, key: String, value: Long) {
        getPreferences(context).edit().putLong(key, value).apply()
        Log.d(TAG, "Set long preference: $key = $value")
    }
    
    /**
     * Get a float value from shared preferences.
     */
    fun getFloat(context: Context, key: String, defaultValue: Float): Float {
        return getPreferences(context).getFloat(key, defaultValue)
    }
    
    /**
     * Set a float value in shared preferences.
     */
    fun setFloat(context: Context, key: String, value: Float) {
        getPreferences(context).edit().putFloat(key, value).apply()
        Log.d(TAG, "Set float preference: $key = $value")
    }
    
    /**
     * Remove a value from shared preferences.
     */
    fun remove(context: Context, key: String) {
        getPreferences(context).edit().remove(key).apply()
        Log.d(TAG, "Removed preference: $key")
    }
    
    /**
     * Clear all shared preferences.
     */
    fun clear(context: Context) {
        getPreferences(context).edit().clear().apply()
        Log.d(TAG, "Cleared all preferences")
    }
}
