package com.example.billmate.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.suspendCancellableCoroutine
import java.text.NumberFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Utility class to process OCR results from bill images.
 */
class OcrProcessor {
    private val recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

    /**
     * Process an image and extract text using ML Kit.
     */
    suspend fun processImage(bitmap: Bitmap): Text = suspendCancellableCoroutine { continuation ->
        val image = InputImage.fromBitmap(bitmap, 0)
        recognizer.process(image)
            .addOnSuccessListener { text ->
                continuation.resume(text)
            }
            .addOnFailureListener { e ->
                continuation.resumeWithException(e)
            }
    }

    /**
     * Process an image from byte array and extract text using ML Kit.
     */
    suspend fun processImageBytes(imageBytes: ByteArray): Text {
        val bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
        return processImage(bitmap)
    }

    /**
     * Process an image from URI and extract bill information.
     */
    suspend fun processImageUri(context: Context, imageUri: Uri): BillInfo {
        val inputImage = InputImage.fromFilePath(context, imageUri)
        val text = suspendCancellableCoroutine<Text> { continuation ->
            recognizer.process(inputImage)
                .addOnSuccessListener { text ->
                    continuation.resume(text)
                }
                .addOnFailureListener { e ->
                    continuation.resumeWithException(e)
                }
        }
        return extractBillInfo(text)
    }

    /**
     * Extract bill information from OCR text.
     */
    fun extractBillInfo(text: Text): BillInfo {
        val fullText = text.text
        Log.d("OcrProcessor", "=== Starting bill info extraction ===")
        Log.d("OcrProcessor", "Full extracted text: $fullText")
        Log.d("OcrProcessor", "Text blocks count: ${text.textBlocks.size}")

        // Log individual text blocks for debugging
        text.textBlocks.forEachIndexed { index, block ->
            Log.d("OcrProcessor", "Block $index: ${block.text}")
        }

        // Extract potential bill information
        val amount = extractAmount(fullText)
        val dueDate = extractDate(fullText)
        val title = extractTitle(fullText)

        Log.d("OcrProcessor", "=== Extraction results ===")
        Log.d("OcrProcessor", "Title: $title")
        Log.d("OcrProcessor", "Amount: $amount")
        Log.d("OcrProcessor", "Due date: $dueDate")

        return BillInfo(
            title = title,
            amount = amount,
            dueDate = dueDate,
            rawText = fullText
        )
    }

    /**
     * Extract monetary amount from text.
     */
    private fun extractAmount(text: String): Double? {
        Log.d("OcrProcessor", "Extracting amount from text: $text")

        // Enhanced patterns for European/Slovenian bills - ordered by priority
        val patterns = listOf(
            // Highest priority: Final total with "Skupaj" keyword
            Pattern.compile("skupaj\\s*:?\\s*(\\d{1,4}(?:[.,]\\d{2,3})*(?:[.,]\\d{2}))\\s*EUR", Pattern.CASE_INSENSITIVE),
            // Total amounts in various formats
            Pattern.compile("(?:total|znesek|amount|plačilo|payment)\\s*:?\\s*(\\d{1,4}(?:[.,]\\d{2,3})*(?:[.,]\\d{2}))\\s*EUR", Pattern.CASE_INSENSITIVE),
            // Amount with currency symbol: €427.00 or € 427,00
            Pattern.compile("€\\s*(\\d{1,4}(?:[.,]\\d{2,3})*(?:[.,]\\d{2}))"),
            // European format: 427.00 EUR or 427,00 EUR (but not base amounts)
            Pattern.compile("(\\d{1,4}(?:[.,]\\d{2,3})*(?:[.,]\\d{2}))\\s*EUR", Pattern.CASE_INSENSITIVE),
            // $123.45 or $123,45 (fallback for other currencies)
            Pattern.compile("\\$(\\d{1,4}(?:[.,]\\d{3})*(?:[.,]\\d{2})?)"),
            // Numbers with decimal points that might be amounts (lowest priority)
            Pattern.compile("(\\d{1,4}(?:[.,]\\d{2,3})*(?:[.,]\\d{2}))(?=\\s|$|\\n)")
        )

        val foundAmounts = mutableListOf<Double>()

        // Try patterns in order of preference and collect all amounts
        for (pattern in patterns) {
            val matcher = pattern.matcher(text)
            while (matcher.find()) {
                try {
                    val amountStr = matcher.group(1)
                        ?.replace(",", ".") // Normalize decimal separator to dot
                        ?.replace(" ", "") // Remove spaces
                        ?.trim()

                    Log.d("OcrProcessor", "Found potential amount: $amountStr")

                    amountStr?.let { str ->
                        val amount = str.toDoubleOrNull()
                        if (amount != null && amount > 0 && amount < 100000) { // Reasonable range
                            Log.d("OcrProcessor", "Valid amount found: $amount")
                            foundAmounts.add(amount)

                            // If this is from a high-priority pattern (first 3), return immediately
                            if (patterns.indexOf(pattern) < 3) {
                                Log.d("OcrProcessor", "High priority amount extracted: $amount")
                                return amount
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e("OcrProcessor", "Error parsing amount: ${matcher.group(1)}", e)
                }
            }
        }

        // If we found amounts but none from high-priority patterns, return the largest
        if (foundAmounts.isNotEmpty()) {
            val maxAmount = foundAmounts.maxOrNull()
            Log.d("OcrProcessor", "Returning largest amount found: $maxAmount")
            return maxAmount
        }

        Log.d("OcrProcessor", "No amount found in text")
        return null
    }

    /**
     * Extract date from text.
     */
    private fun extractDate(text: String): Date? {
        Log.d("OcrProcessor", "Extracting date from text: $text")

        // Enhanced date formats including Slovenian/European formats
        val dateFormats = listOf(
            "dd.MM.yyyy",    // Slovenian format: 11.05.2018
            "dd/MM/yyyy",    // European format: 11/05/2018
            "yyyy-MM-dd",    // ISO format: 2018-05-11
            "dd-MM-yyyy",    // Alternative European: 11-05-2018
            "dd. MMM yyyy",  // European month name format: 11. maj 2018
            "dd MMM yyyy",   // European month name: 11 maj 2018
            "MM/dd/yyyy",    // US format (fallback)
            "MMM dd, yyyy",  // US month name format
            "MMMM dd, yyyy"  // Full month name
        )

        // Enhanced date patterns
        val patterns = listOf(
            // DD.MM.YYYY (Slovenian format)
            Pattern.compile("(\\d{1,2}\\.\\d{1,2}\\.\\d{4})"),
            // DD/MM/YYYY or MM/DD/YYYY
            Pattern.compile("(\\d{1,2}/\\d{1,2}/\\d{4})"),
            // YYYY-MM-DD
            Pattern.compile("(\\d{4}-\\d{1,2}-\\d{1,2})"),
            // DD-MM-YYYY
            Pattern.compile("(\\d{1,2}-\\d{1,2}-\\d{4})"),
            // Month DD, YYYY
            Pattern.compile("([A-Za-z]{3,9}\\s+\\d{1,2},?\\s+\\d{4})")
        )

        // Enhanced date keywords (Slovenian and English)
        val dateKeywords = listOf(
            "datum", "due date", "payment date", "due by", "pay by",
            "rok plačila", "datum zapadlosti", "plačilo do", "zapadlost",
            "datum izstavitve", "izdano", "issued"
        )

        val lines = text.split("\n")

        // First, look for dates with keywords
        for (i in lines.indices) {
            val line = lines[i]
            val lowerLine = line.lowercase(Locale.getDefault())
            Log.d("OcrProcessor", "Checking line for date keywords: $line")

            // Check if line contains date keywords
            if (dateKeywords.any { lowerLine.contains(it) }) {
                Log.d("OcrProcessor", "Found date keyword in line: $line")

                // Check current line for date
                for (pattern in patterns) {
                    val matcher = pattern.matcher(line)
                    if (matcher.find()) {
                        val dateStr = matcher.group(1) ?: continue
                        Log.d("OcrProcessor", "Found potential date with keyword in same line: $dateStr")

                        for (format in dateFormats) {
                            try {
                                val sdf = SimpleDateFormat(format, Locale.getDefault())
                                sdf.isLenient = false
                                val date = sdf.parse(dateStr)
                                Log.d("OcrProcessor", "Successfully parsed date: $date")
                                return date
                            } catch (e: ParseException) {
                                // Try next format
                            }
                        }
                    }
                }

                // Check next line for date (common in Slovenian bills)
                if (i + 1 < lines.size) {
                    val nextLine = lines[i + 1]
                    Log.d("OcrProcessor", "Checking next line for date: $nextLine")

                    for (pattern in patterns) {
                        val matcher = pattern.matcher(nextLine)
                        if (matcher.find()) {
                            val dateStr = matcher.group(1) ?: continue
                            Log.d("OcrProcessor", "Found potential date in next line: $dateStr")

                            for (format in dateFormats) {
                                try {
                                    val sdf = SimpleDateFormat(format, Locale.getDefault())
                                    sdf.isLenient = false
                                    val date = sdf.parse(dateStr)
                                    Log.d("OcrProcessor", "Successfully parsed date from next line: $date")
                                    return date
                                } catch (e: ParseException) {
                                    // Try next format
                                }
                            }
                        }
                    }
                }
            }
        }

        // If no date found with keywords, try to find any date
        Log.d("OcrProcessor", "No date found with keywords, trying all dates")
        for (pattern in patterns) {
            val matcher = pattern.matcher(text)
            while (matcher.find()) {
                val dateStr = matcher.group(1) ?: continue
                Log.d("OcrProcessor", "Found potential date: $dateStr")

                for (format in dateFormats) {
                    try {
                        val sdf = SimpleDateFormat(format, Locale.getDefault())
                        sdf.isLenient = false
                        val date = sdf.parse(dateStr) ?: continue
                        // Check if date is reasonable (not too far in past/future)
                        val currentTime = System.currentTimeMillis()
                        val dateTime = date.time
                        if (dateTime > currentTime - (365L * 24 * 60 * 60 * 1000) && // Not older than 1 year
                            dateTime < currentTime + (365L * 24 * 60 * 60 * 1000)) {   // Not more than 1 year in future
                            Log.d("OcrProcessor", "Successfully parsed reasonable date: $date")
                            return date
                        }
                    } catch (e: ParseException) {
                        // Try next format
                    }
                }
            }
        }

        Log.d("OcrProcessor", "No date found in text")
        return null
    }

    /**
     * Extract bill title from text.
     */
    private fun extractTitle(text: String): String {
        Log.d("OcrProcessor", "Extracting title from text: $text")

        val lines = text.split("\n").map { it.trim() }.filter { it.isNotEmpty() }

        // Skip common header elements and look for company name
        val skipPatterns = listOf(
            Regex("\\d+"),                    // Pure numbers
            Regex(".*\\d{2}\\.\\d{2}\\.\\d{4}.*"), // Dates
            Regex(".*EUR.*", RegexOption.IGNORE_CASE), // Lines with EUR
            Regex(".*račun.*", RegexOption.IGNORE_CASE), // "račun" (invoice)
            Regex(".*invoice.*", RegexOption.IGNORE_CASE), // "invoice"
            Regex(".*datum.*", RegexOption.IGNORE_CASE),   // "datum" (date)
            Regex(".*znesek.*", RegexOption.IGNORE_CASE),  // "znesek" (amount)
            Regex(".*skupaj.*", RegexOption.IGNORE_CASE),  // "skupaj" (total)
            Regex(".*ddv.*", RegexOption.IGNORE_CASE),     // "DDV" (VAT)
            Regex(".*\\d+[.,]\\d+.*")         // Lines with decimal numbers
        )

        // Look for company name - typically one of the first few lines
        for (line in lines.take(5)) {
            val shouldSkip = skipPatterns.any { it.matches(line) }

            if (!shouldSkip &&
                line.length > 3 &&
                line.length < 100 && // Reasonable company name length
                !line.matches(Regex("^[\\d\\s.,]+$"))) { // Not just numbers and punctuation

                Log.d("OcrProcessor", "Found potential company name: $line")
                return line
            }
        }

        // Look for lines with Slovenian/English business keywords
        val businessKeywords = listOf(
            "d.o.o.", "s.p.", "d.d.", "družba", "podjetje", "company",
            "ltd", "inc", "corp", "gmbh", "društvo"
        )

        for (line in lines) {
            val lowerLine = line.lowercase(Locale.getDefault())
            if (businessKeywords.any { lowerLine.contains(it) } &&
                line.length > 5 &&
                !skipPatterns.any { it.matches(line) }) {
                Log.d("OcrProcessor", "Found business entity: $line")
                return line
            }
        }

        // Look for lines with "invoice", "bill", "statement", "račun"
        val documentKeywords = listOf("invoice", "bill", "statement", "receipt", "račun", "faktura")
        for (line in lines) {
            val lowerLine = line.lowercase(Locale.getDefault())
            if (documentKeywords.any { lowerLine.contains(it) } &&
                !lowerLine.contains("pay") &&
                !lowerLine.contains("due") &&
                !lowerLine.contains("plačilo")) {
                Log.d("OcrProcessor", "Found document type: $line")
                return line
            }
        }

        // Default to first meaningful line
        val firstLine = lines.firstOrNull { line ->
            line.length > 3 &&
            !skipPatterns.any { it.matches(line) }
        }

        val result = firstLine ?: "New Bill"
        Log.d("OcrProcessor", "Using title: $result")
        return result
    }

    /**
     * Data class to hold extracted bill information.
     */
    data class BillInfo(
        val title: String,
        val amount: Double?,
        val dueDate: Date?,
        val rawText: String
    )
}
