package com.example.billmate

import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.util.Log
import com.example.billmate.service.NotificationService
import com.example.billmate.utils.FirebaseManager
import com.example.billmate.utils.LocaleHelper
import com.example.billmate.utils.WorkManagerUtil

/**
 * Application class for BillMate.
 */
class BillMateApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // Initialize Firebase
        FirebaseManager.initialize(this)

        // Apply saved language
        LocaleHelper.applyLanguage(this)

        // Initialize notification channels
        val notificationService = NotificationService(this)
        notificationService.createNotificationChannels()

        // Schedule bill check worker if notifications are enabled
        if (notificationService.areNotificationsEnabled()) {
            WorkManagerUtil.scheduleBillCheckWorker(this)
        }

        Log.d("BillMateApplication", "Application initialized")
    }

    override fun attachBaseContext(base: Context) {
        // Apply the saved language to the base context
        val context = LocaleHelper.applyLanguage(base)
        super.attachBaseContext(context)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d("BillMateApplication", "Configuration changed")
    }
}
