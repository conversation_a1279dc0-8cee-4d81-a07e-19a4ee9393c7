package com.example.billmate.navigation

import android.net.Uri
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.example.billmate.repository.AuthRepository
import com.example.billmate.repository.EmailRepository
import com.example.billmate.ui.screens.AddEditBillScreen
import com.example.billmate.ui.screens.BillDetailScreen
import com.example.billmate.ui.screens.BillListScreen

import com.example.billmate.ui.screens.EmailBillsScreen
import com.example.billmate.ui.screens.EmailSetupScreen
import com.example.billmate.ui.screens.ForgotPasswordScreen
import com.example.billmate.ui.screens.HomeScreen
import com.example.billmate.ui.screens.LanguageSelectionScreen
import com.example.billmate.ui.screens.LoginScreen
import com.example.billmate.ui.screens.NotificationsScreen
import com.example.billmate.ui.screens.NotificationSettingsScreen
import com.example.billmate.ui.screens.ProfileScreen
import com.example.billmate.ui.screens.RegisterScreen
import com.example.billmate.ui.screens.ScanBillScreen
import com.example.billmate.ui.screens.QrCodeScanScreen
import com.example.billmate.ui.screens.DocumentScanScreen
import com.example.billmate.ui.screens.GalleryScanScreen
import com.example.billmate.ui.screens.StatisticsScreen
import com.example.billmate.ui.screens.onboarding.WelcomeScreen
import com.example.billmate.ui.screens.onboarding.PermissionsScreen
import com.example.billmate.ui.screens.onboarding.FeatureTourScreen
import com.example.billmate.ui.screens.onboarding.PreferencesSetupScreen
import com.example.billmate.ui.screens.onboarding.FirstBillGuideScreen
import com.example.billmate.utils.OnboardingManager
import com.example.billmate.viewmodel.BillViewModel
import kotlinx.coroutines.launch


/**
 * Navigation routes for the app.
 */
sealed class Screen(val route: String) {
    object Login : Screen("login")
    object Register : Screen("register")
    object ForgotPassword : Screen("forgot_password")
    object Home : Screen("home")
    object Profile : Screen("profile")
    object ScanBill : Screen("scan_bill")
    object QrCodeScan : Screen("qr_code_scan")
    object DocumentScan : Screen("document_scan")
    object GalleryScan : Screen("gallery_scan")
    object Statistics : Screen("statistics")
    object LanguageSelection : Screen("language_selection")
    object Notifications : Screen("notifications")
    object NotificationSettings : Screen("notification_settings")

    object BillList : Screen("bill_list?filter={filter}") {
        fun createRoute(filter: String? = null): String {
            return if (filter != null) "bill_list?filter=$filter" else "bill_list"
        }
    }
    object EmailSetup : Screen("email_setup")
    object EmailBills : Screen("email_bills")
    object AddEditBill : Screen("add_edit_bill?billId={billId}&title={title}&amount={amount}&dueDate={dueDate}") {
        fun createRoute(
            billId: String? = null,
            title: String? = null,
            amount: Double? = null,
            dueDate: String? = null
        ): String {
            val params = mutableListOf<String>()
            billId?.let { params.add("billId=${Uri.encode(it)}") }
            title?.let { params.add("title=${Uri.encode(it)}") }
            amount?.let { params.add("amount=${Uri.encode(it.toString())}") }
            dueDate?.let { params.add("dueDate=${Uri.encode(it)}") }

            return "add_edit_bill" + if (params.isNotEmpty()) "?${params.joinToString("&")}" else ""
        }
    }
    object BillDetail : Screen("bill_detail/{billId}") {
        fun createRoute(billId: String): String {
            return "bill_detail/$billId"
        }
    }

    // Onboarding screens
    object OnboardingWelcome : Screen("onboarding_welcome")
    object OnboardingPermissions : Screen("onboarding_permissions")
    object OnboardingFeatures : Screen("onboarding_features")
    object OnboardingPreferences : Screen("onboarding_preferences")
    object OnboardingFirstBill : Screen("onboarding_first_bill")
}

/**
 * Main navigation component for the app.
 */
@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController(),
    authRepository: AuthRepository = AuthRepository(),
    billViewModel: BillViewModel = androidx.lifecycle.viewmodel.compose.viewModel(),
    emailRepository: EmailRepository = EmailRepository(androidx.compose.ui.platform.LocalContext.current)
) {
    val context = androidx.compose.ui.platform.LocalContext.current
    val currentUserState by authRepository.getCurrentUserFlow().collectAsState(initial = null)
    val onboardingManager = OnboardingManager.getInstance(context)
    val isOnboardingCompleted by onboardingManager.isOnboardingCompleted.collectAsState(initial = false)
    val coroutineScope = rememberCoroutineScope()

    // Determine start destination based on auth and onboarding status
    val startDestination = when {
        currentUserState == null -> Screen.Login.route
        !isOnboardingCompleted -> Screen.OnboardingWelcome.route
        else -> Screen.Home.route
    }

    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Authentication screens
        composable(Screen.Login.route) {
            LoginScreen(
                onNavigateToRegister = { navController.navigate(Screen.Register.route) },
                onNavigateToForgotPassword = { navController.navigate(Screen.ForgotPassword.route) },
                onLoginSuccess = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.Login.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.Register.route) {
            RegisterScreen(
                onNavigateToLogin = { navController.navigate(Screen.Login.route) },
                onRegisterSuccess = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.Register.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.ForgotPassword.route) {
            ForgotPasswordScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Onboarding screens
        composable(Screen.OnboardingWelcome.route) {
            WelcomeScreen(
                onContinue = { navController.navigate(Screen.OnboardingPermissions.route) },
                onSkip = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.OnboardingWelcome.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.OnboardingPermissions.route) {
            PermissionsScreen(
                onContinue = { navController.navigate(Screen.OnboardingFeatures.route) },
                onSkip = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.OnboardingWelcome.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.OnboardingFeatures.route) {
            FeatureTourScreen(
                onContinue = { navController.navigate(Screen.OnboardingPreferences.route) },
                onSkip = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.OnboardingWelcome.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.OnboardingPreferences.route) {
            PreferencesSetupScreen(
                onContinue = { navController.navigate(Screen.OnboardingFirstBill.route) },
                onSkip = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.OnboardingWelcome.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.OnboardingFirstBill.route) {
            FirstBillGuideScreen(
                onAddManually = {
                    navController.navigate(Screen.AddEditBill.createRoute())
                },
                onScanText = {
                    navController.navigate(Screen.ScanBill.route)
                },
                onScanQR = {
                    navController.navigate(Screen.QrCodeScan.route)
                },
                onScanDocument = {
                    navController.navigate(Screen.DocumentScan.route)
                },
                onScanFromGallery = {
                    navController.navigate(Screen.GalleryScan.route)
                },
                onFinishOnboarding = {
                    navController.navigate(Screen.Home.route) {
                        popUpTo(Screen.OnboardingWelcome.route) { inclusive = true }
                    }
                }
            )
        }

        // Main app screens
        composable(Screen.Home.route) {
            HomeScreen(
                onNavigateToAddBill = { navController.navigate(Screen.AddEditBill.createRoute()) },
                onNavigateToScanBill = { navController.navigate(Screen.ScanBill.route) },
                onNavigateToQrCodeScan = { navController.navigate(Screen.QrCodeScan.route) },
                onNavigateToDocumentScan = { navController.navigate(Screen.DocumentScan.route) },
                onNavigateToGalleryScan = { navController.navigate(Screen.GalleryScan.route) },
                onNavigateToStatistics = { navController.navigate(Screen.Statistics.route) },

                onNavigateToBillDetail = { billId ->
                    navController.navigate(Screen.BillDetail.createRoute(billId))
                },
                onNavigateToEmailSetup = { navController.navigate(Screen.EmailSetup.route) },
                navController = navController,
                billRepository = billViewModel.billRepository
            )
        }

        composable(
            route = Screen.BillList.route,
            arguments = listOf(
                navArgument("filter") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val filterParam = backStackEntry.arguments?.getString("filter")
            BillListScreen(
                onNavigateToBillDetail = { billId ->
                    navController.navigate(Screen.BillDetail.createRoute(billId))
                },
                navController = navController,
                billRepository = billViewModel.billRepository,
                initialFilter = filterParam
            )
        }

        composable(Screen.Profile.route) {
            ProfileScreen(
                onNavigateToLanguageSelection = { navController.navigate(Screen.LanguageSelection.route) },
                onNavigateToNotifications = { navController.navigate(Screen.NotificationSettings.route) },
                onSignOut = {
                    authRepository.signOut()
                    navController.navigate(Screen.Login.route) {
                        popUpTo(navController.graph.id) { inclusive = true }
                    }
                },
                navController = navController
            )
        }

        composable(Screen.Notifications.route) {
            NotificationsScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        composable(Screen.NotificationSettings.route) {
            NotificationSettingsScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        composable(Screen.LanguageSelection.route) {
            LanguageSelectionScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        composable(Screen.Statistics.route) {
            StatisticsScreen(
                navController = navController
            )
        }

        composable(Screen.ScanBill.route) {
            ScanBillScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onScanComplete = { billData ->
                    Log.d("AppNavigation", "Received OCR scan data: $billData")

                    // Extract data from the scan result
                    val title = billData["title"]
                    val amountStr = billData["amount"]
                    val amount = amountStr?.toDoubleOrNull()
                    val dueDate = billData["dueDate"]

                    Log.d("AppNavigation", "Parsed OCR data: title=$title, amount=$amount, dueDate=$dueDate")

                    // Navigate to add bill screen with extracted data
                    navController.navigate(
                        Screen.AddEditBill.createRoute(
                            title = title,
                            amount = amount,
                            dueDate = dueDate
                        )
                    )
                }
            )
        }

        composable(Screen.QrCodeScan.route) {
            QrCodeScanScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onScanComplete = { billData ->
                    Log.d("AppNavigation", "Received QR scan data: $billData")

                    // Extract data from the scan result
                    val title = billData["title"]
                    val amountStr = billData["amount"]
                    val amount = amountStr?.toDoubleOrNull()
                    val dueDate = billData["dueDate"]

                    Log.d("AppNavigation", "Parsed QR data: title=$title, amount=$amount, dueDate=$dueDate")

                    // Navigate to add bill screen with extracted data
                    navController.navigate(
                        Screen.AddEditBill.createRoute(
                            title = title,
                            amount = amount,
                            dueDate = dueDate
                        )
                    )
                }
            )
        }

        composable(Screen.DocumentScan.route) {
            DocumentScanScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onScanComplete = { billData ->
                    Log.d("AppNavigation", "Received Document scan data: $billData")

                    // Extract data from the scan result
                    val title = billData["title"]
                    val amountStr = billData["amount"]
                    val amount = amountStr?.toDoubleOrNull()
                    val dueDate = billData["dueDate"]

                    Log.d("AppNavigation", "Parsed Document data: title=$title, amount=$amount, dueDate=$dueDate")

                    // Navigate to add bill screen with extracted data
                    navController.navigate(
                        Screen.AddEditBill.createRoute(
                            title = title,
                            amount = amount,
                            dueDate = dueDate
                        )
                    )
                }
            )
        }

        composable(Screen.GalleryScan.route) {
            GalleryScanScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onScanComplete = { billData ->
                    Log.d("AppNavigation", "Received Gallery scan data: $billData")

                    // Extract data from the scan result
                    val title = billData["title"]
                    val amountStr = billData["amount"]
                    val amount = amountStr?.toDoubleOrNull()
                    val dueDate = billData["dueDate"]

                    Log.d("AppNavigation", "Parsed Gallery data: title=$title, amount=$amount, dueDate=$dueDate")

                    // Navigate to add bill screen with extracted data
                    navController.navigate(
                        Screen.AddEditBill.createRoute(
                            title = title,
                            amount = amount,
                            dueDate = dueDate
                        )
                    )
                }
            )
        }

        composable(Screen.EmailSetup.route) {
            EmailSetupScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToEmailBills = { navController.navigate(Screen.EmailBills.route) },
                emailRepository = emailRepository
            )
        }

        composable(Screen.EmailBills.route) {
            EmailBillsScreen(
                onNavigateBack = { navController.popBackStack() },
                navController = navController,
                emailRepository = emailRepository
            )
        }



        composable(
            route = Screen.AddEditBill.route,
            arguments = listOf(
                navArgument("billId") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
                navArgument("title") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
                navArgument("amount") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
                navArgument("dueDate") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val billId = backStackEntry.arguments?.getString("billId")
            val title = backStackEntry.arguments?.getString("title")
            val amountStr = backStackEntry.arguments?.getString("amount")
            val amount = amountStr?.toDoubleOrNull()
            val dueDate = backStackEntry.arguments?.getString("dueDate")

            // Log the received data
            Log.d("AppNavigation", "Received data from navigation: billId=$billId, title=$title, amount=$amount, dueDate=$dueDate")

            AddEditBillScreen(
                billId = billId,
                initialTitle = title,
                initialAmount = amount,
                initialDueDate = dueDate,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onSaveComplete = {
                    // Check if we're in onboarding flow
                    val backStackEntry = navController.previousBackStackEntry
                    if (backStackEntry?.destination?.route == Screen.OnboardingFirstBill.route) {
                        // Complete onboarding and go to home
                        coroutineScope.launch {
                            onboardingManager.markOnboardingCompleted()
                        }
                        navController.navigate(Screen.Home.route) {
                            popUpTo(Screen.OnboardingWelcome.route) { inclusive = true }
                        }
                    } else {
                        navController.popBackStack()
                    }
                }
            )
        }

        composable(
            route = Screen.BillDetail.route,
            arguments = listOf(
                navArgument("billId") {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val billId = backStackEntry.arguments?.getString("billId") ?: ""
            BillDetailScreen(
                billId = billId,
                onNavigateBack = { navController.popBackStack() },
                onNavigateToEdit = { billId ->
                    navController.navigate(Screen.AddEditBill.createRoute(billId))
                }
            )
        }
    }
}
