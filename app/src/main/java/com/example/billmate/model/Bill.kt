package com.example.billmate.model

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import com.google.firebase.firestore.ServerTimestamp
import java.util.Date

/**
 * Represents a bill or obligation in the application.
 */
data class Bill(
    @DocumentId
    val id: String = "",

    // Basic bill information
    val title: String = "",
    val amount: Double = 0.0,
    val description: String = "",

    // Dates
    val dueDate: Date? = null,
    val paidDate: Date? = null,

    // Status - use field annotation to map to Firestore field
    @field:JvmField // This makes the field accessible to Java
    @PropertyName("isPaid") // This tells Firestore to use "isPaid" as the field name
    val isPaid: Boolean = false,

    // Categorization
    val category: BillCategory = BillCategory.PERSONAL,
    val tags: List<String> = emptyList(),

    // Image reference (if any)
    val imageUrl: String? = null,

    // Metadata
    val userId: String = "",
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
) {
    // Add a no-arg constructor for Firestore
    constructor() : this(id = "")

    /**
     * Returns a formatted string representation of the amount with the Euro symbol.
     */
    val formattedAmount: String
        get() = String.format("%.2f €", amount)

    override fun toString(): String {
        return "Bill(id=$id, title=$title, amount=$amount, isPaid=$isPaid, dueDate=$dueDate, paidDate=$paidDate)"
    }
}

/**
 * Enum representing the main categories for bills.
 */
enum class BillCategory {
    PERSONAL,
    BUSINESS
}
