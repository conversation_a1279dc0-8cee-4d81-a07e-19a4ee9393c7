package com.example.billmate.model

import java.util.Date

/**
 * Data class representing an email account.
 */
data class EmailAccount(
    val email: String,
    val displayName: String = "",
    val provider: EmailProvider = EmailProvider.GMAIL,
    val accessToken: String = "",
    val refreshToken: String = "",
    val isAuthorized: Boolean = false
)

/**
 * Enum representing email providers.
 */
enum class EmailProvider {
    GMAIL,
    OUTLOOK,
    YAHOO,
    OTHER
}

/**
 * Data class representing an email message.
 */
data class EmailMessage(
    val id: String,
    val from: String,
    val to: String,
    val subject: String,
    val body: String,
    val receivedDate: Date,
    val hasAttachments: Boolean = false,
    val attachments: List<EmailAttachment> = emptyList(),
    val isRead: Boolean = false,
    val labels: List<String> = emptyList()
)

/**
 * Data class representing an email attachment.
 */
data class EmailAttachment(
    val id: String,
    val name: String,
    val contentType: String,
    val size: Long,
    val data: ByteArray? = null
) {
    override fun equals(other: Any?): Bo<PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EmailAttachment

        if (id != other.id) return false
        if (name != other.name) return false
        if (contentType != other.contentType) return false
        if (size != other.size) return false
        if (data != null) {
            if (other.data == null) return false
            if (!data.contentEquals(other.data)) return false
        } else if (other.data != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + contentType.hashCode()
        result = 31 * result + size.hashCode()
        result = 31 * result + (data?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * Data class representing the result of bill extraction from an email.
 */
data class EmailBillExtraction(
    val emailId: String,
    val title: String?,
    val amount: Double?,
    val dueDate: Date?,
    val description: String?,
    val confidence: Float,
    val extractedFrom: String // "SUBJECT", "BODY", "ATTACHMENT"
)
