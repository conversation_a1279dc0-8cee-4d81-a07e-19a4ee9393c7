package com.example.billmate.service

import android.app.AlarmManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.billmate.MainActivity
import com.example.billmate.R
import com.example.billmate.model.Bill
import com.example.billmate.utils.PermissionManager
import com.example.billmate.utils.PreferencesManager
import com.example.billmate.utils.WorkManagerUtil
import java.util.Calendar
import java.util.Date
import java.util.concurrent.TimeUnit

/**
 * Service for handling notifications in the app.
 */
class NotificationService(private val context: Context) {

    companion object {
        private const val TAG = "NotificationService"

        // Notification channels
        const val CHANNEL_BILLS_DUE = "bills_due"
        const val CHANNEL_BILLS_UPCOMING = "bills_upcoming"
        const val CHANNEL_BILLS_OVERDUE = "bills_overdue"

        // Notification IDs
        private var notificationId = 1000

        // Shared preferences keys
        const val PREF_NOTIFICATIONS_ENABLED = "notifications_enabled"
        const val PREF_DUE_DATE_NOTIFICATIONS = "due_date_notifications"
        const val PREF_UPCOMING_NOTIFICATIONS = "upcoming_notifications"
        const val PREF_NOTIFICATION_DAYS_BEFORE = "notification_days_before"
        const val PREF_NOTIFICATION_HOUR = "notification_hour"
        const val PREF_NOTIFICATION_MINUTE = "notification_minute"
    }

    /**
     * Initialize notification channels.
     */
    fun createNotificationChannels() {
        // Only needed for Android 8.0 (API level 26) and higher
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Create the bills due channel
            val billsDueChannel = NotificationChannel(
                CHANNEL_BILLS_DUE,
                context.getString(R.string.notification_channel_bills_due),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = context.getString(R.string.notification_channel_bills_due_description)
            }

            // Create the upcoming bills channel
            val upcomingBillsChannel = NotificationChannel(
                CHANNEL_BILLS_UPCOMING,
                context.getString(R.string.notification_channel_bills_upcoming),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = context.getString(R.string.notification_channel_bills_upcoming_description)
            }

            // Create the overdue bills channel
            val overdueBillsChannel = NotificationChannel(
                CHANNEL_BILLS_OVERDUE,
                context.getString(R.string.notification_channel_bills_overdue),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = context.getString(R.string.notification_channel_bills_overdue_description)
            }

            // Register the channels with the system
            notificationManager.createNotificationChannel(billsDueChannel)
            notificationManager.createNotificationChannel(upcomingBillsChannel)
            notificationManager.createNotificationChannel(overdueBillsChannel)

            Log.d(TAG, "Notification channels created")
        }
    }

    /**
     * Send a notification for a bill that is due today.
     */
    fun sendBillDueNotification(bill: Bill) {
        if (!areNotificationsEnabled() || !areDueDateNotificationsEnabled()) {
            Log.d(TAG, "Due date notifications are disabled")
            return
        }

        // Check if we have notification permission
        if (!PermissionManager.hasNotificationPermission(context)) {
            Log.d(TAG, "Notification permission not granted")
            return
        }

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("billId", bill.id)
            putExtra("screen", "billDetail")
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_BILLS_DUE)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(context.getString(R.string.notification_bill_due_title))
            .setContentText(context.getString(R.string.notification_bill_due_text, bill.title, bill.formattedAmount))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)

        with(NotificationManagerCompat.from(context)) {
            try {
                notify(getNextNotificationId(), builder.build())
                Log.d(TAG, "Bill due notification sent for bill: ${bill.id}")
            } catch (e: SecurityException) {
                Log.e(TAG, "Failed to send notification: ${e.message}")
            }
        }
    }

    /**
     * Send a notification for an upcoming bill.
     */
    fun sendUpcomingBillNotification(bill: Bill, daysUntilDue: Int) {
        if (!areNotificationsEnabled() || !areUpcomingNotificationsEnabled()) {
            Log.d(TAG, "Upcoming notifications are disabled")
            return
        }

        // Check if we have notification permission
        if (!PermissionManager.hasNotificationPermission(context)) {
            Log.d(TAG, "Notification permission not granted")
            return
        }

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("billId", bill.id)
            putExtra("screen", "billDetail")
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_BILLS_UPCOMING)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(context.getString(R.string.notification_bill_upcoming_title))
            .setContentText(context.getString(
                R.string.notification_bill_upcoming_text,
                bill.title,
                bill.formattedAmount,
                daysUntilDue
            ))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)

        with(NotificationManagerCompat.from(context)) {
            try {
                notify(getNextNotificationId(), builder.build())
                Log.d(TAG, "Upcoming bill notification sent for bill: ${bill.id}")
            } catch (e: SecurityException) {
                Log.e(TAG, "Failed to send notification: ${e.message}")
            }
        }
    }

    /**
     * Send a notification for an overdue bill.
     */
    fun sendOverdueBillNotification(bill: Bill, daysOverdue: Int) {
        if (!areNotificationsEnabled()) {
            Log.d(TAG, "Notifications are disabled")
            return
        }

        // Check if we have notification permission
        if (!PermissionManager.hasNotificationPermission(context)) {
            Log.d(TAG, "Notification permission not granted")
            return
        }

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("billId", bill.id)
            putExtra("screen", "billDetail")
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_BILLS_OVERDUE)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(context.getString(R.string.notification_bill_overdue_title))
            .setContentText(context.getString(
                R.string.notification_bill_overdue_text,
                bill.title,
                bill.formattedAmount,
                daysOverdue
            ))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)

        with(NotificationManagerCompat.from(context)) {
            try {
                notify(getNextNotificationId(), builder.build())
                Log.d(TAG, "Overdue bill notification sent for bill: ${bill.id}")
            } catch (e: SecurityException) {
                Log.e(TAG, "Failed to send notification: ${e.message}")
            }
        }
    }

    /**
     * Check if a bill should trigger a notification based on its due date.
     */
    fun shouldNotifyForBill(bill: Bill): Boolean {
        if (!areNotificationsEnabled()) {
            return false
        }

        if (bill.isPaid) {
            return false
        }

        val today = Date()
        val dueDate = bill.dueDate

        if (dueDate == null) {
            return false
        }

        // Calculate days until due
        val diffInMillis = dueDate.time - today.time
        val daysUntilDue = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS).toInt()

        // Check if due today
        if (daysUntilDue == 0 && areDueDateNotificationsEnabled()) {
            sendBillDueNotification(bill)
            return true
        }

        // Check if upcoming notification should be sent
        val daysBefore = getNotificationDaysBefore()
        if (daysUntilDue > 0 && daysUntilDue <= daysBefore && areUpcomingNotificationsEnabled()) {
            sendUpcomingBillNotification(bill, daysUntilDue)
            return true
        }

        return false
    }

    /**
     * Get a unique notification ID.
     */
    private fun getNextNotificationId(): Int {
        return notificationId++
    }

    /**
     * Check if notifications are enabled.
     */
    fun areNotificationsEnabled(): Boolean {
        return PreferencesManager.getBoolean(context, PREF_NOTIFICATIONS_ENABLED, true)
    }

    /**
     * Set whether notifications are enabled.
     */
    fun setNotificationsEnabled(enabled: Boolean) {
        PreferencesManager.setBoolean(context, PREF_NOTIFICATIONS_ENABLED, enabled)

        // Schedule or cancel the bill check worker based on the new setting
        if (enabled) {
            WorkManagerUtil.scheduleBillCheckWorker(context)
        } else {
            // Cancel the worker if notifications are disabled
            androidx.work.WorkManager.getInstance(context)
                .cancelUniqueWork(BillCheckWorker.WORK_NAME)
        }
    }

    /**
     * Check if due date notifications are enabled.
     */
    fun areDueDateNotificationsEnabled(): Boolean {
        return PreferencesManager.getBoolean(context, PREF_DUE_DATE_NOTIFICATIONS, true)
    }

    /**
     * Set whether due date notifications are enabled.
     */
    fun setDueDateNotificationsEnabled(enabled: Boolean) {
        PreferencesManager.setBoolean(context, PREF_DUE_DATE_NOTIFICATIONS, enabled)
    }

    /**
     * Check if upcoming notifications are enabled.
     */
    fun areUpcomingNotificationsEnabled(): Boolean {
        return PreferencesManager.getBoolean(context, PREF_UPCOMING_NOTIFICATIONS, true)
    }

    /**
     * Set whether upcoming notifications are enabled.
     */
    fun setUpcomingNotificationsEnabled(enabled: Boolean) {
        PreferencesManager.setBoolean(context, PREF_UPCOMING_NOTIFICATIONS, enabled)
    }

    /**
     * Get the number of days before a bill is due to send a notification.
     */
    fun getNotificationDaysBefore(): Int {
        return PreferencesManager.getInt(context, PREF_NOTIFICATION_DAYS_BEFORE, 3)
    }

    /**
     * Set the number of days before a bill is due to send a notification.
     */
    fun setNotificationDaysBefore(days: Int) {
        PreferencesManager.setInt(context, PREF_NOTIFICATION_DAYS_BEFORE, days)
    }

    /**
     * Get the hour of the day when notifications should be sent (24-hour format).
     */
    fun getNotificationHour(): Int {
        return PreferencesManager.getInt(context, PREF_NOTIFICATION_HOUR, 9) // Default to 9 AM
    }

    /**
     * Set the hour of the day when notifications should be sent (24-hour format).
     */
    fun setNotificationHour(hour: Int) {
        PreferencesManager.setInt(context, PREF_NOTIFICATION_HOUR, hour)
        Log.d(TAG, "Notification hour set to: $hour")

        // Reschedule the worker with the new time
        if (areNotificationsEnabled()) {
            WorkManagerUtil.scheduleBillCheckWorker(context)
        }
    }

    /**
     * Get the minute of the hour when notifications should be sent.
     */
    fun getNotificationMinute(): Int {
        return PreferencesManager.getInt(context, PREF_NOTIFICATION_MINUTE, 0) // Default to on the hour
    }

    /**
     * Set the minute of the hour when notifications should be sent.
     */
    fun setNotificationMinute(minute: Int) {
        PreferencesManager.setInt(context, PREF_NOTIFICATION_MINUTE, minute)
        Log.d(TAG, "Notification minute set to: $minute")

        // Reschedule the worker with the new time
        if (areNotificationsEnabled()) {
            WorkManagerUtil.scheduleBillCheckWorker(context)
        }
    }

    /**
     * Check if the current time is within the notification window.
     * This can be used to determine if notifications should be sent now.
     *
     * For testing purposes, we're using a wider window (15 minutes before and after)
     * to make it easier to test notifications.
     */
    fun isWithinNotificationWindow(): Boolean {
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        val currentMinute = Calendar.getInstance().get(Calendar.MINUTE)
        val preferredHour = getNotificationHour()
        val preferredMinute = getNotificationMinute()

        // Convert both times to minutes since midnight for easier comparison
        val currentTimeInMinutes = currentHour * 60 + currentMinute
        val preferredTimeInMinutes = preferredHour * 60 + preferredMinute

        // Allow a 30-minute window for notifications (15 minutes before and after)
        val timeDifference = Math.abs(currentTimeInMinutes - preferredTimeInMinutes)

        // Check if we're within 15 minutes of the preferred time
        // or if we're within 15 minutes of the preferred time + 24 hours
        // (to handle cases where the current time is just before midnight and the preferred time is just after)
        return timeDifference <= 15 || timeDifference >= (24 * 60 - 15)
    }

    /**
     * Send a test notification to verify that notifications are working.
     * This ignores the notification time window.
     */
    fun sendTestNotification() {
        // Check if we have notification permission
        if (!PermissionManager.hasNotificationPermission(context)) {
            Log.d(TAG, "Notification permission not granted")
            return
        }

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_BILLS_DUE)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(context.getString(R.string.test_notification))
            .setContentText(context.getString(R.string.test_notification_sent))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)

        with(NotificationManagerCompat.from(context)) {
            try {
                notify(getNextNotificationId(), builder.build())
                Log.d(TAG, "Test notification sent")
            } catch (e: SecurityException) {
                Log.e(TAG, "Failed to send test notification: ${e.message}")
            }
        }
    }
}
