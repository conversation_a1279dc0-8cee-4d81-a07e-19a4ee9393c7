package com.example.billmate.service

import android.content.Context
import com.example.billmate.model.EmailAccount
import com.example.billmate.model.EmailMessage
import kotlinx.coroutines.flow.Flow

/**
 * Interface for email service operations.
 */
interface EmailService {
    /**
     * Connect to the email service with the given account.
     * @param account The email account to connect with
     * @return True if connection was successful, false otherwise
     */
    suspend fun connect(account: EmailAccount): Result<Boolean>
    
    /**
     * Disconnect from the email service.
     */
    suspend fun disconnect()
    
    /**
     * Check if the service is connected.
     * @return True if connected, false otherwise
     */
    fun isConnected(): Boolean
    
    /**
     * Get messages from the inbox that might contain bills.
     * @param maxResults Maximum number of messages to retrieve
     * @return List of email messages
     */
    suspend fun getBillEmails(maxResults: Int = 10): Result<List<EmailMessage>>
    
    /**
     * Get a specific email message by ID.
     * @param messageId The ID of the message to retrieve
     * @return The email message
     */
    suspend fun getEmailById(messageId: String): Result<EmailMessage>
    
    /**
     * Authorize the app to access the user's email.
     * @param context Android context
     * @return Flow of authorization state
     */
    fun authorize(context: Context): Flow<AuthState>
    
    /**
     * Check if the app is authorized to access the user's email.
     * @return True if authorized, false otherwise
     */
    fun isAuthorized(): Boolean
    
    /**
     * Revoke authorization to access the user's email.
     */
    suspend fun revokeAuthorization()
    
    /**
     * Enum representing the state of authorization.
     */
    enum class AuthState {
        UNAUTHORIZED,
        AUTHORIZING,
        AUTHORIZED,
        FAILED
    }
}
