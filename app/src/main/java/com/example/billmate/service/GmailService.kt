package com.example.billmate.service

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import com.example.billmate.MainActivity
import com.example.billmate.model.EmailAccount
import com.example.billmate.model.EmailAttachment
import com.example.billmate.model.EmailMessage
import com.example.billmate.model.EmailProvider
import com.example.billmate.utils.EnvConfig
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.auth.api.signin.GoogleSignInStatusCodes
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.Scope
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport
import com.google.api.client.http.GenericUrl
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.client.util.Base64
import com.google.api.client.auth.oauth2.TokenRequest
import com.google.api.client.auth.oauth2.ClientParametersAuthentication
import com.google.api.services.gmail.Gmail
import com.google.api.services.gmail.GmailScopes
import com.google.api.services.gmail.model.ListMessagesResponse
import com.google.api.services.gmail.model.Message
import com.google.api.services.gmail.model.MessagePart
import com.google.api.services.gmail.model.MessagePartBody
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import java.io.ByteArrayInputStream
import java.io.InputStreamReader
import java.util.Date
import java.util.Properties
import javax.mail.Session
import javax.mail.internet.MimeMessage

/**
 * Implementation of EmailService for Gmail.
 */
class GmailService : EmailService {
    private val TAG = "GmailService"

    private val JSON_FACTORY = GsonFactory.getDefaultInstance()
    private val SCOPES = listOf(GmailScopes.GMAIL_READONLY)

    private var gmailService: Gmail? = null
    private var credential: GoogleAccountCredential? = null
    private var currentAccount: EmailAccount? = null
    private var account: GoogleSignInAccount? = null

    private val _authState = MutableStateFlow(EmailService.AuthState.UNAUTHORIZED)



    override suspend fun connect(account: EmailAccount): Result<Boolean> = withContext(Dispatchers.IO) {
        Log.d(TAG, "Connecting to Gmail with account: ${account.email}")
        try {
            if (account.provider != EmailProvider.GMAIL) {
                Log.e(TAG, "Provider is not Gmail: ${account.provider}")
                return@withContext Result.failure(IllegalArgumentException("Only Gmail is supported"))
            }

            if (!account.isAuthorized) {
                Log.e(TAG, "Account is not authorized")
                return@withContext Result.failure(IllegalStateException("Account is not authorized"))
            }

            Log.d(TAG, "Account is authorized, initializing transport")

            // Initialize the transport
            val httpTransport = GoogleNetHttpTransport.newTrustedTransport()

            // Check if credential is initialized
            if (credential == null) {
                Log.e(TAG, "Credential is null")
                return@withContext Result.failure(IllegalStateException("Credential is null"))
            }

            Log.d(TAG, "Building Gmail service")

            // Build Gmail service
            gmailService = Gmail.Builder(httpTransport, JSON_FACTORY, credential)
                .setApplicationName(APPLICATION_NAME)
                .build()

            currentAccount = account

            Log.d(TAG, "Successfully connected to Gmail")

            Result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error connecting to Gmail", e)
            Result.failure(e)
        }
    }

    override suspend fun disconnect() = withContext(Dispatchers.IO) {
        gmailService = null
        currentAccount = null
    }

    override fun isConnected(): Boolean {
        return gmailService != null && currentAccount != null
    }

    override suspend fun getBillEmails(maxResults: Int): Result<List<EmailMessage>> = withContext(Dispatchers.IO) {
        try {
            if (!isConnected()) {
                return@withContext Result.failure(IllegalStateException("Not connected to Gmail"))
            }

            val gmail = gmailService ?: return@withContext Result.failure(IllegalStateException("Gmail service is null"))

            // Query for emails that might contain bills
            // Look for common bill-related terms in the subject or body
            val query = "subject:(invoice OR bill OR payment OR receipt OR statement OR due) OR (invoice OR bill OR payment OR receipt OR statement OR due)"

            val listResponse: ListMessagesResponse = gmail.users().messages()
                .list("me")
                .setMaxResults(maxResults.toLong())
                .setQ(query)
                .execute()

            val messageIds = listResponse.messages ?: emptyList()

            // Fetch full message details for each message ID
            val emails = messageIds.mapNotNull { messageRef ->
                val message = gmail.users().messages().get("me", messageRef.id).execute()
                parseMessage(message)
            }

            Result.success(emails)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting bill emails", e)
            Result.failure(e)
        }
    }

    override suspend fun getEmailById(messageId: String): Result<EmailMessage> = withContext(Dispatchers.IO) {
        try {
            if (!isConnected()) {
                return@withContext Result.failure(IllegalStateException("Not connected to Gmail"))
            }

            val gmail = gmailService ?: return@withContext Result.failure(IllegalStateException("Gmail service is null"))

            val message = gmail.users().messages().get("me", messageId).execute()
            val email = parseMessage(message) ?: return@withContext Result.failure(IllegalStateException("Failed to parse message"))

            Result.success(email)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting email by ID", e)
            Result.failure(e)
        }
    }

    override fun authorize(context: Context): Flow<EmailService.AuthState> {
        // Store the context for later use
        this.appContext = context.applicationContext

        // Initialize environment config
        EnvConfig.init(context)

        // Log the package name
        Log.d(TAG, "Package name: ${appContext.packageName}")

        // Update auth state to authorizing
        _authState.value = EmailService.AuthState.AUTHORIZING

        // Launch the Google Sign-In intent
        try {
            // Get the client ID from environment config
            val clientId = EnvConfig.getGmailClientId()
            Log.d(TAG, "Retrieved client ID: ${clientId.take(15)}...")

            if (clientId.isNullOrEmpty()) {
                Log.e(TAG, "Client ID not found in environment config")
                _authState.value = EmailService.AuthState.FAILED
                return _authState.asStateFlow()
            }

            // Configure Google Sign-In with explicit Gmail scope
            val gmailScope = Scope("https://www.googleapis.com/auth/gmail.readonly")
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestEmail()
                .requestProfile()
                .requestScopes(gmailScope)
                .build()

            Log.d(TAG, "Built GoogleSignInOptions with explicit Gmail scope")
            Log.d(TAG, "Using package name: ${appContext.packageName}")
            Log.d(TAG, "Requesting scope: https://www.googleapis.com/auth/gmail.readonly")

            // Build a GoogleSignInClient with the options
            val googleSignInClient = GoogleSignIn.getClient(context, gso)

            // Get the sign-in intent
            val signInIntent = googleSignInClient.signInIntent

            // Start the sign-in activity
            if (context is MainActivity) {
                // Use the modern ActivityResultLauncher in MainActivity
                context.launchSignIn(signInIntent)
                Log.d(TAG, "Launched sign-in intent using MainActivity's launcher")
            } else if (context is Activity) {
                // Fallback to deprecated method for other activities
                context.startActivityForResult(signInIntent, REQUEST_CODE_SIGN_IN)
                Log.d(TAG, "Launched sign-in intent using deprecated startActivityForResult")
            } else {
                Log.e(TAG, "Context is not an Activity, cannot start sign-in flow")
                _authState.value = EmailService.AuthState.FAILED
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting OAuth flow", e)
            _authState.value = EmailService.AuthState.FAILED
        }

        return _authState.asStateFlow()
    }

    /**
     * Handle the result of the sign-in activity.
     */
    fun handleSignInResult(data: Intent?) {
        Log.d(TAG, "Handling sign-in result, data is ${if (data == null) "null" else "not null"}")

        try {
            if (data == null) {
                Log.e(TAG, "Sign-in data is null")
                _authState.value = EmailService.AuthState.FAILED
                return
            }

            // Get the sign-in result
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            Log.d(TAG, "Created sign-in task from intent")

            try {
                val account = task.getResult(ApiException::class.java)
                Log.d(TAG, "Got account from sign-in task: ${account?.email}")

                // Instead of using server auth code, we'll use the account directly
                if (account != null) {
                    // Store the account information
                    val email = account.email ?: ""
                    val displayName = account.displayName ?: ""

                    Log.d(TAG, "Successfully signed in as $displayName ($email)")

                    // Update the auth state
                    _authState.value = EmailService.AuthState.AUTHORIZED

                    // Store the account for later use
                    this.account = account

                    // Try to get messages
                    fetchRecentEmails()
                } else {
                    Log.e(TAG, "Account is null")
                    _authState.value = EmailService.AuthState.FAILED
                }
            } catch (e: ApiException) {
                // Handle specific API exceptions with more detailed logging
                when (e.statusCode) {
                    GoogleSignInStatusCodes.SIGN_IN_CANCELLED ->
                        Log.e(TAG, "Sign-in was cancelled by the user", e)
                    GoogleSignInStatusCodes.SIGN_IN_CURRENTLY_IN_PROGRESS ->
                        Log.e(TAG, "Sign-in is already in progress", e)
                    GoogleSignInStatusCodes.SIGN_IN_FAILED ->
                        Log.e(TAG, "Sign-in failed - general error", e)
                    GoogleSignInStatusCodes.NETWORK_ERROR ->
                        Log.e(TAG, "Sign-in failed due to network error", e)
                    GoogleSignInStatusCodes.INVALID_ACCOUNT ->
                        Log.e(TAG, "Sign-in failed - invalid account", e)
                    GoogleSignInStatusCodes.SIGN_IN_REQUIRED ->
                        Log.e(TAG, "Sign-in required but not signed in", e)
                    GoogleSignInStatusCodes.DEVELOPER_ERROR -> {
                        Log.e(TAG, "Sign-in failed - DEVELOPER_ERROR (10). This usually means:", e)
                        Log.e(TAG, "1. The SHA-1 fingerprint in Google Cloud Console doesn't match your app's fingerprint")
                        Log.e(TAG, "2. The package name in Google Cloud Console doesn't match your app's package name")
                        Log.e(TAG, "3. The OAuth client ID is incorrect or malformed")
                        Log.e(TAG, "4. The app wasn't properly set up in the Google Cloud Console")
                        Log.e(TAG, "Current package name: ${appContext.packageName}")

                        // Try a different approach - check if we already have a signed-in account
                        val lastAccount = GoogleSignIn.getLastSignedInAccount(appContext)
                        if (lastAccount != null) {
                            Log.d(TAG, "Found previously signed-in account: ${lastAccount.email}")
                            this.account = lastAccount
                            _authState.value = EmailService.AuthState.AUTHORIZED
                            fetchRecentEmails()
                        }
                    }
                    12501 -> {
                        Log.e(TAG, "Sign-in was cancelled by the user", e)
                        _authState.value = EmailService.AuthState.UNAUTHORIZED
                    }
                    16 -> {
                        Log.e(TAG, "Sign-in failed - API_UNAVAILABLE. This usually means the API is disabled or not configured properly.", e)
                        _authState.value = EmailService.AuthState.FAILED
                    }
                    8 -> {
                        Log.e(TAG, "Sign-in failed - INTERNAL_ERROR. This could be due to Google's verification process not being completed.", e)
                        _authState.value = EmailService.AuthState.FAILED
                        // Show a more user-friendly message
                        GlobalScope.launch(Dispatchers.Main) {
                            Toast.makeText(
                                appContext,
                                "Google hasn't finished reviewing this app yet. Only test users can access it at this time.",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
                    else ->
                        Log.e(TAG, "Sign-in failed with status code: ${e.statusCode}", e)
                }

                if (_authState.value != EmailService.AuthState.AUTHORIZED) {
                    _authState.value = EmailService.AuthState.FAILED
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling sign-in result", e)
            _authState.value = EmailService.AuthState.FAILED
        }
    }

    /**
     * Exchange the auth code for access and refresh tokens.
     */
    private lateinit var appContext: Context

    /**
     * Fetch recent emails using the Google Sign-In account.
     * This is a simplified approach that doesn't require server auth code.
     */
    private fun fetchRecentEmails() {
        Log.d(TAG, "Fetching recent emails")

        val signedInAccount = account ?: GoogleSignIn.getLastSignedInAccount(appContext)

        if (signedInAccount == null) {
            Log.e(TAG, "No signed-in account found")
            _authState.value = EmailService.AuthState.FAILED
            return
        }

        // Create the credential
        credential = GoogleAccountCredential.usingOAuth2(
            appContext,
            listOf(GmailScopes.GMAIL_READONLY)
        )
        credential?.selectedAccount = signedInAccount.account

        // Create the email account
        val emailAccount = EmailAccount(
            email = signedInAccount.email ?: "",
            displayName = signedInAccount.displayName ?: "",
            provider = EmailProvider.GMAIL,
            accessToken = "", // Empty string instead of null
            refreshToken = "",
            isAuthorized = true
        )

        // Connect to Gmail
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val result = connect(emailAccount)

                if (result.isSuccess) {
                    _authState.value = EmailService.AuthState.AUTHORIZED
                    Log.d(TAG, "Successfully connected to Gmail")
                } else {
                    Log.e(TAG, "Failed to connect to Gmail")
                    _authState.value = EmailService.AuthState.FAILED
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error connecting to Gmail", e)
                _authState.value = EmailService.AuthState.FAILED
            }
        }
    }

    private fun exchangeAuthCodeForTokens(authCode: String, account: GoogleSignInAccount) {
        Log.d(TAG, "Exchanging auth code for tokens")
        Log.d(TAG, "Account email: ${account.email}")
        Log.d(TAG, "Auth code length: ${authCode.length}")

        // This would typically be done on a background thread
        GlobalScope.launch(Dispatchers.IO) {
            try {
                // Get the client ID from environment config
                val clientId = EnvConfig.getGmailClientId()

                Log.d(TAG, "Client ID: ${clientId.take(15)}...")

                if (clientId.isNullOrEmpty()) {
                    Log.e(TAG, "Client ID not found in environment config")
                    _authState.value = EmailService.AuthState.FAILED
                    return@launch
                }

                // Create the token request
                val tokenRequest = com.google.api.client.auth.oauth2.TokenRequest(
                    NetHttpTransport(),
                    JSON_FACTORY,
                    GenericUrl("https://oauth2.googleapis.com/token"),
                    "authorization_code"
                )

                // Set parameters
                tokenRequest.put("client_id", clientId)
                tokenRequest.put("code", authCode)
                // Use the correct redirect URI format that matches the manifest
                val redirectUri = if (appContext.packageName.endsWith(".debug")) {
                    "com.example.billmate.debug://oauth2callback"
                } else {
                    "com.example.billmate://oauth2callback"
                }
                tokenRequest.put("redirect_uri", redirectUri)
                // For Android, we use the package name and SHA-1 fingerprint instead of client secret
                tokenRequest.put("grant_type", "authorization_code")
                Log.d(TAG, "Using redirect URI: $redirectUri")

                // Execute the token request
                Log.d(TAG, "Executing token request...")
                var accessToken: String? = null
                var refreshToken: String? = null

                try {
                    val tokenResponse = tokenRequest.execute()
                    Log.d(TAG, "Token request executed successfully")

                    // Get the access and refresh tokens
                    accessToken = tokenResponse.accessToken
                    refreshToken = tokenResponse.refreshToken

                    Log.d(TAG, "Access token received: ${accessToken?.take(10) ?: "null"}...")
                    Log.d(TAG, "Refresh token received: ${refreshToken?.take(10) ?: "null"}...")

                    if (accessToken == null) {
                        Log.e(TAG, "Access token is null")
                        _authState.value = EmailService.AuthState.FAILED
                        return@launch
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error executing token request", e)
                    _authState.value = EmailService.AuthState.FAILED
                    return@launch
                }

                // Create the credential
                credential = GoogleAccountCredential.usingOAuth2(
                    appContext,
                    listOf(GmailScopes.GMAIL_READONLY)
                )
                credential?.selectedAccount = account.account

                // Create the email account
                val emailAccount = EmailAccount(
                    email = account.email ?: "",
                    displayName = account.displayName ?: "",
                    provider = EmailProvider.GMAIL,
                    accessToken = accessToken,
                    refreshToken = refreshToken ?: "",
                    isAuthorized = true
                )

                // Connect to Gmail
                val result = connect(emailAccount)

                if (result.isSuccess) {
                    _authState.value = EmailService.AuthState.AUTHORIZED
                } else {
                    Log.e(TAG, "Failed to connect to Gmail")
                    _authState.value = EmailService.AuthState.FAILED
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error exchanging auth code for tokens", e)
                _authState.value = EmailService.AuthState.FAILED
            }
        }
    }

    companion object {
        private const val APPLICATION_NAME = "BillMate"
        const val REQUEST_CODE_SIGN_IN = 9001
    }

    override fun isAuthorized(): Boolean {
        return credential != null && credential!!.selectedAccountName != null
    }

    override suspend fun revokeAuthorization() = withContext(Dispatchers.IO) {
        // TODO: Implement revocation of authorization
        credential = null
        _authState.value = EmailService.AuthState.UNAUTHORIZED
    }

    /**
     * Parse a Gmail message into an EmailMessage.
     */
    private fun parseMessage(message: Message): EmailMessage? {
        try {
            // Extract headers
            val headers = message.payload.headers

            // Get subject
            val subject = headers.find { it.name.equals("Subject", ignoreCase = true) }?.value ?: ""

            // Get from
            val from = headers.find { it.name.equals("From", ignoreCase = true) }?.value ?: ""

            // Get to
            val to = headers.find { it.name.equals("To", ignoreCase = true) }?.value ?: ""

            // Get date
            val dateHeader = headers.find { it.name.equals("Date", ignoreCase = true) }?.value
            val receivedDate = if (dateHeader != null) {
                try {
                    // Parse RFC 2822 date format
                    javax.mail.internet.MailDateFormat().parse(dateHeader)
                } catch (e: Exception) {
                    Date(message.internalDate)
                }
            } else {
                Date(message.internalDate)
            }

            // Extract body and attachments
            val bodyAndAttachments = extractBodyAndAttachments(message.payload)
            val body = bodyAndAttachments.first
            val attachments = bodyAndAttachments.second

            return EmailMessage(
                id = message.id,
                from = from,
                to = to,
                subject = subject,
                body = body,
                receivedDate = receivedDate,
                hasAttachments = attachments.isNotEmpty(),
                attachments = attachments,
                isRead = !message.labelIds.contains("UNREAD"),
                labels = message.labelIds
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message", e)
            return null
        }
    }

    /**
     * Extract body and attachments from a message part.
     */
    private fun extractBodyAndAttachments(messagePart: MessagePart): Pair<String, List<EmailAttachment>> {
        val body = StringBuilder()
        val attachments = mutableListOf<EmailAttachment>()

        // Process this part
        if (messagePart.mimeType == "text/plain" || messagePart.mimeType == "text/html") {
            // This is a text part
            val data = messagePart.body.data
            if (data != null) {
                val decodedBytes = Base64.decodeBase64(data)
                val text = String(decodedBytes)
                body.append(text)
            }
        } else if (messagePart.filename != null && messagePart.filename.isNotEmpty()) {
            // This is an attachment
            val attachment = createAttachment(messagePart)
            if (attachment != null) {
                attachments.add(attachment)
            }
        }

        // Process child parts recursively
        messagePart.parts?.forEach { part ->
            val (partBody, partAttachments) = extractBodyAndAttachments(part)
            body.append(partBody)
            attachments.addAll(partAttachments)
        }

        return Pair(body.toString(), attachments)
    }

    /**
     * Create an EmailAttachment from a message part.
     */
    private fun createAttachment(part: MessagePart): EmailAttachment? {
        try {
            return EmailAttachment(
                id = part.body.attachmentId ?: "",
                name = part.filename,
                contentType = part.mimeType,
                size = part.body.size?.toLong() ?: 0L,
                data = null // We don't load the data here to save memory
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error creating attachment", e)
            return null
        }
    }

    /**
     * Get attachment data.
     */
    private suspend fun getAttachmentData(messageId: String, attachmentId: String): ByteArray? = withContext(Dispatchers.IO) {
        try {
            if (!isConnected()) {
                return@withContext null
            }

            val gmail = gmailService ?: return@withContext null

            val attachmentResponse: MessagePartBody = gmail.users().messages().attachments()
                .get("me", messageId, attachmentId)
                .execute()

            Base64.decodeBase64(attachmentResponse.data)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting attachment data", e)
            null
        }
    }
}
