package com.example.billmate.service

import android.content.Context
import android.util.Log
import com.example.billmate.utils.EnvConfig
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.GenerateContentResponse
import com.google.ai.client.generativeai.type.content
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern

/**
 * Service to interact with the Gemini API
 */
class GeminiService(val context: Context) {
    private val apiKey: String
    private val model: GenerativeModel

    init {
        // Initialize environment config
        EnvConfig.init(context)

        // Get API key
        apiKey = EnvConfig.getGeminiApiKey()

        Log.d("GeminiService", "Got API key from EnvConfig: ${apiKey.take(5)}...")

        // Initialize Gemini model
        try {
            model = GenerativeModel(
                modelName = "gemini-2.0-flash",
                apiKey = apiKey
            )
            Log.d("GeminiService", "Successfully initialized Gemini model")
        } catch (e: Exception) {
            Log.e("GeminiService", "Error initializing Gemini model", e)
            throw e
        }
    }

    /**
     * Send a message to the Gemini API
     */
    suspend fun sendMessage(message: String, locale: Locale = Locale.getDefault()): String {
        return try {
            withContext(Dispatchers.IO) {
                Log.d("GeminiService", "Preparing to send message to Gemini API")

                // Determine if we should use Slovenian instructions
                val isSlovenian = locale.language == "sl"

                val prompt = if (isSlovenian) {
                    """
                    Ti si pomočnik za aplikacijo za upravljanje računov, imenovano BillMate.
                    Iz uporabnikovega sporočila izvleci podatke o računu.

                    Uporabnikovo sporočilo: $message

                    Izvleci naslednje podatke, če so prisotni:
                    - Ime/naslov računa: Če ni navedeno specifično ime, uporabi vrsto računa (npr. "Račun za elektriko", "Telefonski račun")
                    - Znesek: Izvleci katerokoli denarno vrednost, ignoriraj simbole valut. Pretvori v decimalno število.
                    - Rok plačila: Izvleci omenjeni datum in ga pretvori v format YYYY-MM-DD

                    Bodi prilagodljiv pri formatih:
                    - Za zneske kot so "90€", "€90", "90", "90 evrov", izvleci samo število 90
                    - Za datume kot so "7. maj", "7. maja", "7.5.", pretvori v format YYYY-MM-DD (predpostavi tekoče leto)

                    Odgovori v slovenskem jeziku in v JSON formatu, kot je ta:
                    {
                      "title": "izvlečen naslov ali null, če ni najden",
                      "amount": "izvlečen znesek kot število ali null, če ni najden",
                      "dueDate": "izvlečen datum v formatu YYYY-MM-DD ali null, če ni najden"
                    }

                    Odgovori samo z JSON, nič drugega.
                    """.trimIndent()
                } else {
                    """
                    You are a helpful assistant for a bill management app called BillMate.
                    Extract bill information from the user's message.

                    User message: $message

                    Extract the following information if present:
                    - Bill name/title: If no specific name is given, use the type of bill (e.g., "Electric Bill", "Phone Bill")
                    - Amount: Extract any monetary value, ignoring currency symbols. Convert to decimal number.
                    - Due date: Extract any date mentioned and convert to YYYY-MM-DD format

                    Be flexible with formats:
                    - For amounts like "90€", "€90", "$90", "90 dollars", extract just the number 90
                    - For dates like "May 7th", "7th of May", "05/07", convert to YYYY-MM-DD format (assuming current year)

                    Respond in JSON format like this:
                    {
                      "title": "extracted title or null if not found",
                      "amount": "extracted amount as number or null if not found",
                      "dueDate": "extracted date in YYYY-MM-DD format or null if not found"
                    }

                    Only respond with the JSON, nothing else.
                    """.trimIndent()
                }

                Log.d("GeminiService", "Sending prompt to Gemini API")

                try {
                    val response = model.generateContent(
                        content {
                            text(prompt)
                        }
                    )

                    Log.d("GeminiService", "Received response from Gemini API")
                    processResponse(response)
                } catch (e: Exception) {
                    Log.e("GeminiService", "Error in API call to Gemini", e)
                    e.printStackTrace()

                    // Fallback to direct extraction
                    Log.d("GeminiService", "Falling back to direct extraction")
                    val title = extractTitleFromMessage(message)
                    val amount = extractAmountFromMessage(message)
                    val dueDate = extractDateFromMessage(message)

                    // Create a JSON response
                    """
                    {
                      "title": "${title ?: "null"}",
                      "amount": "${amount ?: "null"}",
                      "dueDate": "${dueDate ?: "null"}"
                    }
                    """.trimIndent()
                }
            }
        } catch (e: Exception) {
            Log.e("GeminiService", "Error in sendMessage", e)
            e.printStackTrace()
            "Error: ${e.message}"
        }
    }

    /**
     * Process the response from Gemini
     */
    private fun processResponse(response: GenerateContentResponse): String {
        val text = response.text ?: "No response from Gemini"
        Log.d("GeminiService", "Raw response: $text")
        return text
    }

    /**
     * Extract bill information from a user message
     */
    suspend fun extractBillInfo(message: String, locale: Locale = Locale.getDefault()): BillInfo {
        val response = sendMessage(message, locale)

        // If the response is an error message, return empty bill info
        if (response.startsWith("Error:")) {
            Log.e("GeminiService", "Error in response: $response")
            return BillInfo(null, null, null)
        }

        // Try to parse the JSON response
        return try {
            // Extract JSON from the response (in case there's any extra text)
            val jsonPattern = Pattern.compile("\\{[^}]*\\}")
            val jsonMatcher = jsonPattern.matcher(response)
            val jsonStr = if (jsonMatcher.find()) jsonMatcher.group(0) else response

            Log.d("GeminiService", "Extracted JSON: $jsonStr")

            // Simple JSON parsing without a library
            // Match both quoted and unquoted values (numbers might not be quoted)
            val titlePattern = Pattern.compile("\"title\"\\s*:\\s*(?:\"([^\"]+)\"|null)")
            val amountPattern = Pattern.compile("\"amount\"\\s*:\\s*(?:\"([^\"]+)\"|([\\d.]+)|null)")
            val dueDatePattern = Pattern.compile("\"dueDate\"\\s*:\\s*(?:\"([^\"]+)\"|null)")

            val titleMatcher = titlePattern.matcher(jsonStr)
            val amountMatcher = amountPattern.matcher(jsonStr)
            val dueDateMatcher = dueDatePattern.matcher(jsonStr)

            // Extract title
            val title = if (titleMatcher.find()) {
                titleMatcher.group(1)
            } else {
                // If no title found, try to extract from the message
                extractTitleFromMessage(message)
            }

            // Extract amount
            val amountStr = if (amountMatcher.find()) {
                // Group 1 is quoted value, Group 2 is unquoted value
                amountMatcher.group(1) ?: amountMatcher.group(2)
            } else {
                // If no amount found, try to extract from the message
                extractAmountFromMessage(message)
            }

            // Extract due date
            val dueDateStr = if (dueDateMatcher.find()) {
                dueDateMatcher.group(1)
            } else {
                // If no date found, try to extract from the message
                extractDateFromMessage(message)
            }

            Log.d("GeminiService", "Extracted values - Title: $title, Amount: $amountStr, Date: $dueDateStr")

            val amount = amountStr?.toDoubleOrNull()
            val dueDate = parseDateString(dueDateStr)

            BillInfo(title, amount, dueDate)
        } catch (e: Exception) {
            Log.e("GeminiService", "Error parsing Gemini response", e)

            // Fallback to direct extraction from the message
            val title = extractTitleFromMessage(message)
            val amountStr = extractAmountFromMessage(message)
            val dueDateStr = extractDateFromMessage(message)

            val amount = amountStr?.toDoubleOrNull()
            val dueDate = parseDateString(dueDateStr)

            BillInfo(title, amount, dueDate)
        }
    }

    /**
     * Extract title from message as fallback
     */
    private fun extractTitleFromMessage(message: String): String? {
        Log.d("GeminiService", "Extracting title from message: $message")

        // Look for common bill types in English and Slovenian
        val billTypes = listOf(
            // English keywords
            "electric" to "Electric Bill",
            "electricity" to "Electric Bill",
            "power" to "Power Bill",
            "water" to "Water Bill",
            "gas" to "Gas Bill",
            "phone" to "Phone Bill",
            "mobile" to "Mobile Phone Bill",
            "internet" to "Internet Bill",
            "cable" to "Cable TV Bill",
            "rent" to "Rent Payment",
            "mortgage" to "Mortgage Payment",
            "insurance" to "Insurance Bill",
            "credit" to "Credit Card Bill",
            "loan" to "Loan Payment",
            "tax" to "Tax Payment",
            "subscription" to "Subscription Payment",

            // Slovenian keywords
            "elektrik" to "račun za elektriko",
            "elektr" to "račun za elektriko",
            "vod" to "račun za vodo",
            "plin" to "račun za plin",
            "telefon" to "račun za telefon",
            "mobitel" to "račun za mobilni telefon",
            "internet" to "račun za internet",
            "kabelska" to "račun za kabelsko TV",
            "najemnin" to "plačilo najemnine",
            "hipoteka" to "plačilo hipoteke",
            "zavarovanje" to "račun za zavarovanje",
            "kreditn" to "račun za kreditno kartico",
            "posojil" to "plačilo posojila",
            "davek" to "plačilo davka",
            "naročnin" to "plačilo naročnine"
        )

        val lowerMessage = message.lowercase()

        for ((keyword, title) in billTypes) {
            if (lowerMessage.contains(keyword)) {
                Log.d("GeminiService", "Found bill type: $title")
                return title
            }
        }

        Log.d("GeminiService", "No specific bill type found, using generic title")
        // Return localized generic bill title based on locale
        return if (Locale.getDefault().language == "sl") {
            "Račun"
        } else {
            "Bill"
        }
    }

    /**
     * Extract amount from message as fallback
     */
    private fun extractAmountFromMessage(message: String): String? {
        Log.d("GeminiService", "Extracting amount from message: $message")

        // Match patterns like: 90€, €90, $90, 90 dollars, 90.50
        // Include both standard Euro symbol and its Unicode representation
        val amountPattern = Pattern.compile("(\\d+(?:[.,]\\d+)?)\\s*[€$£]|[€$£]\\s*(\\d+(?:[.,]\\d+)?)|\\b(\\d+(?:[.,]\\d+)?)\\s*(?:dollars|euros|pounds|eur|euro)\\b")
        val matcher = amountPattern.matcher(message.lowercase())

        return if (matcher.find()) {
            // Return the first non-null group
            val amount = matcher.group(1) ?: matcher.group(2) ?: matcher.group(3)
            // Replace comma with dot for decimal separator
            val normalizedAmount = amount?.replace(',', '.')
            Log.d("GeminiService", "Extracted amount: $normalizedAmount")
            normalizedAmount
        } else {
            // Try a simpler pattern to just find numbers
            val simplePattern = Pattern.compile("\\b(\\d+(?:[.,]\\d+)?)\\b")
            val simpleMatcher = simplePattern.matcher(message)

            if (simpleMatcher.find()) {
                val amount = simpleMatcher.group(1)
                // Replace comma with dot for decimal separator
                val normalizedAmount = amount?.replace(',', '.')
                Log.d("GeminiService", "Extracted amount with simple pattern: $normalizedAmount")
                normalizedAmount
            } else {
                Log.d("GeminiService", "No amount found in message")
                null
            }
        }
    }

    /**
     * Extract date from message as fallback
     */
    private fun extractDateFromMessage(message: String): String? {
        Log.d("GeminiService", "Extracting date from message: $message")

        // Match common date formats in English and Slovenian
        val monthNames = listOf(
            // English month names
            "january", "february", "march", "april", "may", "june", "july", "august", "september", "october", "november", "december",
            "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec",
            // Slovenian month names
            "januar", "februar", "marec", "april", "maj", "junij", "julij", "avgust", "september", "oktober", "november", "december",
            "jan", "feb", "mar", "apr", "maj", "jun", "jul", "avg", "sep", "okt", "nov", "dec"
        )

        // Match patterns like: May 7, May 7th, 7th of May, 05/07, 7/5
        val datePattern = Pattern.compile("\\b(\\d{1,2})/(\\d{1,2})(?:/\\d{2,4})?\\b|\\b(\\d{1,2})-(\\d{1,2})(?:-\\d{2,4})?\\b|\\b(" + monthNames.joinToString("|") + ")\\s+(\\d{1,2})(?:st|nd|rd|th)?\\b|\\b(\\d{1,2})(?:st|nd|rd|th)?\\s+(?:of\\s+)?(" + monthNames.joinToString("|") + ")\\b")

        val matcher = datePattern.matcher(message.lowercase())

        if (matcher.find()) {
            Log.d("GeminiService", "Found date pattern in message")

            // Get the current year
            val calendar = Calendar.getInstance()
            val currentYear = calendar.get(Calendar.YEAR).toString()

            // Check which pattern matched
            if (matcher.group(1) != null && matcher.group(2) != null) {
                // MM/DD format in US or DD/MM format in Europe
                val first = matcher.group(1)
                val second = matcher.group(2)

                // Try to determine if it's MM/DD or DD/MM
                // If first > 12, it's likely DD/MM
                val month = if (first.toIntOrNull() ?: 0 > 12) second else first
                val day = if (first.toIntOrNull() ?: 0 > 12) first else second

                // Ensure month and day are two digits
                val formattedMonth = month.padStart(2, '0')
                val formattedDay = day.padStart(2, '0')

                Log.d("GeminiService", "Extracted date: $currentYear-$formattedMonth-$formattedDay")
                return "$currentYear-$formattedMonth-$formattedDay"
            } else if (matcher.group(3) != null && matcher.group(4) != null) {
                // MM-DD format
                val first = matcher.group(3)
                val second = matcher.group(4)

                // Try to determine if it's MM-DD or DD-MM
                // If first > 12, it's likely DD-MM
                val month = if (first.toIntOrNull() ?: 0 > 12) second else first
                val day = if (first.toIntOrNull() ?: 0 > 12) first else second

                // Ensure month and day are two digits
                val formattedMonth = month.padStart(2, '0')
                val formattedDay = day.padStart(2, '0')

                Log.d("GeminiService", "Extracted date: $currentYear-$formattedMonth-$formattedDay")
                return "$currentYear-$formattedMonth-$formattedDay"
            } else if (matcher.group(5) != null && matcher.group(6) != null) {
                // Month Name Day format
                val monthName = matcher.group(5)
                val day = matcher.group(6)
                val month = getMonthNumber(monthName)

                // Ensure day is two digits
                val formattedDay = day.padStart(2, '0')

                Log.d("GeminiService", "Extracted date: $currentYear-$month-$formattedDay")
                return "$currentYear-$month-$formattedDay"
            } else if (matcher.group(7) != null && matcher.group(8) != null) {
                // Day Month Name format
                val day = matcher.group(7)
                val monthName = matcher.group(8)
                val month = getMonthNumber(monthName)

                // Ensure day is two digits
                val formattedDay = day.padStart(2, '0')

                Log.d("GeminiService", "Extracted date: $currentYear-$month-$formattedDay")
                return "$currentYear-$month-$formattedDay"
            }
        }

        Log.d("GeminiService", "No date pattern found in message")
        return null
    }

    /**
     * Get month number from month name
     * Always returns a non-null String
     */
    private fun getMonthNumber(monthName: String): String {
        val monthMap = mapOf(
            // English month names
            "january" to "01", "jan" to "01",
            "february" to "02", "feb" to "02",
            "march" to "03", "mar" to "03",
            "april" to "04", "apr" to "04",
            "may" to "05",
            "june" to "06", "jun" to "06",
            "july" to "07", "jul" to "07",
            "august" to "08", "aug" to "08",
            "september" to "09", "sep" to "09",
            "october" to "10", "oct" to "10",
            "november" to "11", "nov" to "11",
            "december" to "12", "dec" to "12",

            // Slovenian month names
            "januar" to "01",
            "februar" to "02",
            "marec" to "03",
            "april" to "04",
            "maj" to "05",
            "junij" to "06",
            "julij" to "07",
            "avgust" to "08",
            "september" to "09",
            "oktober" to "10",
            "november" to "11",
            "december" to "12",
            "avg" to "08",
            "okt" to "10"
        )

        // Always returns a non-null String
        return monthMap.getOrDefault(monthName.lowercase(), "01")
    }

    /**
     * Parse a date string in YYYY-MM-DD format
     */
    private fun parseDateString(dateStr: String?, locale: Locale = Locale.getDefault()): Date? {
        if (dateStr == null || dateStr == "null") return null

        return try {
            val format = SimpleDateFormat("yyyy-MM-dd", locale)
            format.parse(dateStr)
        } catch (e: Exception) {
            Log.e("GeminiService", "Error parsing date: $dateStr", e)
            null
        }
    }

    /**
     * Data class to hold extracted bill information
     */
    data class BillInfo(
        val title: String?,
        val amount: Double?,
        val dueDate: Date?
    )
}
