package com.example.billmate.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.billmate.MainActivity
import com.example.billmate.R
import com.example.billmate.repository.BillRepository
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Service for handling Firebase Cloud Messaging (FCM) messages.
 */
class BillMateMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "BillMateMessagingService"
        private const val CHANNEL_ID = "fcm_default_channel"
        private const val NOTIFICATION_ID = 1001
    }

    /**
     * Called when a new FCM token is generated.
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "New FCM token: $token")

        // Store the token in Firestore for server-side notifications
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val billRepository = BillRepository()
                billRepository.updateFcmToken(token)
                Log.d(TAG, "FCM token updated in Firestore")
            } catch (e: Exception) {
                Log.e(TAG, "Error updating FCM token", e)
            }
        }
    }

    /**
     * Called when a message is received.
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        Log.d(TAG, "Message received from: ${remoteMessage.from}")

        // Check if message contains a notification payload
        remoteMessage.notification?.let { notification ->
            Log.d(TAG, "Message Notification Body: ${notification.body}")
            sendNotification(notification.title, notification.body)
        }

        // Check if message contains a data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")

            // Handle data payload
            val messageType = remoteMessage.data["type"]
            val billId = remoteMessage.data["billId"]

            when (messageType) {
                "bill_due" -> {
                    if (billId != null) {
                        handleBillDueNotification(billId)
                    }
                }
                "bill_upcoming" -> {
                    if (billId != null) {
                        val daysUntilDue = remoteMessage.data["daysUntilDue"]?.toIntOrNull() ?: 0
                        handleUpcomingBillNotification(billId, daysUntilDue)
                    }
                }
                else -> {
                    // Default handling for unknown message types
                    val title = remoteMessage.data["title"] ?: getString(R.string.app_name)
                    val body = remoteMessage.data["body"] ?: ""
                    sendNotification(title, body)
                }
            }
        }
    }

    /**
     * Handle a notification for a bill that is due.
     */
    private fun handleBillDueNotification(billId: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val billRepository = BillRepository()
                val billResult = billRepository.getBillByIdResult(billId)

                if (billResult.isSuccess) {
                    val bill = billResult.getOrNull()
                    if (bill != null) {
                        val notificationService = NotificationService(applicationContext)

                        // Only send notification if within the user's preferred time window
                        if (notificationService.isWithinNotificationWindow()) {
                            notificationService.sendBillDueNotification(bill)
                        } else {
                            Log.d(TAG, "Skipping notification as outside preferred time window")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling bill due notification", e)
            }
        }
    }

    /**
     * Handle a notification for an upcoming bill.
     */
    private fun handleUpcomingBillNotification(billId: String, daysUntilDue: Int) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val billRepository = BillRepository()
                val billResult = billRepository.getBillByIdResult(billId)

                if (billResult.isSuccess) {
                    val bill = billResult.getOrNull()
                    if (bill != null) {
                        val notificationService = NotificationService(applicationContext)

                        // Only send notification if within the user's preferred time window
                        if (notificationService.isWithinNotificationWindow()) {
                            notificationService.sendUpcomingBillNotification(bill, daysUntilDue)
                        } else {
                            Log.d(TAG, "Skipping notification as outside preferred time window")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling upcoming bill notification", e)
            }
        }
    }

    /**
     * Create and show a notification with the given title and body.
     * This will respect the user's preferred notification time unless it's a high-priority notification.
     */
    private fun sendNotification(title: String?, body: String?) {
        // Check if we should send the notification now based on user preferences
        val notificationService = NotificationService(applicationContext)
        if (!notificationService.isWithinNotificationWindow()) {
            // Skip notifications outside the preferred time window
            // unless they contain "urgent" in the title
            if (title == null || !title.contains("urgent", ignoreCase = true)) {
                Log.d(TAG, "Skipping notification as outside preferred time window")
                return
            }
        }
        val intent = Intent(this, MainActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val channelId = CHANNEL_ID
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title ?: getString(R.string.app_name))
            .setContentText(body ?: "")
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create the notification channel for Android 8.0+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                getString(R.string.default_notification_channel_name),
                NotificationManager.IMPORTANCE_DEFAULT
            )
            notificationManager.createNotificationChannel(channel)
        }

        notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build())
    }
}
