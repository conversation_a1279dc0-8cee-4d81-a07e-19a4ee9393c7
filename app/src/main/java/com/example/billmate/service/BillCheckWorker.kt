package com.example.billmate.service

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.billmate.model.Bill
import com.example.billmate.repository.BillRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Calendar
import java.util.Date
import java.util.concurrent.TimeUnit

/**
 * Worker class to check for bills that need notifications.
 */
class BillCheckWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "BillCheckWorker"
        const val WORK_NAME = "bill_check_work"
    }
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        Log.d(TAG, "Starting bill check worker")
        
        try {
            val notificationService = NotificationService(applicationContext)
            
            // Check if notifications are enabled
            if (!notificationService.areNotificationsEnabled()) {
                Log.d(TAG, "Notifications are disabled, skipping bill check")
                return@withContext Result.success()
            }
            
            // Check if we're within the notification time window
            if (!notificationService.isWithinNotificationWindow()) {
                Log.d(TAG, "Outside notification time window, skipping bill check")
                return@withContext Result.retry()
            }
            
            // Get all bills
            val billRepository = BillRepository()
            val billsResult = billRepository.getAllBills()
            
            if (billsResult.isSuccess) {
                val bills = billsResult.getOrNull() ?: emptyList()
                Log.d(TAG, "Found ${bills.size} bills to check")
                
                // Check for overdue bills
                checkOverdueBills(bills, notificationService)
                
                // Check for bills due today
                checkBillsDueToday(bills, notificationService)
                
                // Check for upcoming bills
                checkUpcomingBills(bills, notificationService)
                
                return@withContext Result.success()
            } else {
                Log.e(TAG, "Failed to get bills: ${billsResult.exceptionOrNull()?.message}")
                return@withContext Result.retry()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in bill check worker: ${e.message}", e)
            return@withContext Result.failure()
        }
    }
    
    /**
     * Check for overdue bills and send notifications.
     */
    private fun checkOverdueBills(bills: List<Bill>, notificationService: NotificationService) {
        val today = Date()
        
        // Filter for unpaid bills that are overdue
        val overdueBills = bills.filter { bill ->
            !bill.isPaid && bill.dueDate != null && bill.dueDate.before(today)
        }
        
        if (overdueBills.isNotEmpty()) {
            Log.d(TAG, "Found ${overdueBills.size} overdue bills")
            
            // Send notifications for overdue bills
            for (bill in overdueBills) {
                val dueDate = bill.dueDate ?: continue
                val diffInMillis = today.time - dueDate.time
                val daysOverdue = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS).toInt()
                
                if (daysOverdue > 0) {
                    Log.d(TAG, "Bill ${bill.id} is overdue by $daysOverdue days")
                    notificationService.sendOverdueBillNotification(bill, daysOverdue)
                }
            }
        } else {
            Log.d(TAG, "No overdue bills found")
        }
    }
    
    /**
     * Check for bills due today and send notifications.
     */
    private fun checkBillsDueToday(bills: List<Bill>, notificationService: NotificationService) {
        if (!notificationService.areDueDateNotificationsEnabled()) {
            Log.d(TAG, "Due date notifications are disabled")
            return
        }
        
        val today = Calendar.getInstance()
        today.set(Calendar.HOUR_OF_DAY, 0)
        today.set(Calendar.MINUTE, 0)
        today.set(Calendar.SECOND, 0)
        today.set(Calendar.MILLISECOND, 0)
        
        val tomorrow = Calendar.getInstance()
        tomorrow.timeInMillis = today.timeInMillis
        tomorrow.add(Calendar.DAY_OF_YEAR, 1)
        
        // Filter for unpaid bills due today
        val billsDueToday = bills.filter { bill ->
            !bill.isPaid && bill.dueDate != null && 
            bill.dueDate.time >= today.timeInMillis && 
            bill.dueDate.time < tomorrow.timeInMillis
        }
        
        if (billsDueToday.isNotEmpty()) {
            Log.d(TAG, "Found ${billsDueToday.size} bills due today")
            
            // Send notifications for bills due today
            for (bill in billsDueToday) {
                Log.d(TAG, "Bill ${bill.id} is due today")
                notificationService.sendBillDueNotification(bill)
            }
        } else {
            Log.d(TAG, "No bills due today")
        }
    }
    
    /**
     * Check for upcoming bills and send notifications.
     */
    private fun checkUpcomingBills(bills: List<Bill>, notificationService: NotificationService) {
        if (!notificationService.areUpcomingNotificationsEnabled()) {
            Log.d(TAG, "Upcoming notifications are disabled")
            return
        }
        
        val today = Calendar.getInstance()
        today.set(Calendar.HOUR_OF_DAY, 0)
        today.set(Calendar.MINUTE, 0)
        today.set(Calendar.SECOND, 0)
        today.set(Calendar.MILLISECOND, 0)
        
        val daysBefore = notificationService.getNotificationDaysBefore()
        
        // Filter for unpaid upcoming bills
        val upcomingBills = bills.filter { bill ->
            if (bill.isPaid || bill.dueDate == null) {
                return@filter false
            }
            
            val dueDate = Calendar.getInstance()
            dueDate.time = bill.dueDate
            dueDate.set(Calendar.HOUR_OF_DAY, 0)
            dueDate.set(Calendar.MINUTE, 0)
            dueDate.set(Calendar.SECOND, 0)
            dueDate.set(Calendar.MILLISECOND, 0)
            
            val diffInMillis = dueDate.timeInMillis - today.timeInMillis
            val daysUntilDue = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS).toInt()
            
            daysUntilDue > 0 && daysUntilDue <= daysBefore
        }
        
        if (upcomingBills.isNotEmpty()) {
            Log.d(TAG, "Found ${upcomingBills.size} upcoming bills")
            
            // Send notifications for upcoming bills
            for (bill in upcomingBills) {
                val dueDate = Calendar.getInstance()
                dueDate.time = bill.dueDate ?: continue
                dueDate.set(Calendar.HOUR_OF_DAY, 0)
                dueDate.set(Calendar.MINUTE, 0)
                dueDate.set(Calendar.SECOND, 0)
                dueDate.set(Calendar.MILLISECOND, 0)
                
                val diffInMillis = dueDate.timeInMillis - today.timeInMillis
                val daysUntilDue = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS).toInt()
                
                if (daysUntilDue > 0 && daysUntilDue <= daysBefore) {
                    Log.d(TAG, "Bill ${bill.id} is due in $daysUntilDue days")
                    notificationService.sendUpcomingBillNotification(bill, daysUntilDue)
                }
            }
        } else {
            Log.d(TAG, "No upcoming bills found")
        }
    }
}
