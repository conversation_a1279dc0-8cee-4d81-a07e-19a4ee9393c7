package com.example.billmate.service

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.example.billmate.model.EmailAttachment
import com.example.billmate.model.EmailBillExtraction
import com.example.billmate.model.EmailMessage
import com.example.billmate.utils.OcrProcessor
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.NumberFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern

/**
 * Service to extract bill information from emails.
 */
class EmailBillExtractor(private val context: Context) {
    private val TAG = "EmailBillExtractor"
    private val geminiService = GeminiService(context)
    private val ocrProcessor = OcrProcessor()

    /**
     * Extract bill information from an email.
     */
    suspend fun extractBillInfo(email: EmailMessage): Result<EmailBillExtraction> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Extracting bill info from email: ${email.subject}")

            // First, try to extract from subject and body
            val subjectAndBodyExtraction = extractFromSubjectAndBody(email)

            // If we have high confidence from subject and body, return that
            if (subjectAndBodyExtraction.confidence > 0.7f) {
                return@withContext Result.success(subjectAndBodyExtraction)
            }

            // Otherwise, try to extract from attachments if available
            if (email.hasAttachments) {
                val attachmentExtraction = extractFromAttachments(email)

                // If attachment extraction has higher confidence, return that
                if (attachmentExtraction.confidence > subjectAndBodyExtraction.confidence) {
                    return@withContext Result.success(attachmentExtraction)
                }
            }

            // Fall back to subject and body extraction
            Result.success(subjectAndBodyExtraction)
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting bill info", e)
            Result.failure(e)
        }
    }

    /**
     * Extract bill information from email subject and body.
     */
    private suspend fun extractFromSubjectAndBody(email: EmailMessage): EmailBillExtraction {
        // Combine subject and body for extraction
        val content = "${email.subject}\n\n${email.body}"

        try {
            // Use Gemini to extract bill information
            val billInfo = geminiService.extractBillInfo(content)

            // Calculate confidence based on how many fields were extracted
            val fieldsExtracted = listOf(
                billInfo.title != null,
                billInfo.amount != null,
                billInfo.dueDate != null
            ).count { it }

            val confidence = fieldsExtracted / 3.0f

            return EmailBillExtraction(
                emailId = email.id,
                title = billInfo.title,
                amount = billInfo.amount,
                dueDate = billInfo.dueDate,
                description = email.subject,
                confidence = confidence,
                extractedFrom = "SUBJECT_BODY"
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error using Gemini for extraction, falling back to regex", e)

            // Fall back to regex extraction
            val title = extractTitleFromText(content)
            val amount = extractAmountFromText(content)
            val dueDate = extractDateFromText(content)

            // Calculate confidence based on how many fields were extracted
            val fieldsExtracted = listOf(
                title != null,
                amount != null,
                dueDate != null
            ).count { it }

            val confidence = fieldsExtracted / 3.0f * 0.7f // Lower confidence for regex extraction

            return EmailBillExtraction(
                emailId = email.id,
                title = title,
                amount = amount?.toDouble(),
                dueDate = dueDate,
                description = email.subject,
                confidence = confidence,
                extractedFrom = "SUBJECT_BODY_REGEX"
            )
        }
    }

    /**
     * Extract bill information from email attachments.
     */
    private suspend fun extractFromAttachments(email: EmailMessage): EmailBillExtraction {
        // For now, we'll just return a placeholder
        // In a real implementation, we would:
        // 1. Download the attachments
        // 2. Process PDFs and images using OCR
        // 3. Extract bill information from the OCR text

        return EmailBillExtraction(
            emailId = email.id,
            title = null,
            amount = null,
            dueDate = null,
            description = email.subject,
            confidence = 0.0f,
            extractedFrom = "ATTACHMENT"
        )
    }

    /**
     * Extract title from text using regex.
     */
    private fun extractTitleFromText(text: String): String? {
        // Common bill keywords
        val billKeywords = listOf(
            "electric", "electricity", "power", "water", "gas", "phone", "mobile",
            "internet", "cable", "rent", "mortgage", "insurance", "credit card",
            "loan", "tax", "subscription", "bill", "invoice", "statement", "payment"
        )

        val lowerText = text.lowercase()

        // Look for bill keywords in the text
        for (keyword in billKeywords) {
            if (lowerText.contains(keyword)) {
                // Extract a phrase around the keyword
                val pattern = Pattern.compile("\\b\\w+\\s+$keyword|$keyword\\s+\\w+\\b", Pattern.CASE_INSENSITIVE)
                val matcher = pattern.matcher(text)

                if (matcher.find()) {
                    return matcher.group(0).capitalize()
                }

                // If no phrase found, just return the keyword
                return keyword.capitalize()
            }
        }

        return null
    }

    /**
     * Extract amount from text using regex.
     */
    private fun extractAmountFromText(text: String): String? {
        // Match patterns like: $100, 100€, €100, 100 EUR, 100.50, 1,234.56
        val amountPattern = Pattern.compile("\\$?(\\d{1,3}(,\\d{3})*(\\.\\d{1,2})?)(\\s*\\$|€|EUR|USD)?|€?(\\d{1,3}(,\\d{3})*(\\.\\d{1,2})?)")
        val matcher = amountPattern.matcher(text)

        if (matcher.find()) {
            // Extract just the number part
            val amount = matcher.group(1) ?: matcher.group(5)
            return amount?.replace(",", "")
        }

        return null
    }

    /**
     * Extract date from text using regex.
     */
    private fun extractDateFromText(text: String): Date? {
        // List of month names for pattern matching
        val monthNames = listOf(
            "january", "february", "march", "april", "may", "june",
            "july", "august", "september", "october", "november", "december",
            "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"
        )

        // Match patterns like: May 7, May 7th, 7th of May, 05/07, 7/5
        val datePattern = Pattern.compile("\\b(\\d{1,2})/(\\d{1,2})(?:/\\d{2,4})?\\b|\\b(\\d{1,2})-(\\d{1,2})(?:-\\d{2,4})?\\b|\\b(" + monthNames.joinToString("|") + ")\\s+(\\d{1,2})(?:st|nd|rd|th)?\\b|\\b(\\d{1,2})(?:st|nd|rd|th)?\\s+(?:of\\s+)?(" + monthNames.joinToString("|") + ")\\b")

        val matcher = datePattern.matcher(text.lowercase())

        if (matcher.find()) {
            // Get the current year
            val calendar = Calendar.getInstance()
            val currentYear = calendar.get(Calendar.YEAR)

            try {
                // Try to parse the date
                if (matcher.group(1) != null && matcher.group(2) != null) {
                    // Format: MM/DD or DD/MM
                    val month = matcher.group(1)!!.toInt()
                    val day = matcher.group(2)!!.toInt()

                    // Determine if it's MM/DD or DD/MM based on values
                    val formattedDate = if (month <= 12) {
                        "$currentYear-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}"
                    } else {
                        "$currentYear-${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}"
                    }

                    return SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(formattedDate)
                } else if (matcher.group(3) != null && matcher.group(4) != null) {
                    // Format: MM-DD or DD-MM
                    val month = matcher.group(3)!!.toInt()
                    val day = matcher.group(4)!!.toInt()

                    // Determine if it's MM-DD or DD-MM based on values
                    val formattedDate = if (month <= 12) {
                        "$currentYear-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}"
                    } else {
                        "$currentYear-${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}"
                    }

                    return SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(formattedDate)
                } else if (matcher.group(5) != null && matcher.group(6) != null) {
                    // Format: Month Day
                    val month = getMonthNumber(matcher.group(5)!!)
                    val day = matcher.group(6)!!.toInt()

                    val formattedDate = "$currentYear-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}"
                    return SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(formattedDate)
                } else if (matcher.group(7) != null && matcher.group(8) != null) {
                    // Format: Day Month
                    val day = matcher.group(7)!!.toInt()
                    val month = getMonthNumber(matcher.group(8)!!)

                    val formattedDate = "$currentYear-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}"
                    return SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(formattedDate)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing date", e)
            }
        }

        return null
    }

    /**
     * Get month number from month name.
     */
    private fun getMonthNumber(monthName: String): Int {
        return when (monthName.lowercase().take(3)) {
            "jan" -> 1
            "feb" -> 2
            "mar" -> 3
            "apr" -> 4
            "may" -> 5
            "jun" -> 6
            "jul" -> 7
            "aug" -> 8
            "sep" -> 9
            "oct" -> 10
            "nov" -> 11
            "dec" -> 12
            else -> 1 // Default to January
        }
    }

    /**
     * Convert EmailBillExtraction to Bill.
     */
    fun convertToBill(extraction: EmailBillExtraction): Bill {
        return Bill(
            id = "",  // Will be set by Firestore
            title = extraction.title ?: "Unknown Bill",
            amount = extraction.amount ?: 0.0,
            description = "Extracted from email: ${extraction.description}",
            dueDate = extraction.dueDate,
            isPaid = false,
            category = BillCategory.PERSONAL  // Default category
        )
    }
}
