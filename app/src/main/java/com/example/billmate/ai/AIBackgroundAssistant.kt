package com.example.billmate.ai

import android.content.Context
import android.util.Log
import com.example.billmate.model.Bill
import com.example.billmate.repository.BillRepository
import com.example.billmate.service.GeminiService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

/**
 * AI Background Assistant that provides intelligent insights and suggestions
 */
class AIBackgroundAssistant(private val context: Context) {

    companion object {
        private const val TAG = "AIBackgroundAssistant"
    }

    private val geminiService = GeminiService(context)
    private val billRepository = BillRepository(context)

    /**
     * Analyze bills and suggest missing recurring bills
     */
    suspend fun analyzeRecurringBills(): List<RecurringSuggestion> = withContext(Dispatchers.IO) {
        try {
            val bills = billRepository.getAllBills().getOrElse { emptyList() }
            val suggestions = mutableListOf<RecurringSuggestion>()

            // Group bills by title (normalized)
            val billGroups = bills.groupBy { normalizeBillTitle(it.title) }

            // Find potential recurring bills
            for ((normalizedTitle, billList) in billGroups) {
                if (billList.size >= 2) {
                    val suggestion = analyzeRecurringPattern(normalizedTitle, billList)
                    if (suggestion != null) {
                        suggestions.add(suggestion)
                    }
                }
            }

            Log.d(TAG, "Found ${suggestions.size} recurring bill suggestions")
            suggestions
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing recurring bills", e)
            emptyList()
        }
    }

    /**
     * Detect spending anomalies using AI
     */
    suspend fun detectSpendingAnomalies(): List<SpendingAlert> = withContext(Dispatchers.IO) {
        try {
            val bills = billRepository.getAllBills().getOrElse { emptyList() }
            val alerts = mutableListOf<SpendingAlert>()

            // Calculate monthly spending
            val monthlySpending = calculateMonthlySpending(bills)

            if (monthlySpending.size >= 2) {
                val currentMonth = monthlySpending.values.lastOrNull() ?: 0.0
                val previousMonths = monthlySpending.values.toList().dropLast(1)
                val averagePrevious = previousMonths.average()

                // Check for significant increase (>30%)
                val increasePercentage = ((currentMonth - averagePrevious) / averagePrevious) * 100.0

                if (increasePercentage > 30) {
                    alerts.add(
                        SpendingAlert(
                            type = SpendingAlertType.SIGNIFICANT_INCREASE,
                            message = "Poraba se je povečala za ${increasePercentage.toInt()}% glede na povprečje",
                            currentAmount = currentMonth,
                            previousAverage = averagePrevious,
                            percentage = increasePercentage
                        )
                    )
                }

                // Check for unusual bills this month
                val currentMonthBills = getCurrentMonthBills(bills)
                val unusualBills = findUnusualBills(currentMonthBills, bills)

                if (unusualBills.isNotEmpty()) {
                    alerts.add(
                        SpendingAlert(
                            type = SpendingAlertType.UNUSUAL_BILLS,
                            message = "Zaznani nenavadni računi: ${unusualBills.joinToString(", ") { it.title }}",
                            unusualBills = unusualBills
                        )
                    )
                }
            }

            Log.d(TAG, "Found ${alerts.size} spending alerts")
            alerts
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting spending anomalies", e)
            emptyList()
        }
    }

    /**
     * Enhance OCR results using AI
     */
    suspend fun enhanceOCRResult(ocrText: String, partialBill: Map<String, String?>): EnhancedBillData = withContext(Dispatchers.IO) {
        try {
            // Get historical bills for context
            val recentBills = billRepository.getAllBills().getOrElse { emptyList() }.takeLast(50)

            // Create AI prompt for enhancement
            val prompt = createOCREnhancementPrompt(ocrText, partialBill, recentBills)

            // Get AI enhancement
            val aiResponse = geminiService.sendMessage(prompt)

            // Parse AI response
            parseEnhancementResponse(aiResponse, partialBill)
        } catch (e: Exception) {
            Log.e(TAG, "Error enhancing OCR result", e)
            // Return original data if AI enhancement fails
            EnhancedBillData(
                title = partialBill["title"],
                amount = partialBill["amount"]?.toDoubleOrNull(),
                dueDate = partialBill["dueDate"],
                confidence = 0.5,
                aiEnhanced = false
            )
        }
    }

    /**
     * Normalize bill title for pattern matching
     */
    private fun normalizeBillTitle(title: String): String {
        return title.lowercase()
            .replace(Regex("[0-9]"), "") // Remove numbers
            .replace(Regex("[^a-z\\s]"), "") // Remove special characters
            .trim()
    }

    /**
     * Analyze recurring pattern for a group of bills
     */
    private fun analyzeRecurringPattern(normalizedTitle: String, bills: List<Bill>): RecurringSuggestion? {
        if (bills.size < 2) return null

        // Sort bills by date
        val sortedBills = bills.sortedBy { it.dueDate }

        // Check if we're missing a recent bill (last 2 months)
        val now = Calendar.getInstance()
        val twoMonthsAgo = Calendar.getInstance().apply { add(Calendar.MONTH, -2) }

        val recentBills = sortedBills.filter { bill ->
            bill.dueDate?.after(twoMonthsAgo.time) == true
        }

        // If no recent bills, suggest adding one
        if (recentBills.isEmpty() && sortedBills.isNotEmpty()) {
            val lastBill = sortedBills.last()
            val averageAmount = bills.map { it.amount }.average()

            return RecurringSuggestion(
                title = lastBill.title,
                suggestedAmount = averageAmount,
                lastSeen = lastBill.dueDate,
                frequency = estimateFrequency(sortedBills),
                confidence = calculateConfidence(bills)
            )
        }

        return null
    }

    /**
     * Calculate monthly spending
     */
    private fun calculateMonthlySpending(bills: List<Bill>): Map<String, Double> {
        val monthlySpending = mutableMapOf<String, Double>()
        val dateFormat = SimpleDateFormat("yyyy-MM", Locale.getDefault())

        for (bill in bills) {
            bill.dueDate?.let { date ->
                val monthKey = dateFormat.format(date)
                monthlySpending[monthKey] = monthlySpending.getOrDefault(monthKey, 0.0) + bill.amount
            }
        }

        return monthlySpending.toSortedMap()
    }

    /**
     * Get bills from current month
     */
    private fun getCurrentMonthBills(bills: List<Bill>): List<Bill> {
        val now = Calendar.getInstance()
        val currentMonth = now.get(Calendar.MONTH)
        val currentYear = now.get(Calendar.YEAR)

        return bills.filter { bill ->
            bill.dueDate?.let { date ->
                val billCalendar = Calendar.getInstance().apply { time = date }
                billCalendar.get(Calendar.MONTH) == currentMonth &&
                billCalendar.get(Calendar.YEAR) == currentYear
            } ?: false
        }
    }

    /**
     * Find unusual bills (not seen in previous months)
     */
    private fun findUnusualBills(currentMonthBills: List<Bill>, allBills: List<Bill>): List<Bill> {
        val historicalTitles = allBills
            .filter { !getCurrentMonthBills(allBills).contains(it) }
            .map { normalizeBillTitle(it.title) }
            .toSet()

        return currentMonthBills.filter { bill ->
            normalizeBillTitle(bill.title) !in historicalTitles
        }
    }

    /**
     * Estimate frequency of recurring bills
     */
    private fun estimateFrequency(sortedBills: List<Bill>): RecurringFrequency {
        if (sortedBills.size < 2) return RecurringFrequency.UNKNOWN

        val intervals = mutableListOf<Long>()
        for (i in 1 until sortedBills.size) {
            val prev = sortedBills[i-1].dueDate
            val curr = sortedBills[i].dueDate
            if (prev != null && curr != null) {
                intervals.add(curr.time - prev.time)
            }
        }

        if (intervals.isEmpty()) return RecurringFrequency.UNKNOWN

        val averageInterval = intervals.average()
        val daysInterval = averageInterval / (1000 * 60 * 60 * 24)

        return when {
            daysInterval < 35 -> RecurringFrequency.MONTHLY
            daysInterval < 100 -> RecurringFrequency.QUARTERLY
            daysInterval < 400 -> RecurringFrequency.YEARLY
            else -> RecurringFrequency.UNKNOWN
        }
    }

    /**
     * Calculate confidence score for recurring suggestion
     */
    private fun calculateConfidence(bills: List<Bill>): Double {
        val amountVariance = bills.map { it.amount }.let { amounts ->
            val avg = amounts.average()
            amounts.map { (it - avg) * (it - avg) }.average()
        }

        // Lower variance = higher confidence
        val varianceScore = 1.0 / (1.0 + amountVariance / 100.0)
        val frequencyScore = minOf(bills.size / 5.0, 1.0) // More bills = higher confidence

        return (varianceScore + frequencyScore) / 2.0
    }

    /**
     * Create OCR enhancement prompt
     */
    private fun createOCREnhancementPrompt(
        ocrText: String,
        partialBill: Map<String, String?>,
        recentBills: List<Bill>
    ): String {
        val recentBillsContext = recentBills.takeLast(10).joinToString("\n") {
            "- ${it.title}: €${it.amount}"
        }

        return """
        Analiziraj OCR besedilo in delno pridobljene podatke o računu. Uporabi kontekst nedavnih računov za izboljšanje.

        OCR besedilo: "$ocrText"

        Delno pridobljeni podatki:
        - Naslov: ${partialBill["title"] ?: "ni zaznano"}
        - Znesek: ${partialBill["amount"] ?: "ni zaznano"}
        - Rok plačila: ${partialBill["dueDate"] ?: "ni zaznano"}

        Nedavni računi za kontekst:
        $recentBillsContext

        Prosim, izboljšaj podatke in vrni v JSON formatu:
        {
          "title": "izboljšan naslov",
          "amount": "izboljšan znesek",
          "dueDate": "izboljšan datum",
          "confidence": 0.8
        }
        """.trimIndent()
    }

    /**
     * Parse AI enhancement response
     */
    private fun parseEnhancementResponse(response: String, fallback: Map<String, String?>): EnhancedBillData {
        return try {
            // Simple JSON parsing (in production, use proper JSON library)
            val titleMatch = Regex("\"title\"\\s*:\\s*\"([^\"]+)\"").find(response)
            val amountMatch = Regex("\"amount\"\\s*:\\s*\"?([0-9.,]+)\"?").find(response)
            val dateMatch = Regex("\"dueDate\"\\s*:\\s*\"([^\"]+)\"").find(response)
            val confidenceMatch = Regex("\"confidence\"\\s*:\\s*([0-9.]+)").find(response)

            EnhancedBillData(
                title = titleMatch?.groupValues?.get(1) ?: fallback["title"],
                amount = amountMatch?.groupValues?.get(1)?.replace(",", ".")?.toDoubleOrNull()
                    ?: fallback["amount"]?.toDoubleOrNull(),
                dueDate = dateMatch?.groupValues?.get(1) ?: fallback["dueDate"],
                confidence = confidenceMatch?.groupValues?.get(1)?.toDoubleOrNull() ?: 0.7,
                aiEnhanced = true
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing AI response", e)
            EnhancedBillData(
                title = fallback["title"],
                amount = fallback["amount"]?.toDoubleOrNull(),
                dueDate = fallback["dueDate"],
                confidence = 0.5,
                aiEnhanced = false
            )
        }
    }
}
