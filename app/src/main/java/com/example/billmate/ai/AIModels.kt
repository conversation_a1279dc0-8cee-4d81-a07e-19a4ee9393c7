package com.example.billmate.ai

import com.example.billmate.model.Bill
import java.util.*

/**
 * Data models for AI Background Assistant
 */

/**
 * Suggestion for a missing recurring bill
 */
data class RecurringSuggestion(
    val title: String,
    val suggestedAmount: Double,
    val lastSeen: Date?,
    val frequency: RecurringFrequency,
    val confidence: Double,
    val id: String = UUID.randomUUID().toString()
) {
    fun getDisplayMessage(): String {
        val amountStr = "€${String.format("%.2f", suggestedAmount).replace('.', ',')}"
        return when (frequency) {
            RecurringFrequency.MONTHLY -> "Manjka mesečni račun: $title (~$amountStr)"
            RecurringFrequency.QUARTERLY -> "Manjka četrtletni račun: $title (~$amountStr)"
            RecurringFrequency.YEARLY -> "Manjka letni račun: $title (~$amountStr)"
            RecurringFrequency.UNKNOWN -> "<PERSON><PERSON>a račun: $title (~$amountStr)"
        }
    }
}

/**
 * Frequency of recurring bills
 */
enum class RecurringFrequency {
    MONTHLY,
    QUARTERLY, 
    YEARLY,
    UNKNOWN
}

/**
 * Alert for spending anomalies
 */
data class SpendingAlert(
    val type: SpendingAlertType,
    val message: String,
    val currentAmount: Double? = null,
    val previousAverage: Double? = null,
    val percentage: Double? = null,
    val unusualBills: List<Bill>? = null,
    val id: String = UUID.randomUUID().toString(),
    val timestamp: Date = Date()
) {
    fun getSeverity(): AlertSeverity {
        return when (type) {
            SpendingAlertType.SIGNIFICANT_INCREASE -> {
                when {
                    percentage != null && percentage > 50 -> AlertSeverity.HIGH
                    percentage != null && percentage > 30 -> AlertSeverity.MEDIUM
                    else -> AlertSeverity.LOW
                }
            }
            SpendingAlertType.UNUSUAL_BILLS -> AlertSeverity.MEDIUM
            SpendingAlertType.BUDGET_EXCEEDED -> AlertSeverity.HIGH
        }
    }
}

/**
 * Types of spending alerts
 */
enum class SpendingAlertType {
    SIGNIFICANT_INCREASE,
    UNUSUAL_BILLS,
    BUDGET_EXCEEDED
}

/**
 * Alert severity levels
 */
enum class AlertSeverity {
    LOW,
    MEDIUM,
    HIGH
}

/**
 * Enhanced bill data from AI processing
 */
data class EnhancedBillData(
    val title: String?,
    val amount: Double?,
    val dueDate: String?,
    val confidence: Double,
    val aiEnhanced: Boolean,
    val suggestions: List<String> = emptyList()
) {
    fun isHighConfidence(): Boolean = confidence > 0.8
    fun isMediumConfidence(): Boolean = confidence > 0.5
    fun isLowConfidence(): Boolean = confidence <= 0.5
}

/**
 * AI insight for user dashboard
 */
data class AIInsight(
    val type: InsightType,
    val title: String,
    val message: String,
    val actionText: String? = null,
    val actionData: Map<String, Any>? = null,
    val priority: InsightPriority = InsightPriority.MEDIUM,
    val id: String = UUID.randomUUID().toString(),
    val timestamp: Date = Date()
)

/**
 * Types of AI insights
 */
enum class InsightType {
    RECURRING_SUGGESTION,
    SPENDING_ALERT,
    OCR_ENHANCEMENT,
    BUDGET_ADVICE,
    PAYMENT_REMINDER
}

/**
 * Priority levels for insights
 */
enum class InsightPriority {
    LOW,
    MEDIUM,
    HIGH,
    URGENT
}

/**
 * AI analysis result
 */
data class AIAnalysisResult(
    val recurringSuggestions: List<RecurringSuggestion>,
    val spendingAlerts: List<SpendingAlert>,
    val insights: List<AIInsight>,
    val analysisTimestamp: Date = Date()
) {
    fun hasHighPriorityItems(): Boolean {
        return spendingAlerts.any { it.getSeverity() == AlertSeverity.HIGH } ||
               insights.any { it.priority == InsightPriority.HIGH || it.priority == InsightPriority.URGENT }
    }
    
    fun getTotalSuggestions(): Int {
        return recurringSuggestions.size + spendingAlerts.size + insights.size
    }
}

/**
 * AI processing status
 */
enum class AIProcessingStatus {
    IDLE,
    ANALYZING,
    COMPLETED,
    ERROR
}

/**
 * AI configuration settings
 */
data class AISettings(
    val enableRecurringSuggestions: Boolean = true,
    val enableSpendingAlerts: Boolean = true,
    val enableOCREnhancement: Boolean = true,
    val spendingAlertThreshold: Double = 30.0, // Percentage increase threshold
    val analysisFrequency: AnalysisFrequency = AnalysisFrequency.WEEKLY
)

/**
 * How often to run AI analysis
 */
enum class AnalysisFrequency {
    DAILY,
    WEEKLY,
    MONTHLY
}
