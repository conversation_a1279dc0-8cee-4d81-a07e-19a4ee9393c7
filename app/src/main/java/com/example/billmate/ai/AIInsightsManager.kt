package com.example.billmate.ai

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*

/**
 * Manages AI insights and background analysis
 */
class AIInsightsManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AIInsightsManager"
        private const val PREFS_NAME = "ai_insights_prefs"
        private const val KEY_LAST_ANALYSIS = "last_analysis_timestamp"
        private const val KEY_ANALYSIS_FREQUENCY = "analysis_frequency"
        private const val ANALYSIS_INTERVAL_HOURS = 24 // Run analysis daily
    }
    
    private val aiAssistant = AIBackgroundAssistant(context)
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    private val _analysisResult = MutableStateFlow<AIAnalysisResult?>(null)
    val analysisResult: StateFlow<AIAnalysisResult?> = _analysisResult.asStateFlow()
    
    private val _processingStatus = MutableStateFlow(AIProcessingStatus.IDLE)
    val processingStatus: StateFlow<AIProcessingStatus> = _processingStatus.asStateFlow()
    
    private val _activeInsights = MutableStateFlow<List<AIInsight>>(emptyList())
    val activeInsights: StateFlow<List<AIInsight>> = _activeInsights.asStateFlow()
    
    private var analysisJob: Job? = null
    
    /**
     * Start background AI analysis
     */
    fun startBackgroundAnalysis() {
        if (shouldRunAnalysis()) {
            runAnalysis()
        }
    }
    
    /**
     * Run AI analysis manually
     */
    fun runAnalysis() {
        analysisJob?.cancel()
        analysisJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                _processingStatus.value = AIProcessingStatus.ANALYZING
                Log.d(TAG, "Starting AI analysis...")
                
                // Run all AI analysis tasks
                val recurringSuggestions = aiAssistant.analyzeRecurringBills()
                val spendingAlerts = aiAssistant.detectSpendingAnomalies()
                
                // Convert to insights
                val insights = mutableListOf<AIInsight>()
                
                // Add recurring suggestions as insights
                recurringSuggestions.forEach { suggestion ->
                    insights.add(
                        AIInsight(
                            type = InsightType.RECURRING_SUGGESTION,
                            title = "Manjkajoči račun",
                            message = suggestion.getDisplayMessage(),
                            actionText = "Dodaj račun",
                            actionData = mapOf(
                                "title" to suggestion.title,
                                "amount" to suggestion.suggestedAmount
                            ),
                            priority = when {
                                suggestion.confidence > 0.8 -> InsightPriority.HIGH
                                suggestion.confidence > 0.6 -> InsightPriority.MEDIUM
                                else -> InsightPriority.LOW
                            }
                        )
                    )
                }
                
                // Add spending alerts as insights
                spendingAlerts.forEach { alert ->
                    insights.add(
                        AIInsight(
                            type = InsightType.SPENDING_ALERT,
                            title = "Opozorilo o porabi",
                            message = alert.message,
                            actionText = "Poglej podrobnosti",
                            priority = when (alert.getSeverity()) {
                                AlertSeverity.HIGH -> InsightPriority.HIGH
                                AlertSeverity.MEDIUM -> InsightPriority.MEDIUM
                                AlertSeverity.LOW -> InsightPriority.LOW
                            }
                        )
                    )
                }
                
                // Create analysis result
                val result = AIAnalysisResult(
                    recurringSuggestions = recurringSuggestions,
                    spendingAlerts = spendingAlerts,
                    insights = insights
                )
                
                // Update state
                _analysisResult.value = result
                _activeInsights.value = insights.sortedByDescending { it.priority.ordinal }
                _processingStatus.value = AIProcessingStatus.COMPLETED
                
                // Save analysis timestamp
                saveLastAnalysisTimestamp()
                
                Log.d(TAG, "AI analysis completed: ${result.getTotalSuggestions()} insights found")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error during AI analysis", e)
                _processingStatus.value = AIProcessingStatus.ERROR
            }
        }
    }
    
    /**
     * Enhance OCR result with AI
     */
    suspend fun enhanceOCRResult(ocrText: String, partialBill: Map<String, String?>): EnhancedBillData {
        return try {
            Log.d(TAG, "Enhancing OCR result with AI...")
            val enhanced = aiAssistant.enhanceOCRResult(ocrText, partialBill)
            
            // Add OCR enhancement insight if AI made improvements
            if (enhanced.aiEnhanced && enhanced.isHighConfidence()) {
                val insight = AIInsight(
                    type = InsightType.OCR_ENHANCEMENT,
                    title = "AI je izboljšal skeniranje",
                    message = "Podatki o računu so bili samodejno izboljšani z AI",
                    priority = InsightPriority.MEDIUM
                )
                
                // Add to active insights
                val currentInsights = _activeInsights.value.toMutableList()
                currentInsights.add(0, insight) // Add at top
                _activeInsights.value = currentInsights
            }
            
            enhanced
        } catch (e: Exception) {
            Log.e(TAG, "Error enhancing OCR result", e)
            EnhancedBillData(
                title = partialBill["title"],
                amount = partialBill["amount"]?.toDoubleOrNull(),
                dueDate = partialBill["dueDate"],
                confidence = 0.5,
                aiEnhanced = false
            )
        }
    }
    
    /**
     * Dismiss an insight
     */
    fun dismissInsight(insightId: String) {
        val currentInsights = _activeInsights.value.toMutableList()
        currentInsights.removeAll { it.id == insightId }
        _activeInsights.value = currentInsights
        
        Log.d(TAG, "Dismissed insight: $insightId")
    }
    
    /**
     * Get high priority insights
     */
    fun getHighPriorityInsights(): List<AIInsight> {
        return _activeInsights.value.filter { 
            it.priority == InsightPriority.HIGH || it.priority == InsightPriority.URGENT 
        }
    }
    
    /**
     * Get insights by type
     */
    fun getInsightsByType(type: InsightType): List<AIInsight> {
        return _activeInsights.value.filter { it.type == type }
    }
    
    /**
     * Check if analysis should run
     */
    private fun shouldRunAnalysis(): Boolean {
        val lastAnalysis = sharedPreferences.getLong(KEY_LAST_ANALYSIS, 0)
        val now = System.currentTimeMillis()
        val hoursSinceLastAnalysis = (now - lastAnalysis) / (1000 * 60 * 60)
        
        return hoursSinceLastAnalysis >= ANALYSIS_INTERVAL_HOURS
    }
    
    /**
     * Save timestamp of last analysis
     */
    private fun saveLastAnalysisTimestamp() {
        sharedPreferences.edit()
            .putLong(KEY_LAST_ANALYSIS, System.currentTimeMillis())
            .apply()
    }
    
    /**
     * Clear all insights
     */
    fun clearAllInsights() {
        _activeInsights.value = emptyList()
        _analysisResult.value = null
        Log.d(TAG, "Cleared all AI insights")
    }
    
    /**
     * Get analysis summary for dashboard
     */
    fun getAnalysisSummary(): String? {
        val result = _analysisResult.value ?: return null
        
        val totalSuggestions = result.getTotalSuggestions()
        if (totalSuggestions == 0) return null
        
        return when {
            result.hasHighPriorityItems() -> "AI je našel $totalSuggestions pomembnih vpogledov"
            totalSuggestions == 1 -> "AI je našel 1 vpogled"
            else -> "AI je našel $totalSuggestions vpogledov"
        }
    }
    
    /**
     * Stop background analysis
     */
    fun stopAnalysis() {
        analysisJob?.cancel()
        _processingStatus.value = AIProcessingStatus.IDLE
        Log.d(TAG, "Stopped AI analysis")
    }
}
