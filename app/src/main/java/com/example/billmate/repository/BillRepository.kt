package com.example.billmate.repository

import android.content.Context
import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.example.billmate.notifications.NotificationScheduler
import com.example.billmate.utils.DuplicateDetector
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import java.util.Date
import java.util.concurrent.ConcurrentHashMap

/**
 * Repository class for handling bill-related operations with Firestore.
 */
class BillRepository(private val context: Context? = null) {
    private val firestore = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    private val billsCollection = firestore.collection("bills")

    // Notification scheduler (only if context is provided)
    private val notificationScheduler: NotificationScheduler? = context?.let { NotificationScheduler(it) }

    // In-memory cache for bills
    private val billCache = ConcurrentHashMap<String, Bill>()

    // Cache for bill lists by category
    private val allBillsCache = MutableStateFlow<List<Bill>?>(null)
    private val personalBillsCache = MutableStateFlow<List<Bill>?>(null)
    private val businessBillsCache = MutableStateFlow<List<Bill>?>(null)
    private val upcomingBillsCache = MutableStateFlow<List<Bill>?>(null)

    // Last query timestamps to implement debouncing
    private var lastAllBillsQuery = 0L
    private var lastPersonalBillsQuery = 0L
    private var lastBusinessBillsQuery = 0L
    private var lastUpcomingBillsQuery = 0L

    // Minimum time between queries in milliseconds (500ms)
    private val queryDebounceTime = 500L

    /**
     * Get the current user ID or empty string if not logged in.
     */
    fun getCurrentUserId(): String {
        return auth.currentUser?.uid ?: ""
    }

    /**
     * Check for potential duplicates before adding a bill.
     *
     * @param bill The bill to check for duplicates
     * @return List of potential duplicate matches
     */
    suspend fun checkForDuplicates(bill: Bill): Result<List<DuplicateDetector.DuplicateMatch>> {
        return try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            // Get all existing bills for the user
            val existingBillsResult = getAllBills()
            if (existingBillsResult.isFailure) {
                return Result.failure(existingBillsResult.exceptionOrNull() ?: Exception("Failed to get existing bills"))
            }

            val existingBills = existingBillsResult.getOrNull() ?: emptyList()
            val duplicates = DuplicateDetector.findPotentialDuplicates(bill, existingBills)

            android.util.Log.d("BillRepository", "Found ${duplicates.size} potential duplicates for bill: ${bill.title}")

            Result.success(duplicates)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error checking for duplicates: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Add a new bill to Firestore.
     */
    suspend fun addBill(bill: Bill): Result<String> {
        return try {
            val startTime = System.currentTimeMillis()
            android.util.Log.d("PerformanceMonitor", "Starting addBill operation")

            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            // Create a new bill with the current user ID and timestamps
            val newBill = bill.copy(
                userId = userId,
                createdAt = com.google.firebase.Timestamp.now(),
                updatedAt = com.google.firebase.Timestamp.now()
            )

            android.util.Log.d("BillRepository", "Adding new bill: $newBill")

            // Add the bill to Firestore
            val docRef = billsCollection.add(newBill).await()
            val billId = docRef.id

            // Create a copy with the generated ID
            val billWithId = newBill.copy(id = billId)

            // Add to cache
            billCache[billId] = billWithId

            // Invalidate list caches to force refresh
            invalidateListCaches()

            // Schedule notifications for the new bill
            notificationScheduler?.scheduleBillNotifications(billWithId)

            val queryTime = System.currentTimeMillis() - startTime
            android.util.Log.d("PerformanceMonitor", "Firestore addBill operation took $queryTime ms")

            Result.success(billId)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error adding bill: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Update an existing bill.
     */
    suspend fun updateBill(bill: Bill): Result<Unit> {
        android.util.Log.d("BillRepository", "updateBill called with bill: $bill")
        return try {
            val userId = getCurrentUserId()
            android.util.Log.d("BillRepository", "Current userId: $userId, Bill userId: ${bill.userId}")

            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            if (userId != bill.userId) {
                android.util.Log.e("BillRepository", "Unauthorized to update bill: ${bill.id}")
                return Result.failure(IllegalStateException("Unauthorized to update this bill"))
            }

            val startTime = System.currentTimeMillis()

            // Get the original bill to preserve any fields that might be missing
            val originalBill = billsCollection.document(bill.id).get().await().toObject(Bill::class.java)

            if (originalBill != null) {
                android.util.Log.d("BillRepository", "Original bill: $originalBill")

                // Create an updated bill that preserves the original userId
                val updatedBill = bill.copy(
                    userId = originalBill.userId,
                    createdAt = originalBill.createdAt,
                    updatedAt = com.google.firebase.Timestamp.now()
                )

                android.util.Log.d("BillRepository", "Updating bill in Firestore: $updatedBill")
                billsCollection.document(bill.id).set(updatedBill).await()

                // Update the bill in the cache
                billCache[bill.id] = updatedBill

                // Invalidate list caches to force refresh
                invalidateListCaches()

                // Reschedule notifications for the updated bill
                notificationScheduler?.cancelBillNotifications(bill.id)
                notificationScheduler?.scheduleBillNotifications(updatedBill)
            } else {
                // If we can't find the original bill, just update with what we have
                android.util.Log.d("BillRepository", "Original bill not found, updating with provided bill")
                billsCollection.document(bill.id).set(bill).await()

                // Update the bill in the cache
                billCache[bill.id] = bill

                // Invalidate list caches to force refresh
                invalidateListCaches()

                // Reschedule notifications for the updated bill
                notificationScheduler?.cancelBillNotifications(bill.id)
                notificationScheduler?.scheduleBillNotifications(bill)
            }

            val queryTime = System.currentTimeMillis() - startTime
            android.util.Log.d("PerformanceMonitor", "Firestore updateBill operation took $queryTime ms")

            android.util.Log.d("BillRepository", "Bill updated successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error updating bill: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Invalidate all list caches to force refresh on next query
     */
    private fun invalidateListCaches() {
        allBillsCache.value = null
        personalBillsCache.value = null
        businessBillsCache.value = null
        upcomingBillsCache.value = null
    }

    /**
     * Delete a bill.
     */
    suspend fun deleteBill(billId: String): Result<Unit> {
        return try {
            val startTime = System.currentTimeMillis()
            android.util.Log.d("PerformanceMonitor", "Starting deleteBill operation")

            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            // Check if the bill is in the cache first
            val cachedBill = billCache[billId]
            if (cachedBill != null) {
                // Verify the bill belongs to the current user
                if (cachedBill.userId != userId) {
                    android.util.Log.e("BillRepository", "Unauthorized to delete bill: $billId")
                    return Result.failure(IllegalStateException("Unauthorized to delete this bill"))
                }
            } else {
                // If not in cache, check Firestore
                val bill = billsCollection.document(billId).get().await()
                    .toObject(Bill::class.java) ?: return Result.failure(IllegalStateException("Bill not found"))

                if (bill.userId != userId) {
                    android.util.Log.e("BillRepository", "Unauthorized to delete bill: $billId")
                    return Result.failure(IllegalStateException("Unauthorized to delete this bill"))
                }
            }

            // Delete from Firestore
            billsCollection.document(billId).delete().await()

            // Remove from cache
            billCache.remove(billId)

            // Cancel any scheduled notifications for this bill
            notificationScheduler?.cancelBillNotifications(billId)

            // Invalidate list caches to force refresh
            invalidateListCaches()

            val queryTime = System.currentTimeMillis() - startTime
            android.util.Log.d("PerformanceMonitor", "Firestore deleteBill operation took $queryTime ms")

            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error deleting bill: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Mark a bill as paid or unpaid.
     */
    suspend fun markBillPaid(billId: String, isPaid: Boolean): Result<Bill> {
        android.util.Log.d("BillRepository", "markBillPaid called with billId: $billId, isPaid: $isPaid")
        return try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            // First check if the bill exists and belongs to the current user
            val billDoc = billsCollection.document(billId).get().await()
            if (!billDoc.exists()) {
                android.util.Log.e("BillRepository", "Bill not found: $billId")
                return Result.failure(IllegalStateException("Bill not found"))
            }

            val bill = billDoc.toObject(Bill::class.java)
            if (bill?.userId != userId) {
                android.util.Log.e("BillRepository", "Unauthorized to update bill: $billId")
                return Result.failure(IllegalStateException("Unauthorized to update this bill"))
            }

            android.util.Log.d("BillRepository", "Current bill status: isPaid=${bill?.isPaid}, updating to: $isPaid")

            // Create a complete bill object with the updated status
            val updatedBill = bill!!.copy(
                isPaid = isPaid,
                paidDate = if (isPaid) Date() else null,
                updatedAt = com.google.firebase.Timestamp.now()
            )

            android.util.Log.d("BillRepository", "Updating bill to: $updatedBill")

            // Set the entire document to ensure all fields are updated correctly
            billsCollection.document(billId).set(updatedBill).await()

            // Double-check the update
            val updatedDoc = billsCollection.document(billId).get().await()
            val retrievedBill = updatedDoc.toObject(Bill::class.java)
            android.util.Log.d("BillRepository", "After update, bill is: $retrievedBill, isPaid: ${retrievedBill?.isPaid}")

            // If the update didn't take effect, try a more direct approach
            if (retrievedBill?.isPaid != isPaid) {
                android.util.Log.w("BillRepository", "Update didn't take effect, trying a more direct approach")

                // Use a direct update with a map
                // Make sure the field name matches exactly what's in Firestore
                val updates = mapOf(
                    "isPaid" to isPaid,
                    "paidDate" to if (isPaid) Date() else null,
                    "updatedAt" to com.google.firebase.Timestamp.now()
                )

                billsCollection.document(billId).update(updates).await()
                android.util.Log.d("BillRepository", "Direct update completed")

                // Get the bill again
                val finalDoc = billsCollection.document(billId).get().await()
                val finalBill = finalDoc.toObject(Bill::class.java)
                android.util.Log.d("BillRepository", "Final bill state: $finalBill, isPaid: ${finalBill?.isPaid}")

                if (finalBill != null) {
                    // Update the cache with the final bill
                    billCache[billId] = finalBill

                    // Handle notifications based on payment status
                    if (finalBill.isPaid) {
                        // Cancel notifications when bill is paid
                        notificationScheduler?.cancelBillNotifications(billId)
                    } else {
                        // Reschedule notifications when bill is marked unpaid
                        notificationScheduler?.scheduleBillNotifications(finalBill)
                    }

                    // Invalidate list caches to force refresh
                    invalidateListCaches()

                    return Result.success(finalBill)
                }
            } else if (retrievedBill != null) {
                // Update the cache with the retrieved bill
                billCache[billId] = retrievedBill

                // Handle notifications based on payment status
                if (retrievedBill.isPaid) {
                    // Cancel notifications when bill is paid
                    notificationScheduler?.cancelBillNotifications(billId)
                } else {
                    // Reschedule notifications when bill is marked unpaid
                    notificationScheduler?.scheduleBillNotifications(retrievedBill)
                }

                // Invalidate list caches to force refresh
                invalidateListCaches()
            }

            android.util.Log.d("BillRepository", "Bill updated successfully")
            Result.success(retrievedBill ?: updatedBill)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error updating bill: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Get all bills for the current user as a Flow.
     */
    fun getBillsFlow(category: BillCategory? = null): Flow<List<Bill>> = callbackFlow {
        val startTime = System.currentTimeMillis()
        android.util.Log.d("PerformanceMonitor", "Starting getBillsFlow query for category: $category")

        val userId = getCurrentUserId()
        if (userId.isEmpty()) {
            trySend(emptyList())
            close()
            return@callbackFlow
        }

        // Determine which cache to use based on category
        val cache = when (category) {
            BillCategory.PERSONAL -> personalBillsCache
            BillCategory.BUSINESS -> businessBillsCache
            null -> allBillsCache
        }

        // Get the last query timestamp for this category
        val lastQueryTime = when (category) {
            BillCategory.PERSONAL -> lastPersonalBillsQuery
            BillCategory.BUSINESS -> lastBusinessBillsQuery
            null -> lastAllBillsQuery
        }

        // Check if we have cached data and if the debounce period has passed
        val currentTime = System.currentTimeMillis()
        val cachedBills = cache.value

        if (cachedBills != null && (currentTime - lastQueryTime < queryDebounceTime)) {
            android.util.Log.d("PerformanceMonitor", "Using cached bills for category: $category (${cachedBills.size} bills)")
            trySend(cachedBills)

            // We still set up the listener for future updates, but we've already sent the cached data
        }

        // Update the last query time for this category
        when (category) {
            BillCategory.PERSONAL -> lastPersonalBillsQuery = currentTime
            BillCategory.BUSINESS -> lastBusinessBillsQuery = currentTime
            null -> lastAllBillsQuery = currentTime
        }

        var query: Query = billsCollection.whereEqualTo("userId", userId)

        // Apply category filter if provided
        if (category != null) {
            query = query.whereEqualTo("category", category)
        }

        // Use a single subscription and handle errors gracefully
        val subscription = try {
            // Try to use ordered query first
            query.orderBy("dueDate", Query.Direction.ASCENDING)
                .addSnapshotListener { snapshot, error ->
                    val queryTime = System.currentTimeMillis() - startTime
                    android.util.Log.d("PerformanceMonitor", "Firestore query completed in $queryTime ms")

                    if (error != null) {
                        android.util.Log.e("PerformanceMonitor", "Error in getBillsFlow: ${error.message}")
                        // Just send empty list on error
                        trySend(emptyList())
                        return@addSnapshotListener
                    }

                    val processingStartTime = System.currentTimeMillis()
                    val bills = snapshot?.documents?.mapNotNull { doc ->
                        val bill = doc.toObject(Bill::class.java)
                        // Update the individual bill cache
                        if (bill != null) {
                            billCache[bill.id] = bill
                        }
                        bill
                    } ?: emptyList()

                    // Update the list cache
                    cache.value = bills

                    val processingTime = System.currentTimeMillis() - processingStartTime
                    android.util.Log.d("PerformanceMonitor", "Processing ${bills.size} bills took $processingTime ms")

                    val totalTime = System.currentTimeMillis() - startTime
                    android.util.Log.d("PerformanceMonitor", "Total getBillsFlow operation took $totalTime ms")

                    trySend(bills)
                }
        } catch (e: Exception) {
            android.util.Log.e("PerformanceMonitor", "Exception in getBillsFlow, falling back to unordered query: ${e.message}")
            // Fallback to unordered query
            query.addSnapshotListener { snapshot, error ->
                val queryTime = System.currentTimeMillis() - startTime
                android.util.Log.d("PerformanceMonitor", "Fallback query completed in $queryTime ms")

                if (error != null) {
                    android.util.Log.e("PerformanceMonitor", "Error in fallback query: ${error.message}")
                    trySend(emptyList())
                    return@addSnapshotListener
                }

                val processingStartTime = System.currentTimeMillis()
                val bills = snapshot?.documents?.mapNotNull { doc ->
                    val bill = doc.toObject(Bill::class.java)
                    // Update the individual bill cache
                    if (bill != null) {
                        billCache[bill.id] = bill
                    }
                    bill
                }?.sortedBy { it.dueDate } ?: emptyList()

                // Update the list cache
                cache.value = bills

                val processingTime = System.currentTimeMillis() - processingStartTime
                android.util.Log.d("PerformanceMonitor", "Processing ${bills.size} bills in fallback took $processingTime ms")

                val totalTime = System.currentTimeMillis() - startTime
                android.util.Log.d("PerformanceMonitor", "Total fallback operation took $totalTime ms")

                trySend(bills)
            }
        }

        // Only one awaitClose for the entire flow
        awaitClose {
            subscription.remove()
        }
    }

    /**
     * Get a single bill by ID (nullable version for internal use).
     */
    suspend fun getBillById(billId: String): Bill? {
        return try {
            val result = getBillByIdResult(billId)
            result.getOrNull()
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error getting bill by ID: ${e.message}", e)
            null
        }
    }

    /**
     * Get a single bill by ID.
     */
    suspend fun getBillByIdResult(billId: String): Result<Bill> {
        android.util.Log.d("BillRepository", "getBillById called with billId: $billId")

        // Check if the bill is in the cache
        val cachedBill = billCache[billId]
        if (cachedBill != null) {
            android.util.Log.d("BillRepository", "Retrieved bill from cache: $cachedBill")
            return Result.success(cachedBill)
        }

        return try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            val startTime = System.currentTimeMillis()
            val doc = billsCollection.document(billId).get().await()
            val queryTime = System.currentTimeMillis() - startTime
            android.util.Log.d("PerformanceMonitor", "Firestore getBillById query took $queryTime ms")

            if (!doc.exists()) {
                android.util.Log.e("BillRepository", "Bill not found: $billId")
                return Result.failure(IllegalStateException("Bill not found"))
            }

            val bill = doc.toObject(Bill::class.java)
                ?: return Result.failure(IllegalStateException("Bill not found"))

            android.util.Log.d("BillRepository", "Retrieved bill from Firestore: $bill, isPaid: ${bill.isPaid}")

            // Verify the bill belongs to the current user
            if (bill.userId != userId) {
                android.util.Log.e("BillRepository", "Unauthorized to access bill: $billId")
                return Result.failure(IllegalStateException("Unauthorized to access this bill"))
            }

            // Add the bill to the cache
            billCache[billId] = bill

            Result.success(bill)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error getting bill: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Get all bills for the current user.
     */
    suspend fun getAllBills(): Result<List<Bill>> {
        return try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            val startTime = System.currentTimeMillis()
            android.util.Log.d("PerformanceMonitor", "Starting getAllBills query")

            val snapshot = billsCollection
                .whereEqualTo("userId", userId)
                .get()
                .await()

            val queryTime = System.currentTimeMillis() - startTime
            android.util.Log.d("PerformanceMonitor", "Firestore getAllBills query took $queryTime ms")

            val bills = snapshot.documents.mapNotNull { doc ->
                val bill = doc.toObject(Bill::class.java)
                // Update the individual bill cache
                if (bill != null) {
                    billCache[bill.id] = bill
                }
                bill
            }

            android.util.Log.d("BillRepository", "Retrieved ${bills.size} bills")
            Result.success(bills)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error getting all bills: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Get upcoming bills (due within the next 7 days).
     */
    fun getUpcomingBillsFlow(): Flow<List<Bill>> = callbackFlow {
        val startTime = System.currentTimeMillis()
        android.util.Log.d("PerformanceMonitor", "Starting getUpcomingBillsFlow query")

        val userId = getCurrentUserId()
        if (userId.isEmpty()) {
            trySend(emptyList())
            close()
            return@callbackFlow
        }

        // Check if we have cached data and if the debounce period has passed
        val currentTime = System.currentTimeMillis()
        val cachedBills = upcomingBillsCache.value

        if (cachedBills != null && (currentTime - lastUpcomingBillsQuery < queryDebounceTime)) {
            android.util.Log.d("PerformanceMonitor", "Using cached upcoming bills (${cachedBills.size} bills)")
            trySend(cachedBills)

            // We still set up the listener for future updates, but we've already sent the cached data
        }

        // Update the last query time
        lastUpcomingBillsQuery = currentTime

        val today = Date()
        val sevenDaysLater = Date(today.time + (7 * 24 * 60 * 60 * 1000))
        android.util.Log.d("PerformanceMonitor", "Date range: $today to $sevenDaysLater")

        // Create base query
        val baseQuery = billsCollection
            .whereEqualTo("userId", userId)
            .whereEqualTo("isPaid", false)

        // Use a single subscription and handle errors gracefully
        val subscription = try {
            // Try to use complex query first
            android.util.Log.d("PerformanceMonitor", "Attempting complex query for upcoming bills")
            baseQuery
                .whereGreaterThanOrEqualTo("dueDate", today)
                .whereLessThanOrEqualTo("dueDate", sevenDaysLater)
                .orderBy("dueDate", Query.Direction.ASCENDING)
                .addSnapshotListener { snapshot, error ->
                    val queryTime = System.currentTimeMillis() - startTime
                    android.util.Log.d("PerformanceMonitor", "Complex query completed in $queryTime ms")

                    if (error != null) {
                        android.util.Log.e("PerformanceMonitor", "Error in getUpcomingBillsFlow: ${error.message}")
                        // Just send empty list on error
                        trySend(emptyList())
                        return@addSnapshotListener
                    }

                    val processingStartTime = System.currentTimeMillis()
                    val bills = snapshot?.documents?.mapNotNull { doc ->
                        val bill = doc.toObject(Bill::class.java)
                        // Update the individual bill cache
                        if (bill != null) {
                            billCache[bill.id] = bill
                        }
                        bill
                    } ?: emptyList()

                    // Update the list cache
                    upcomingBillsCache.value = bills

                    val processingTime = System.currentTimeMillis() - processingStartTime
                    android.util.Log.d("PerformanceMonitor", "Processing ${bills.size} upcoming bills took $processingTime ms")

                    val totalTime = System.currentTimeMillis() - startTime
                    android.util.Log.d("PerformanceMonitor", "Total getUpcomingBillsFlow operation took $totalTime ms")

                    trySend(bills)
                }
        } catch (e: Exception) {
            // Fallback to simpler query
            android.util.Log.e("PerformanceMonitor", "Exception in complex query, falling back to simple query: ${e.message}")
            baseQuery.addSnapshotListener { snapshot, error ->
                val queryTime = System.currentTimeMillis() - startTime
                android.util.Log.d("PerformanceMonitor", "Simple query completed in $queryTime ms")

                if (error != null) {
                    android.util.Log.e("PerformanceMonitor", "Error in fallback query: ${error.message}")
                    trySend(emptyList())
                    return@addSnapshotListener
                }

                val processingStartTime = System.currentTimeMillis()

                // Filter and sort in memory instead of in the query
                val bills = snapshot?.documents?.mapNotNull { doc ->
                    val bill = doc.toObject(Bill::class.java)
                    // Update the individual bill cache
                    if (bill != null) {
                        billCache[bill.id] = bill
                    }
                    bill
                }?.filter { bill ->
                    bill.dueDate != null &&
                    bill.dueDate.time >= today.time &&
                    bill.dueDate.time <= sevenDaysLater.time
                }?.sortedBy { it.dueDate } ?: emptyList()

                // Update the list cache
                upcomingBillsCache.value = bills

                val processingTime = System.currentTimeMillis() - processingStartTime
                android.util.Log.d("PerformanceMonitor", "Processing ${bills.size} upcoming bills in fallback took $processingTime ms")

                val totalTime = System.currentTimeMillis() - startTime
                android.util.Log.d("PerformanceMonitor", "Total fallback operation took $totalTime ms")

                trySend(bills)
            }
        }

        // Only one awaitClose for the entire flow
        awaitClose {
            subscription.remove()
        }
    }

    /**
     * Update the FCM token for the current user.
     */
    suspend fun updateFcmToken(token: String): Result<Unit> {
        return try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                android.util.Log.e("BillRepository", "User not logged in")
                return Result.failure(IllegalStateException("User not logged in"))
            }

            // Store the token in the user's document
            val userDoc = firestore.collection("users").document(userId)

            // Update or create the user document with the FCM token
            userDoc.set(
                mapOf(
                    "fcmToken" to token,
                    "updatedAt" to com.google.firebase.Timestamp.now()
                ),
                com.google.firebase.firestore.SetOptions.merge()
            ).await()

            android.util.Log.d("BillRepository", "FCM token updated for user: $userId")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("BillRepository", "Error updating FCM token: ${e.message}", e)
            Result.failure(e)
        }
    }
}
