package com.example.billmate.repository

import com.example.billmate.model.Bill
import com.example.billmate.model.BillCategory
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Calendar
import java.util.Date

/**
 * Repository for statistics and data aggregation.
 */
class StatisticsRepository(
    private val billRepository: BillRepository = BillRepository()
) {
    /**
     * Get total amount spent by category.
     */
    fun getCategoryTotalsFlow(): Flow<Map<BillCategory, Double>> {
        return billRepository.getBillsFlow().map { bills ->
            bills.groupBy { it.category }
                .mapValues { entry -> entry.value.sumOf { it.amount } }
        }
    }
    
    /**
     * Get monthly spending data for the last 6 months.
     */
    fun getMonthlySpendingFlow(): Flow<Map<String, Double>> {
        return billRepository.getBillsFlow().map { bills ->
            // Get bills from the last 6 months
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.MONTH, -5) // Go back 5 months to show 6 months total
            val sixMonthsAgo = calendar.time
            
            val filteredBills = bills.filter { bill -> 
                bill.dueDate?.after(sixMonthsAgo) ?: false 
            }
            
            // Group by month
            filteredBills.groupBy { bill ->
                bill.dueDate?.let { dueDate ->
                    val cal = Calendar.getInstance()
                    cal.time = dueDate
                    val month = cal.get(Calendar.MONTH)
                    val year = cal.get(Calendar.YEAR)
                    "$year-${month + 1}" // +1 because Calendar months are 0-based
                } ?: "Unknown"
            }.mapValues { entry ->
                entry.value.sumOf { it.amount }
            }
        }
    }
    
    /**
     * Get paid vs unpaid bill counts.
     */
    fun getBillStatusCountsFlow(): Flow<Pair<Int, Int>> {
        return billRepository.getBillsFlow().map { bills ->
            val paidCount = bills.count { it.isPaid }
            val unpaidCount = bills.size - paidCount
            Pair(paidCount, unpaidCount)
        }
    }
    
    /**
     * Get upcoming bills due in the next 7 days.
     */
    fun getUpcomingBillsFlow(): Flow<List<Bill>> {
        return billRepository.getUpcomingBillsFlow()
    }
    
    /**
     * Get total amount of all bills.
     */
    fun getTotalAmountFlow(): Flow<Double> {
        return billRepository.getBillsFlow().map { bills ->
            bills.sumOf { it.amount }
        }
    }
    
    /**
     * Get total amount of unpaid bills.
     */
    fun getUnpaidAmountFlow(): Flow<Double> {
        return billRepository.getBillsFlow().map { bills ->
            bills.filter { !it.isPaid }.sumOf { it.amount }
        }
    }
    
    /**
     * Get average bill amount.
     */
    fun getAverageBillAmountFlow(): Flow<Double> {
        return billRepository.getBillsFlow().map { bills ->
            if (bills.isEmpty()) 0.0 else bills.sumOf { it.amount } / bills.size
        }
    }
}
