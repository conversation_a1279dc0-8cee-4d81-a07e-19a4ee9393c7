package com.example.billmate.repository

import android.content.Context
import android.util.Log
import com.example.billmate.model.Bill
import com.example.billmate.model.EmailAccount
import com.example.billmate.model.EmailBillExtraction
import com.example.billmate.model.EmailMessage
import com.example.billmate.service.EmailBillExtractor
import com.example.billmate.service.EmailService
import com.example.billmate.service.GmailService
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * Repository for email-related operations.
 */
class EmailRepository(private val context: Context) {
    private val TAG = "EmailRepository"

    private val emailService: GmailService = GmailService()
    private val billExtractor = EmailBillExtractor(context)
    private val billRepository = BillRepository()

    init {
        // Set the Gmail service in MainActivity for handling OAuth callbacks
        if (context is android.app.Activity) {
            (context as? com.example.billmate.MainActivity)?.setGmailService(emailService)
        }
    }

    private val _connectedAccount = MutableStateFlow<EmailAccount?>(null)
    val connectedAccount = _connectedAccount.asStateFlow()

    /**
     * Authorize the app to access the user's email.
     */
    fun authorize(): Flow<EmailService.AuthState> {
        return emailService.authorize(context)
    }

    /**
     * Connect to the email service with the given account.
     */
    suspend fun connect(account: EmailAccount): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val result = emailService.connect(account)

            if (result.isSuccess) {
                _connectedAccount.value = account
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "Error connecting to email service", e)
            Result.failure(e)
        }
    }

    /**
     * Disconnect from the email service.
     */
    suspend fun disconnect() = withContext(Dispatchers.IO) {
        try {
            emailService.disconnect()
            _connectedAccount.value = null
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error disconnecting from email service", e)
            Result.failure(e)
        }
    }

    /**
     * Check if the service is connected.
     */
    fun isConnected(): Boolean {
        return emailService.isConnected()
    }

    /**
     * Get emails that might contain bills.
     */
    suspend fun getBillEmails(maxResults: Int = 10): Result<List<EmailMessage>> = withContext(Dispatchers.IO) {
        try {
            emailService.getBillEmails(maxResults)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting bill emails", e)
            Result.failure(e)
        }
    }

    /**
     * Extract bill information from an email.
     */
    suspend fun extractBillInfo(email: EmailMessage): Result<EmailBillExtraction> = withContext(Dispatchers.IO) {
        try {
            billExtractor.extractBillInfo(email)
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting bill info", e)
            Result.failure(e)
        }
    }

    /**
     * Extract bill information from multiple emails.
     */
    suspend fun extractBillsFromEmails(emails: List<EmailMessage>): Result<List<EmailBillExtraction>> = withContext(Dispatchers.IO) {
        try {
            val extractions = mutableListOf<EmailBillExtraction>()

            for (email in emails) {
                val extractionResult = extractBillInfo(email)

                if (extractionResult.isSuccess) {
                    val extraction = extractionResult.getOrThrow()

                    // Only add extractions with sufficient confidence
                    if (extraction.confidence > 0.5f) {
                        extractions.add(extraction)
                    }
                }
            }

            Result.success(extractions)
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting bills from emails", e)
            Result.failure(e)
        }
    }

    /**
     * Save extracted bills to Firestore.
     */
    suspend fun saveExtractedBills(extractions: List<EmailBillExtraction>): Result<List<Bill>> = withContext(Dispatchers.IO) {
        try {
            val userId = FirebaseAuth.getInstance().currentUser?.uid
                ?: return@withContext Result.failure(IllegalStateException("User not logged in"))

            val bills = mutableListOf<Bill>()

            for (extraction in extractions) {
                // Convert extraction to bill
                val bill = billExtractor.convertToBill(extraction)

                // Create a copy with the user ID
                val billWithUserId = bill.copy(userId = userId)

                // Save bill to Firestore
                val saveResult = billRepository.addBill(billWithUserId)

                if (saveResult.isSuccess) {
                    // Get the bill ID from the result
                    val billId = saveResult.getOrThrow()
                    // Fetch the saved bill
                    val savedBillResult = billRepository.getBillByIdResult(billId)
                    if (savedBillResult.isSuccess) {
                        bills.add(savedBillResult.getOrThrow())
                    }
                }
            }

            Result.success(bills)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving extracted bills", e)
            Result.failure(e)
        }
    }

    /**
     * Scan emails for bills and save them.
     */
    suspend fun scanEmailsForBills(maxEmails: Int = 10): Result<List<Bill>> = withContext(Dispatchers.IO) {
        try {
            // Get emails that might contain bills
            val emailsResult = getBillEmails(maxEmails)

            if (emailsResult.isFailure) {
                return@withContext Result.failure(emailsResult.exceptionOrNull() ?: Exception("Failed to get emails"))
            }

            val emails = emailsResult.getOrThrow()

            // Extract bill information from emails
            val extractionsResult = extractBillsFromEmails(emails)

            if (extractionsResult.isFailure) {
                return@withContext Result.failure(extractionsResult.exceptionOrNull() ?: Exception("Failed to extract bills"))
            }

            val extractions = extractionsResult.getOrThrow()

            // Save extracted bills to Firestore
            saveExtractedBills(extractions)
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning emails for bills", e)
            Result.failure(e)
        }
    }
}
