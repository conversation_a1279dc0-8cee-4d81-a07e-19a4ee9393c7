<resources>
    <!-- App Name -->
    <string name="app_name">BillMate</string>

    <!-- Common -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="back">Back</string>
    <string name="next">Next</string>
    <string name="loading">Loading…</string>
    <string name="error">Error</string>
    <string name="success">Success</string>
    <string name="or">OR</string>
    <string name="google_logo">Google Logo</string>
    <string name="no_date">No date</string>
    <string name="google_sign_in_failed">Google sign-in failed</string>

    <!-- Authentication -->
    <string name="login">Login</string>
    <string name="register">Register</string>
    <string name="logout">Logout</string>
    <string name="email">Email</string>
    <string name="password">Password</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="name">Name</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="create_account">Create Account</string>
    <string name="already_have_account">Already have an account? Login</string>
    <string name="sign_in_with_google">Sign in with Google</string>
    <string name="login_failed">Login failed</string>
    <string name="registration_failed">Registration failed</string>
    <string name="passwords_do_not_match">Passwords do not match</string>
    <string name="password_too_short">Password must be at least 6 characters</string>
    <string name="please_fill_all_fields">Please fill in all fields</string>
    <string name="reset_password">Reset Password</string>
    <string name="reset_password_instructions">Enter your email address and we\'ll send you instructions to reset your password.</string>
    <string name="send_reset_email">Send Reset Email</string>
    <string name="password_reset_email_sent">Password Reset Email Sent</string>
    <string name="password_reset_email_sent_message">We\'ve sent an email to %1$s with instructions to reset your password.</string>
    <string name="back_to_login">Back to Login</string>

    <!-- Home Screen -->
    <string name="home">Home</string>
    <string name="upcoming_bills">%1$d Upcoming Bills</string>
    <string name="all_bills">All Bills</string>
    <string name="no_bills">No bills found</string>
    <string name="add_bill">Add Bill</string>
    <string name="scan_bill">Scan Bill</string>
    <string name="due_date">Due Date: %1$s</string>
    <string name="amount">Amount: %1$s</string>
    <string name="paid">Paid</string>
    <string name="unpaid">Unpaid</string>
    <string name="bills_overview">Bills Overview</string>
    <string name="total_due">Total Due: %1$s</string>
    <string name="view_all">View All</string>
    <string name="add_manually">Add Manually</string>
    <string name="from_email">From Email</string>
    <string name="ai_assistant_description">Ask me about your bills, and I\'ll help you manage them efficiently.</string>
    <string name="ask_billmate">Ask BillMate</string>
    <string name="chat">Chat</string>
    <string name="bills">Bills</string>

    <!-- Email Integration -->
    <string name="email_setup">Email Setup</string>
    <string name="email_bills">Email Bills</string>
    <string name="email_setup_title">Connect Your Email</string>
    <string name="email_setup_description">Connect your email account to automatically extract bills from your inbox.</string>
    <string name="connect_gmail">Connect Gmail Account</string>
    <string name="authorizing">Authorizing…</string>
    <string name="email_setup_success">Email Connected Successfully</string>
    <string name="email_setup_success_description">Your email account has been connected. Now you can extract bills from your emails.</string>
    <string name="continue_to_bills">Continue to Bills</string>
    <string name="email_setup_failed">Connection Failed</string>
    <string name="email_setup_failed_description">Failed to connect to your email account. Please try again.</string>
    <string name="try_again">Try Again</string>
    <string name="found_emails">Found %1$d Emails</string>
    <string name="extract_bills">Extract Bills</string>
    <string name="extract_bills_description">Click the button below to extract bill information from your emails.</string>
    <string name="extracted_bills">Extracted %1$d Bills</string>
    <string name="review_and_save_bills">Review the extracted bills and save them to your account.</string>
    <string name="save_selected_bills">Save Selected Bills (%1$d)</string>
    <string name="bills_saved_successfully">%1$d Bills Saved Successfully</string>
    <string name="bills_saved_description">The selected bills have been saved to your account.</string>
    <string name="done">Done</string>
    <string name="select">Select</string>
    <string name="deselect">Deselect</string>
    <string name="confidence">Confidence: %1$d%%</string>
    <string name="refresh">Refresh</string>
    <string name="no_emails_found">No Emails Found</string>
    <string name="no_emails_found_description">We couldn\'t find any emails that might contain bills. Try refreshing or check your email account settings.</string>
    <string name="unknown_bill">Unknown Bill</string>

    <!-- Bill Details -->
    <string name="bill_details">Bill Details</string>
    <string name="bill_name">Bill Name</string>
    <string name="bill_amount">Amount</string>
    <string name="bill_due_date">Due Date</string>
    <string name="bill_category">Category</string>
    <string name="bill_description">Description</string>
    <string name="bill_recurring">Recurring</string>
    <string name="bill_paid">Paid</string>
    <string name="bill_payment_date">Payment Date</string>
    <string name="bill_image">Bill Image</string>
    <string name="bill_delete_confirmation">Are you sure you want to delete this bill?</string>
    <string name="bill_deleted">Bill deleted</string>
    <string name="bill_saved">Bill saved</string>
    <string name="title">Title</string>
    <string name="edit_bill">Edit Bill</string>
    <string name="add_bill_title">Add Bill</string>
    <string name="delete_bill">Delete Bill</string>
    <string name="saving">Saving...</string>
    <string name="title_required">Title is required</string>
    <string name="valid_amount">Please enter a valid amount</string>
    <string name="failed_to_save">Failed to save: %1$s</string>
    <string name="failed_to_load">Failed to load bill: %1$s</string>
    <string name="bill_not_found">Bill not found</string>
    <string name="failed_to_delete">Failed to delete: %1$s</string>
    <string name="select_date">Select Date</string>
    <string name="status">Status</string>
    <string name="amount_with_currency">€%1$s</string>
    <string name="mark_as_paid">Mark as Paid</string>
    <string name="mark_as_unpaid">Mark as Unpaid</string>
    <!-- Categories -->
    <string name="category_utilities">Utilities</string>
    <string name="category_rent">Rent</string>
    <string name="category_mortgage">Mortgage</string>
    <string name="category_insurance">Insurance</string>
    <string name="category_phone">Phone</string>
    <string name="category_internet">Internet</string>
    <string name="category_streaming">Streaming</string>
    <string name="category_credit_card">Credit Card</string>
    <string name="category_loan">Loan</string>
    <string name="category_other">Other</string>
    <string name="category_personal">Personal</string>
    <string name="category_business">Business</string>

    <!-- Scan Bill -->
    <string name="scan_bill_instructions">Position the bill in the frame and take a photo</string>
    <string name="take_photo">Take Photo</string>
    <string name="retake_photo">Retake Photo</string>
    <string name="processing_image">Processing image…</string>
    <string name="scan_successful">Scan successful</string>
    <string name="scan_failed">Scan failed</string>
    <string name="camera_permission_required">Camera permission is required to scan bills</string>

    <!-- QR Code Scanning -->
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="scan_text">Scan Text</string>
    <string name="qr_code_detected">QR Code Detected!</string>
    <string name="point_camera_at_qr_code">Point camera at QR code</string>
    <string name="qr_code_information">QR Code Information</string>
    <string name="use_data">Use Data</string>
    <string name="qr_code_type">Type</string>
    <string name="qr_code_title">Title</string>
    <string name="qr_code_amount">Amount</string>
    <string name="qr_code_due_date">Due Date</string>
    <string name="qr_code_account">Account</string>
    <string name="qr_code_reference">Reference</string>
    <string name="qr_code_payment_url">Payment URL</string>
    <string name="camera_permission_required_qr">Camera permission is required to scan QR codes</string>
    <string name="qr_code_scan_title">Scan QR Code</string>
    <string name="bill_text_scan_title">Scan Bill Text</string>
    <string name="extracted_information">Extracted Information</string>

    <!-- Document Scanner -->
    <string name="scan_document">Scan Document</string>
    <string name="document_scan_title">Professional Scan</string>
    <string name="document_scanner_description">High-quality document scanning with automatic enhancement</string>
    <string name="document_scanned_successfully">Document scanned successfully</string>
    <string name="document_scan_failed">Document scan failed</string>
    <string name="document_scan_cancelled">Document scan cancelled</string>
    <string name="processing_document">Processing document...</string>
    <string name="document_scanner_error">Document scanner error: %1$s</string>
    <string name="google_play_services_required">Google Play Services required for document scanning</string>

    <!-- Gallery Scanner -->
    <string name="scan_from_picture">Scan from Picture</string>
    <string name="gallery_scan_title">Scan from Gallery</string>
    <string name="gallery_scanner_description">Select a bill image from your gallery to extract information</string>
    <string name="select_image">Select Image</string>
    <string name="image_selected_successfully">Image selected successfully</string>
    <string name="image_processing_failed">Failed to process image</string>
    <string name="no_image_selected">No image selected</string>
    <string name="gallery_permission_required">Storage permission required to access gallery</string>
    <string name="processing_image_from_gallery">Processing image from gallery...</string>
    <string name="select_bill_image">Select Bill Image</string>

    <!-- Statistics -->
    <string name="statistics">Statistics</string>
    <string name="monthly_spending">Monthly Spending</string>
    <string name="spending_by_category">Spending by Category</string>
    <string name="paid_vs_unpaid">Paid vs Unpaid</string>
    <string name="no_data_available">No data available</string>
    <string name="total_bills">Total Bills: %1$d</string>
    <string name="total_amount">Total Amount: %1$s</string>
    <string name="paid_amount">Paid Amount: %1$s</string>
    <string name="unpaid_amount">Unpaid Amount: %1$s</string>
    <string name="loading_statistics">Loading statistics…</string>
    <string name="no_bills_found">No bills found</string>
    <string name="add_bills_for_statistics">Add some bills to see statistics</string>
    <string name="total">Total</string>
    <string name="average">Average</string>
    <string name="bills_count">Bills</string>
    <string name="paid_unpaid_count">%1$d paid / %2$d unpaid</string>
    <string name="category_distribution">Category Distribution</string>
    <string name="bill_status">Bill Status</string>
    <string name="monthly">Monthly</string>
    <string name="overall">Overall</string>

    <!-- Profile/Settings -->
    <string name="profile">Profile</string>
    <string name="settings">Settings</string>
    <string name="language">Language</string>
    <string name="theme">Theme</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="light_mode">Light Mode</string>
    <string name="system_default">System Default</string>
    <string name="dynamic_colors">Dynamic Colors</string>
    <string name="notifications">Notifications</string>
    <string name="currency">Currency</string>
    <string name="about">About</string>
    <string name="version">Version</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="account_settings">Account Settings</string>
    <string name="change_password">Change Password</string>
    <string name="delete_account">Delete Account</string>
    <string name="delete_account_confirmation">Are you sure you want to delete your account? This action cannot be undone.</string>
    <string name="account_deleted">Account deleted</string>
    <string name="profile_picture">Profile Picture</string>
    <string name="edit_profile">Edit Profile</string>
    <string name="account">Account</string>
    <string name="language_test">Language Test</string>
    <string name="current_language">Current language: %1$s</string>
    <string name="login_string_test">Login string: %1$s</string>
    <string name="home_string_test">Home string: %1$s</string>
    <string name="profile_string_test">Profile string: %1$s</string>
    <string name="loading_profile">Loading profile…</string>

    <!-- Language Selection -->
    <string name="select_language">Select Language</string>
    <string name="language_english">English</string>
    <string name="language_slovenian">Slovenščina (Slovenian)</string>
    <string name="language_changed">Language changed</string>
    <string name="restart_app">Please restart the app for the changes to take effect.</string>




    <!-- Error Messages -->
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_unknown">An unknown error occurred.</string>
    <string name="error_authentication">Authentication error. Please try again.</string>
    <string name="error_permission">Permission denied. Please grant the required permissions.</string>
    <string name="error_camera">Camera error. Please try again.</string>
    <string name="error_storage">Storage error. Please check your device storage.</string>
    <string name="error_invalid_input">Invalid input. Please check your entries.</string>

    <!-- Notifications -->
    <string name="notification_settings">Notification Settings</string>
    <string name="enable_notifications">Enable Notifications</string>
    <string name="due_date_notifications">Due Date Notifications</string>
    <string name="upcoming_bill_notifications">Upcoming Bill Notifications</string>
    <string name="days_before_due_date">Days Before Due Date: %1$d</string>
    <string name="notification_time">Notification Time</string>
    <string name="select_notification_time">Select Notification Time</string>
    <string name="notification_time_set">Notification time set to %1$s</string>
    <string name="test_notification">Test Notification</string>
    <string name="test_notification_sent">Test notification sent</string>
    <string name="notification_permission_required">Notification permission is required for notifications to work</string>
    <string name="open_settings">Open Settings</string>
    <string name="notification_channel_bills_due">Bills Due</string>
    <string name="notification_channel_bills_due_description">Notifications for bills that are due today</string>
    <string name="notification_channel_bills_upcoming">Upcoming Bills</string>
    <string name="notification_channel_bills_upcoming_description">Notifications for upcoming bills</string>
    <string name="notification_channel_bills_overdue">Overdue Bills</string>
    <string name="notification_channel_bills_overdue_description">Notifications for bills that are past their due date</string>
    <string name="notification_bill_due_title">Bill Due Today</string>
    <string name="notification_bill_due_text">Your bill %1$s for %2$s is due today</string>
    <string name="notification_bill_upcoming_title">Upcoming Bill</string>
    <string name="notification_bill_upcoming_text">Your bill %1$s for %2$s is due in %3$d days</string>
    <string name="notification_bill_overdue_title">Overdue Bill</string>
    <string name="notification_bill_overdue_text">Your bill %1$s for %2$s is overdue by %3$d days</string>
    <string name="default_notification_channel_name">Default Channel</string>

    <!-- Duplicate Detection -->
    <string name="duplicate_warning_title">Possible Duplicate Bill</string>
    <string name="duplicate_warning_message">We found similar bills that might be duplicates. Please review them before adding:</string>
    <string name="add_anyway">Add Anyway</string>
    <string name="and_more_duplicates">...and %1$d more similar bills</string>
    <string name="match_reasons">Why this might be a duplicate:</string>

    <!-- Filtering and Sorting -->
    <string name="filter">Filter</string>
    <string name="sort">Sort</string>
    <string name="paid_bills">Paid Bills</string>
    <string name="unpaid_bills">Unpaid Bills</string>
    <string name="sort_date_newest">Date (Newest First)</string>
    <string name="sort_date_oldest">Date (Oldest First)</string>
    <string name="sort_amount_high">Amount (High to Low)</string>
    <string name="sort_amount_low">Amount (Low to High)</string>
    <string name="sort_status">Status (Paid First)</string>
    <string name="sort_title">Title (A-Z)</string>
    <string name="bills_count_format">%1$d bills</string>
    <string name="no_bills_match_filter">No bills match the current filter</string>
    <string name="try_different_filter">Try a different filter or add more bills</string>

    <!-- Short versions for chips -->
    <string name="sort_date_short">Date</string>
    <string name="sort_amount_short">Amount</string>
    <string name="sort_title_short">Title</string>

    <!-- Status toggle messages -->
    <string name="marked_as_paid">Bill marked as paid</string>
    <string name="marked_as_unpaid">Bill marked as unpaid</string>
    <string name="failed_to_update">Failed to update: %1$s</string>

    <!-- Enhanced Notification Strings -->
    <string name="notification_channel_due_reminders">Opomniki za zapadlost</string>
    <string name="notification_channel_due_reminders_desc">Opomniki za račune, ki zapadejo v prihodnjih dneh</string>
    <string name="notification_channel_overdue_alerts">Opozorila za zapadle račune</string>
    <string name="notification_channel_overdue_alerts_desc">Opozorila za račune, ki so že zapadli</string>
    <string name="notification_channel_summaries">Povzetki računov</string>
    <string name="notification_channel_summaries_desc">Tedenski in mesečni povzetki računov</string>

    <string name="notification_due_today_title">Račun zapade danes</string>
    <string name="notification_due_tomorrow_title">Račun zapade jutri</string>
    <string name="notification_due_in_days_title">Račun zapade čez %d dni</string>
    <string name="notification_overdue_title">Zapadel račun</string>

    <string name="notification_bill_content">%1$s - %2$s</string>
    <string name="notification_overdue_content">%1$s je zapadel pred %2$d dnevi - %3$s</string>

    <string name="notification_action_mark_paid">Označi kot plačano</string>
    <string name="notification_action_view_details">Poglej podrobnosti</string>

    <string name="notification_marked_paid_success">Račun označen kot plačan</string>
    <string name="notification_marked_paid_error">Napaka pri označevanju računa</string>

    <string name="notification_permission_title">Omogoči obvestila</string>
    <string name="notification_permission_message">BillMate potrebuje dovoljenje za obvestila, da vas lahko opomni na zapadle račune.</string>
    <string name="notification_permission_grant">Omogoči</string>
    <string name="notification_permission_deny">Ne zdaj</string>

    <!-- AI Assistant -->
    <string name="ai_insights">AI Insights</string>
    <string name="ai_analyzing">AI analyzing...</string>
    <string name="ai_suggestion">AI suggestion</string>
    <string name="missing_bill">Missing bill</string>
    <string name="spending_alert">Spending alert</string>
    <string name="ai_add_bill">Add bill</string>
    <string name="ai_view_details">View details</string>
    <string name="ai_dismiss">Dismiss</string>

    <!-- Onboarding Strings -->
    <string name="welcome_to_billmate">Welcome to BillMate</string>
    <string name="welcome_subtitle">Your smart bill management companion that makes tracking and paying bills effortless</string>
    <string name="get_started">Get Started</string>
    <string name="skip_onboarding">Skip Setup</string>

    <!-- Feature Highlights -->
    <string name="feature_smart_scanning">Smart Scanning</string>
    <string name="feature_smart_scanning_desc">Scan bills with your camera or from photos</string>
    <string name="feature_ai_insights">AI Insights</string>
    <string name="feature_ai_insights_desc">Get intelligent suggestions and spending analysis</string>
    <string name="feature_notifications">Smart Reminders</string>
    <string name="feature_notifications_desc">Never miss a payment with timely notifications</string>

    <!-- Permissions -->
    <string name="permissions_title">App Permissions</string>
    <string name="permissions_subtitle">BillMate needs these permissions to work properly</string>
    <string name="camera_permission">Camera Access</string>
    <string name="camera_permission_desc">Scan bills and QR codes</string>
    <string name="storage_permission">Photo Access</string>
    <string name="storage_permission_desc">Scan bills from your photo gallery</string>
    <string name="notification_permission">Notifications</string>
    <string name="notification_permission_desc">Remind you about upcoming bills</string>
    <string name="skip_permissions">Skip for Now</string>
    <string name="granted">Granted</string>
    <string name="grant">Grant</string>

    <!-- Feature Tour -->
    <string name="discover_features">Discover Features</string>
    <string name="discover_features_subtitle">Learn what BillMate can do for you</string>
    <string name="feature_scanning_title">Smart Bill Scanning</string>
    <string name="feature_scanning_description">Capture bill information instantly</string>
    <string name="feature_scanning_details">Use your camera to scan text from bills or take photos to extract payment details automatically</string>
    <string name="feature_qr_title">QR Code Support</string>
    <string name="feature_qr_description">Scan payment QR codes</string>
    <string name="feature_qr_details">Quickly scan UPNQR and other payment QR codes to automatically fill in bill information</string>
    <string name="feature_ai_title">AI-Powered Insights</string>
    <string name="feature_ai_description">Smart spending analysis</string>
    <string name="feature_ai_details">Get personalized insights about your spending patterns and suggestions for better bill management</string>
    <string name="feature_reminders_title">Smart Reminders</string>
    <string name="feature_reminders_description">Never miss a payment</string>
    <string name="feature_reminders_details">Receive timely notifications before bills are due, with customizable reminder settings</string>
    <string name="skip">Skip</string>

    <!-- Preferences Setup -->
    <string name="setup_preferences">Setup Preferences</string>
    <string name="setup_preferences_subtitle">Customize BillMate to your liking</string>
    <string name="dark_mode_desc">Use dark theme for better viewing in low light</string>
    <string name="dynamic_colors_desc">Use colors from your wallpaper (Android 12+)</string>
    <string name="change_in_settings">Change in settings</string>
    <string name="notifications_setup_desc">Configure bill reminders in settings later</string>
    <string name="skip_setup">Skip Setup</string>

    <!-- First Bill Guide -->
    <string name="add_first_bill">Add Your First Bill</string>
    <string name="add_first_bill_subtitle">Choose how you\'d like to add your first bill to get started</string>
    <string name="add_manually_desc">Enter bill details manually</string>
    <string name="scan_text_desc">Use camera to scan bill text</string>
    <string name="scan_qr_desc">Scan QR codes from bills</string>
    <string name="scan_document_desc">Use document scanner for better quality</string>
    <string name="scan_from_picture_desc">Select a photo from your gallery</string>
    <string name="finish_setup">Finish Setup</string>
    <string name="add_bills_later">You can always add bills later from the home screen</string>

    <string name="continue_text">Continue</string>

    <!-- Statistics Screen -->
    <string name="all_time">All Time</string>
    <string name="yearly">Yearly</string>
    <string name="yearly_spending">Yearly Spending</string>
    <string name="summary">Summary</string>
</resources>