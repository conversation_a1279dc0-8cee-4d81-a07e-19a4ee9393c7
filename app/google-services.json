{"project_info": {"project_number": "635502146020", "project_id": "billmate-43ae3", "storage_bucket": "billmate-43ae3.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:635502146020:android:b3ab801eef18eeec5f57f1", "android_client_info": {"package_name": "com.example.billmate"}}, "oauth_client": [{"client_id": "635502146020-09osbs8sg138m65v6aaqs67gnik73prs.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "si.billmate.app", "certificate_hash": "1f0d8bf9f80c30505d1f64416148e9c691c3d71f"}}, {"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCNO_mts3x4p_1Klohf_C9MmFl9GTdi-CA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:635502146020:android:b3ab801eef18eeec5f57f1", "android_client_info": {"package_name": "si.billmate.app.debug"}}, "oauth_client": [{"client_id": "635502146020-09osbs8sg138m65v6aaqs67gnik73prs.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "si.billmate.app.debug", "certificate_hash": "1f0d8bf9f80c30505d1f64416148e9c691c3d71f"}}, {"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCNO_mts3x4p_1Klohf_C9MmFl9GTdi-CA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:635502146020:android:a1b2c3d4e5f6g7h8", "android_client_info": {"package_name": "si.billmate.app"}}, "oauth_client": [{"client_id": "635502146020-09osbs8sg138m65v6aaqs67gnik73prs.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "si.billmate.app", "certificate_hash": "1f0d8bf9f80c30505d1f64416148e9c691c3d71f"}}, {"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCNO_mts3x4p_1Klohf_C9MmFl9GTdi-CA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:635502146020:android:b2c3d4e5f6g7h8i9", "android_client_info": {"package_name": "si.billmate.app.debug"}}, "oauth_client": [{"client_id": "635502146020-09osbs8sg138m65v6aaqs67gnik73prs.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "si.billmate.app.debug", "certificate_hash": "1f0d8bf9f80c30505d1f64416148e9c691c3d71f"}}, {"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCNO_mts3x4p_1Klohf_C9MmFl9GTdi-CA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "635502146020-7t7i0l2e3cka3qf1maq0a7niob9i0vc2.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}