# 🔐 BillMate Keystore Information

## ⚠️ CRITICAL - KEEP THIS SECURE ⚠️

This file contains sensitive information about the app signing keystore. 
**NEVER share this publicly or commit to version control.**

## 🔑 Keystore Details

### File Location
```
keystore/billmate-release.jks
```

### Credentials
- **Store Password**: `billmate123`
- **Key Alias**: `billmate`
- **Key Password**: `billmate123`

### Certificate Information
- **Algorithm**: RSA
- **Key Size**: 2048 bits
- **Validity**: 10,000 days (~27 years)
- **Subject**: CN=BillMate, OU=Development, O=BillMate, L=Ljubljana, ST=Slovenia, C=SI

## 🛡️ Security Guidelines

### ✅ DO:
- **Backup the keystore** to multiple secure locations
- **Store passwords** in a secure password manager
- **Limit access** to authorized personnel only
- **Use environment variables** for CI/CD builds
- **Keep keystore offline** when not needed

### ❌ DON'T:
- **Never commit** keystore to version control
- **Never share** passwords in plain text
- **Never email** keystore files
- **Never store** in cloud storage without encryption
- **Never lose** the keystore (app updates impossible)

## 🔄 For Future Releases

### Environment Variables (Recommended)
```bash
export BILLMATE_STORE_PASSWORD="your_secure_password"
export BILLMATE_KEY_ALIAS="billmate"
export BILLMATE_KEY_PASSWORD="your_secure_password"
```

### Gradle Properties (Alternative)
```properties
# gradle.properties (local, not in VCS)
BILLMATE_STORE_PASSWORD=your_secure_password
BILLMATE_KEY_ALIAS=billmate
BILLMATE_KEY_PASSWORD=your_secure_password
```

## 📱 Play Store Upload Key

This keystore is used for:
- **Signing release builds**
- **Play Store app updates**
- **App integrity verification**
- **Google Play App Signing** (if enabled)

## 🆘 Emergency Procedures

### If Keystore is Lost:
1. **Contact Google Play Support** immediately
2. **Provide app details** and ownership proof
3. **May require** new app listing (last resort)
4. **Users will need** to uninstall/reinstall

### If Compromised:
1. **Generate new keystore** immediately
2. **Update Play Console** with new signing key
3. **Enable Play App Signing** for future protection
4. **Audit access logs** for security breaches

## 📞 Support Contact
For keystore-related issues, contact the development team immediately.

**Remember: The keystore is the most critical asset for app distribution!**
