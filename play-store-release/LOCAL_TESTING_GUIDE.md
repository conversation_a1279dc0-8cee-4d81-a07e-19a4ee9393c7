# 📱 Local Testing Guide - BillMate v1.3

## 🎯 **Test the Latest Release Locally**

### **Debug APK Ready**: `app-debug.apk` (147 MB)
- **Package**: `si.billmate.app.debug`
- **Version**: 1.3 (versionCode: 4)
- **Signed**: Debug keystore (for local testing)
- **Logs**: Full debug logging enabled

## 📲 **Installation Methods**

### **Method 1: ADB Install (Recommended)**
```bash
# Connect your Android device with USB debugging enabled
adb install play-store-release/app-debug.apk

# If you have an existing version installed:
adb install -r play-store-release/app-debug.apk
```

### **Method 2: Direct Install**
1. Copy `app-debug.apk` to your Android device
2. Enable "Install from unknown sources" in Settings
3. Tap the APK file to install

### **Method 3: Android Studio**
```bash
# From the project directory
./gradlew installDebug
```

## 🔍 **How to View Logs**

### **Option 1: ADB Logcat (Best for detailed logs)**
```bash
# Clear existing logs and start fresh
adb logcat -c

# Filter for BillMate logs only
adb logcat | grep -E "(BillMate|GoogleSignInHelper|FirebaseAuth|GoogleSignIn)"

# Or filter by specific tags
adb logcat -s GoogleSignInHelper:D FirebaseAuth:D MainActivity:D
```

### **Option 2: Android Studio Logcat**
1. Open Android Studio
2. Connect your device
3. Go to **View** → **Tool Windows** → **Logcat**
4. Filter by package: `si.billmate.app.debug`

### **Option 3: Device Logs (if you have root)**
```bash
# On device terminal
logcat | grep -i google
```

## 🧪 **Google Sign-In Testing Steps**

### **Step 1: Launch App**
1. Install the debug APK
2. Open BillMate app
3. You should see the onboarding flow (first time)

### **Step 2: Navigate to Login**
1. Complete onboarding or skip
2. Go to Profile/Settings
3. Look for "Sign in with Google" option

### **Step 3: Test Google Sign-In**
1. Tap "Sign in with Google"
2. **Watch the logs** for these messages:

#### **Expected Success Logs:**
```
GoogleSignInHelper: Initializing Google Sign-In with Firebase web client ID
GoogleSignInHelper: Web client ID: ************-7t...
GoogleSignInHelper: Google Sign-In options built successfully with ID token request
GoogleSignInHelper: Google Sign-In successful, account: <EMAIL>
GoogleSignInHelper: ID token present: true
GoogleSignInHelper: Auth credential created successfully
FirebaseAuth: signInWithCredential:success
```

#### **Potential Error Logs:**
```
GoogleSignInHelper: Google sign-in failed: [ERROR_CODE] - [ERROR_MESSAGE]
GoogleSignInHelper: No ID token found, using email for authentication
FirebaseAuth: signInWithCredential:failure
```

## 🔧 **Common Error Codes & Solutions**

### **Error Code 10: DEVELOPER_ERROR**
- **Cause**: Wrong SHA-1 fingerprint or package name mismatch
- **Solution**: Check Firebase console configuration

### **Error Code 12501: CANCELLED**
- **Cause**: User cancelled the sign-in
- **Solution**: Normal behavior, try again

### **Error Code 7: NETWORK_ERROR**
- **Cause**: No internet connection
- **Solution**: Check device connectivity

### **Error Code 8: INTERNAL_ERROR**
- **Cause**: Google Play Services issue
- **Solution**: Update Google Play Services

## 📊 **What to Test**

### **✅ Core Functionality**
1. **App Launch**: Should start without crashes
2. **Onboarding**: Should show welcome screens
3. **Navigation**: Bottom nav should work
4. **Google Sign-In**: Should open account picker
5. **Authentication**: Should successfully sign in
6. **Firebase**: Should sync data after sign-in

### **✅ New Features (v1.3)**
1. **Package Name**: Should be `si.billmate.app.debug`
2. **Statistics Tabs**: Should have sticky tabs with icons
3. **Bill List**: Should have tab icons
4. **Filtering**: Should work properly
5. **UI Improvements**: Should look modern and polished

### **✅ Google Sign-In Flow**
1. **Button Tap**: Should open Google account picker
2. **Account Selection**: Should allow choosing account
3. **Permission Grant**: Should request necessary permissions
4. **ID Token**: Should receive ID token (check logs)
5. **Firebase Auth**: Should authenticate with Firebase
6. **Profile Update**: Should show signed-in state

## 📝 **Log Collection Commands**

### **Capture Full Session**
```bash
# Start logging before testing
adb logcat -c
adb logcat > billmate_test_logs.txt

# In another terminal, test the app
# Then stop logging with Ctrl+C
```

### **Filter Important Logs**
```bash
# Google Sign-In specific
adb logcat | grep -E "(GoogleSignInHelper|GoogleSignIn|FirebaseAuth)"

# All BillMate logs
adb logcat | grep -i billmate

# Error logs only
adb logcat *:E
```

## 🎯 **Success Criteria**

### **✅ Google Sign-In Working If:**
1. **Account picker opens** when tapping sign-in button
2. **Logs show ID token received** (`ID token present: true`)
3. **Firebase authentication succeeds** (`signInWithCredential:success`)
4. **App shows signed-in state** (profile shows user info)
5. **No error codes** in GoogleSignInHelper logs

### **❌ Issues to Report:**
1. App crashes on launch
2. Google Sign-In button does nothing
3. Account picker doesn't open
4. Sign-in fails with error codes
5. No ID token in logs
6. Firebase authentication fails

## 📞 **Debugging Support**

If you encounter issues:

1. **Collect logs** using commands above
2. **Note the exact error** messages and codes
3. **Check device info**: Android version, Google Play Services version
4. **Test network**: Ensure internet connectivity
5. **Try different account**: Test with multiple Google accounts

## 🚀 **Ready for Testing!**

The debug APK is ready for comprehensive testing. Focus on:
- **Google Sign-In functionality**
- **Log collection for debugging**
- **Overall app stability**
- **New UI improvements**

**Happy testing! 📱✨**
