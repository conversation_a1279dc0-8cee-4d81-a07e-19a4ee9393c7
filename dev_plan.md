# 📱 Development Plan: Android App for Bill and Obligation Tracking

## 🎯 Project Overview
An Android application that enables users to track personal and business bills and obligations, allowing input via photo (using Google ML Kit OCR), storing extracted data, and sending notifications for due dates. Key features include a personal/business separation, automatic text recognition from images, reminders, and analytics/reporting.

Firebase will be used from the start to handle:
- Authentication
- Database (Firestore)
- Notifications (FCM)
- Analytics
- Optional storage (for storing original images)

---

## 🚀 Development Phases

### ✅ **Phase 1: MVP (Core Features)**
**Goal:** Deliver a minimal yet functional version with the core workflow.

**Features:**
- User authentication (Firebase Auth: email/password, Google login)
- Image capture → text extraction (Google ML Kit Text Recognition)
- Manual correction of OCR results
- Save extracted data (Firestore Cloud Database)
- Categorization: Personal / Business
- View list of obligations
- Mark obligation as “Paid” or “Unpaid”
- Due date notifications (Firebase Cloud Messaging / local notifications)
- Multi language support (English, Slovenian)

---

### ✅ **Phase 2: UI/UX Improvements**
**Goal:** Improve user experience and usability.

**Features:**
- Dark/Light theme toggle
- Improved filtering and search
- Icons/visual indicators for paid/unpaid status
- Sorting options (by date, amount, etc.)
- Confirmation dialogs & friendly error messages

---

### ✅ **Phase 3: Analytics & Reporting**
**Goal:** Add insights and reporting tools.

**Features:**
- Monthly spending reports (total, category-based)
- Graphical visualization (e.g., pie chart, bar chart)
- Export reports (PDF/CSV)
- Integration with Firebase Analytics for user behavior tracking

---

### ✅ **Phase 4: Additional Features & Integrations**
**Goal:** Extend functionality beyond core features.

**Optional Features:**
- Integration with Google Calendar (add due dates as events)
- Custom categories (user-defined tags)
- Support for QR code scanning (where available on invoices)
- Backup original receipt images in Firebase Storage
- Multi-user support (family/small business accounts)
- Web portal (future)

---

## 🏗️ Technical Stack

| Layer             | Technology                               |
|------------------|------------------------------------------|
| Frontend          | Android (Kotlin + Jetpack Compose)       |
| Backend           | Firebase (Firestore, Auth, FCM, Storage) |
| OCR               | Google ML Kit Text Recognition           |
| Notifications     | Firebase Cloud Messaging (or local)      |
| Analytics         | Firebase Analytics                       |
| Reports/Charts    | MPAndroidChart (or similar library)      |

---

## 📅 Estimated Timeline

| Phase          | Duration |
|----------------|----------|
| Phase 1 (MVP)   | 4-6 weeks |
| Phase 2 (UI/UX) | 2-3 weeks |
| Phase 3 (Analytics) | 2-3 weeks |
| Phase 4 (Extras) | 4+ weeks |

*Timeline depends on developer availability and team size.*

---

## 📝 Next Steps

1. Define data model schema (Firestore collections & documents)
2. Create wireframes/prototype
3. Set up Firebase project (Auth, Firestore, FCM, Analytics)
4. Start Android project with initial architecture
5. Implement authentication flow
6. Implement image capture & OCR pipeline
7. Implement Firestore data storage & retrieval
8. Build basic obligation list UI
9. Add notification scheduling

---

## 🌟 Notes

- Firebase provides scalability and cloud sync from the start
- Offline mode can be supported via Firestore's offline persistence
- Analytics will provide valuable insights for future feature decisions
- Privacy and GDPR compliance must be considered if targeting EU users