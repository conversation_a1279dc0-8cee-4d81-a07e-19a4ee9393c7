# QR Code Testing Guide for BillMate

## 🎯 What's Been Implemented

Your BillMate app now has **dual-mode scanning**:
1. **OCR Mode** - Existing bill text scanning functionality
2. **QR Code Mode** - NEW real-time QR code scanning for bills

## 🔧 How to Test

### 1. **Access QR Code Mode**
- Open the scan screen in your app
- Look for two icons in the top-right corner:
  - 📝 Text icon = OCR mode
  - 📱 QR code icon = QR code mode
- Tap the QR code icon to switch to QR code scanning

### 2. **Test QR Codes**

#### **Simple Test QR Code (JSON format):**
Create a QR code with this content:
```json
{"title": "Electric Bill", "amount": 125.50, "dueDate": "2024-02-15", "accountNumber": "ACC123456"}
```

#### **Payment URL QR Code:**
Create a QR code with this content:
```
https://pay.example.com?title=Water%20Bill&amount=75.00&dueDate=2024-02-20
```

#### **Key-Value Format QR Code:**
Create a QR code with this content:
```
title=Gas Bill
amount=89.99
dueDate=2024-02-25
account=GAS789012
reference=REF456789
```

### 3. **How to Create Test QR Codes**

#### **Online QR Code Generators:**
- https://qr-code-generator.com/
- https://www.qr-code-generator.org/
- https://qrcode.tec-it.com/

#### **Steps:**
1. Go to any QR code generator website
2. Select "Text" or "Free Text" option
3. Copy and paste one of the test formats above
4. Generate the QR code
5. Display it on another device or print it

## 🔍 What to Expect

### **In QR Code Mode:**
1. **Visual Overlay**: You'll see a card overlay with QR code icon and "Point camera at QR code" text
2. **Real-time Scanning**: No need to tap capture - just point the camera at the QR code
3. **Instant Results**: When a QR code is detected, you'll immediately see the extracted information
4. **Information Display**: Shows:
   - QR code type (JSON, PAYMENT_URL, KEY_VALUE, etc.)
   - Title, amount, due date (if available)
   - Payment URL, account number, reference number (if available)
   - Raw data for debugging

### **Debug Information:**
Check Android Studio Logcat for debug messages:
- Filter by "ScanBillScreen" and "QrCodeProcessor"
- You should see logs like:
  - "Processing QR code image..."
  - "Found X barcodes"
  - "QR Code detected: format=X, value=Y"

## 🚨 Troubleshooting

### **If QR codes aren't being detected:**

1. **Check Camera Permissions**: Make sure camera permission is granted
2. **Lighting**: Ensure good lighting conditions
3. **Distance**: Hold the camera 6-12 inches from the QR code
4. **Focus**: Make sure the QR code is clear and in focus
5. **Size**: QR code should be large enough (at least 2cm x 2cm)

### **Check Debug Logs:**
1. Open Android Studio
2. Connect your device
3. Open Logcat
4. Filter by "ScanBillScreen" or "QrCodeProcessor"
5. Switch to QR code mode and point at a QR code
6. Look for processing messages

### **Common Issues:**
- **"No barcodes found"**: QR code might be too small, blurry, or poorly lit
- **"QR code detected but no result"**: The QR code format might not match our parsers
- **Camera not switching**: Try switching modes multiple times

## 📱 Testing Flow

1. **Launch BillMate app**
2. **Navigate to scan screen**
3. **Switch to QR code mode** (tap QR icon in top bar)
4. **Point camera at test QR code**
5. **Wait for detection** (should be instant)
6. **Review extracted information**
7. **Tap "Use Data"** to create a bill with the extracted info

## 🎉 Success Indicators

- ✅ Mode switching works (icons change color)
- ✅ QR code overlay appears in QR mode
- ✅ Real-time detection works (no capture button needed)
- ✅ Information is extracted and displayed correctly
- ✅ "Use Data" button passes information to bill creation

## 📋 Supported QR Code Formats

1. **JSON**: `{"title": "...", "amount": 123.45, ...}`
2. **Payment URLs**: `https://pay.example.com?amount=123&title=...`
3. **Key-Value Pairs**: `title=...\namount=...\n...`
4. **EPC QR Codes**: European payment standard
5. **Generic Text**: Any QR code will show raw data

The app intelligently parses different formats and extracts relevant bill information automatically!
