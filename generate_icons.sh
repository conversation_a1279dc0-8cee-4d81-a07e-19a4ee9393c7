#!/bin/bash

# Generate icons script for BillMate
# This script generates all required Android icon sizes from a source image

SOURCE_IMAGE="logo.png"
APP_RES_DIR="app/src/main/res"

# Check if source image exists
if [ ! -f "$SOURCE_IMAGE" ]; then
    echo "Error: Source image $SOURCE_IMAGE not found!"
    exit 1
fi

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "Error: ImageMagick is not installed. Please install it first."
    echo "On macOS: brew install imagemagick"
    echo "On Ubuntu: sudo apt-get install imagemagick"
    exit 1
fi

echo "Generating Android icons from $SOURCE_IMAGE..."

# Create directories if they don't exist
mkdir -p "$APP_RES_DIR/mipmap-mdpi"
mkdir -p "$APP_RES_DIR/mipmap-hdpi"
mkdir -p "$APP_RES_DIR/mipmap-xhdpi"
mkdir -p "$APP_RES_DIR/mipmap-xxhdpi"
mkdir -p "$APP_RES_DIR/mipmap-xxxhdpi"

# Remove old webp files
echo "Removing old webp icons..."
rm -f "$APP_RES_DIR/mipmap-mdpi/ic_launcher.webp"
rm -f "$APP_RES_DIR/mipmap-hdpi/ic_launcher.webp"
rm -f "$APP_RES_DIR/mipmap-xhdpi/ic_launcher.webp"
rm -f "$APP_RES_DIR/mipmap-xxhdpi/ic_launcher.webp"
rm -f "$APP_RES_DIR/mipmap-xxxhdpi/ic_launcher.webp"
rm -f "$APP_RES_DIR/mipmap-mdpi/ic_launcher_round.webp"
rm -f "$APP_RES_DIR/mipmap-hdpi/ic_launcher_round.webp"
rm -f "$APP_RES_DIR/mipmap-xhdpi/ic_launcher_round.webp"
rm -f "$APP_RES_DIR/mipmap-xxhdpi/ic_launcher_round.webp"
rm -f "$APP_RES_DIR/mipmap-xxxhdpi/ic_launcher_round.webp"

# Generate launcher icons (square) with proper background
echo "Generating launcher icons..."
convert "$SOURCE_IMAGE" -resize 48x48 -background white -gravity center -extent 48x48 "$APP_RES_DIR/mipmap-mdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 72x72 -background white -gravity center -extent 72x72 "$APP_RES_DIR/mipmap-hdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 96x96 -background white -gravity center -extent 96x96 "$APP_RES_DIR/mipmap-xhdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 144x144 -background white -gravity center -extent 144x144 "$APP_RES_DIR/mipmap-xxhdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 192x192 -background white -gravity center -extent 192x192 "$APP_RES_DIR/mipmap-xxxhdpi/ic_launcher.png"

# Generate round launcher icons with circular mask
echo "Generating round launcher icons..."
convert "$SOURCE_IMAGE" -resize 48x48 -background white -gravity center -extent 48x48 \( +clone -threshold 101% -fill white -draw 'circle 24,24 24,0' \) -alpha off -compose copy_opacity -composite "$APP_RES_DIR/mipmap-mdpi/ic_launcher_round.png"
convert "$SOURCE_IMAGE" -resize 72x72 -background white -gravity center -extent 72x72 \( +clone -threshold 101% -fill white -draw 'circle 36,36 36,0' \) -alpha off -compose copy_opacity -composite "$APP_RES_DIR/mipmap-hdpi/ic_launcher_round.png"
convert "$SOURCE_IMAGE" -resize 96x96 -background white -gravity center -extent 96x96 \( +clone -threshold 101% -fill white -draw 'circle 48,48 48,0' \) -alpha off -compose copy_opacity -composite "$APP_RES_DIR/mipmap-xhdpi/ic_launcher_round.png"
convert "$SOURCE_IMAGE" -resize 144x144 -background white -gravity center -extent 144x144 \( +clone -threshold 101% -fill white -draw 'circle 72,72 72,0' \) -alpha off -compose copy_opacity -composite "$APP_RES_DIR/mipmap-xxhdpi/ic_launcher_round.png"
convert "$SOURCE_IMAGE" -resize 192x192 -background white -gravity center -extent 192x192 \( +clone -threshold 101% -fill white -draw 'circle 96,96 96,0' \) -alpha off -compose copy_opacity -composite "$APP_RES_DIR/mipmap-xxxhdpi/ic_launcher_round.png"

# Generate notification icon (24dp, white/transparent)
echo "Generating notification icon..."
mkdir -p "$APP_RES_DIR/drawable-hdpi"
mkdir -p "$APP_RES_DIR/drawable-mdpi"
mkdir -p "$APP_RES_DIR/drawable-xhdpi"
mkdir -p "$APP_RES_DIR/drawable-xxhdpi"
mkdir -p "$APP_RES_DIR/drawable-xxxhdpi"

# Create simplified monochrome version for notifications
convert "$SOURCE_IMAGE" -resize 24x24 -colorspace Gray -threshold 50% -negate "$APP_RES_DIR/drawable-mdpi/ic_notification.png"
convert "$SOURCE_IMAGE" -resize 36x36 -colorspace Gray -threshold 50% -negate "$APP_RES_DIR/drawable-hdpi/ic_notification.png"
convert "$SOURCE_IMAGE" -resize 48x48 -colorspace Gray -threshold 50% -negate "$APP_RES_DIR/drawable-xhdpi/ic_notification.png"
convert "$SOURCE_IMAGE" -resize 72x72 -colorspace Gray -threshold 50% -negate "$APP_RES_DIR/drawable-xxhdpi/ic_notification.png"
convert "$SOURCE_IMAGE" -resize 96x96 -colorspace Gray -threshold 50% -negate "$APP_RES_DIR/drawable-xxxhdpi/ic_notification.png"

echo "Icons generated successfully!"
echo "Generated files:"
echo "- Launcher icons (square): mipmap-*/ic_launcher.png"
echo "- Launcher icons (round): mipmap-*/ic_launcher_round.png"
echo "- Notification icons: drawable-*/ic_notification.png"
echo ""
echo "Note: You may need to clean and rebuild your project for the changes to take effect."
